# PROMIS Landing Page Theme Design Guide

## Overview
This is a professional light theme design system created for the PROMIS SaaS landing page. It's based on the dakoii dark theme but adapted for a clean, modern, and professional appearance suitable for business audiences.

## Theme Characteristics
- **Style**: Professional Light Theme
- **Target**: SaaS Landing Page
- **Audience**: Business professionals and organizations
- **Approach**: Clean, modern, trustworthy design

## Color Palette

### Background Colors
```css
--bg-primary: #FFFFFF;        /* Pure white main background */
--bg-secondary: #F8FAFC;      /* Light gray secondary background */
--bg-tertiary: #F1F5F9;       /* Slightly darker gray for sections */
--bg-accent: #EFF6FF;         /* Light blue accent background */
```

### Surface Colors
```css
--surface-card: #FFFFFF;              /* White cards */
--surface-card-hover: #F8FAFC;        /* Light gray on hover */
--surface-overlay: rgba(255, 255, 255, 0.95);  /* Semi-transparent overlay */
```

### Professional Gradients
```css
--gradient-primary: linear-gradient(135deg, #3B82F6, #1D4ED8);    /* Blue gradient */
--gradient-secondary: linear-gradient(135deg, #10B981, #059669);   /* Green gradient */
--gradient-accent: linear-gradient(135deg, #8B5CF6, #7C3AED);     /* Purple gradient */
--gradient-hero: linear-gradient(135deg, #667EEA 0%, #764BA2 100%); /* Hero section */
```

### Text Colors
```css
--text-primary: #1E293B;      /* Dark gray for main text */
--text-secondary: #475569;    /* Medium gray for secondary text */
--text-tertiary: #64748B;     /* Light gray for tertiary text */
--text-muted: #94A3B8;        /* Very light gray for muted text */
--text-white: #FFFFFF;        /* White text for dark backgrounds */
```

### Brand Colors
```css
--brand-primary: #3B82F6;     /* Primary blue */
--brand-secondary: #10B981;   /* Secondary green */
--brand-accent: #8B5CF6;      /* Accent purple */
```

## Typography

### Font Family
- **Primary**: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif
- **Characteristics**: Modern, clean, highly readable

### Font Sizes
```css
--spacing-xs: 0.25rem;    /* 4px */
--spacing-sm: 0.5rem;     /* 8px */
--spacing-md: 1rem;       /* 16px */
--spacing-lg: 1.5rem;     /* 24px */
--spacing-xl: 2rem;       /* 32px */
--spacing-2xl: 3rem;      /* 48px */
--spacing-3xl: 4rem;      /* 64px */
--spacing-4xl: 5rem;      /* 80px */
```

## Layout Components

### Navigation
- **Position**: Fixed top navigation
- **Background**: Semi-transparent white with backdrop blur
- **Height**: 70px
- **Logo**: Gradient text effect
- **Links**: Smooth hover effects with underline animation

### Hero Section
- **Background**: Gradient background (#667EEA to #764BA2)
- **Layout**: Two-column grid with content and visual
- **Text**: White text on gradient background
- **CTA**: Prominent buttons with hover effects

### Feature Cards
- **Layout**: 3-column grid (responsive)
- **Style**: White cards with subtle shadows
- **Icons**: Gradient backgrounds with white icons
- **Hover**: Lift effect with enhanced shadow

### Pricing Cards
- **Layout**: 3-column grid
- **Highlight**: Middle card with border and "Most Popular" badge
- **Style**: Clean white cards with clear pricing
- **CTA**: Different button styles for each tier

### Contact Section
- **Layout**: Two-column grid
- **Form**: Clean form styling with proper validation
- **Info**: Contact information with gradient icons
- **CTA**: Additional call-to-action card

### Footer
- **Background**: Dark background (#1E293B)
- **Layout**: 4-column grid with company info, links
- **Social**: Social media icons
- **Copyright**: Centered copyright notice

## Interactive Elements

### Buttons
- **Primary**: Gradient background with hover lift effect
- **Secondary**: Outline style with fill on hover
- **Sizes**: Consistent padding and border radius

### Cards
- **Base**: White background with subtle border
- **Hover**: Lift effect with enhanced shadow
- **Border Radius**: Consistent rounded corners

### Forms
- **Inputs**: Clean styling with focus states
- **Labels**: Clear typography hierarchy
- **Validation**: Proper error handling

## Responsive Design

### Breakpoints
- **Mobile**: 0px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

### Mobile Adaptations
- **Navigation**: Simplified mobile menu
- **Grid**: Single column layouts
- **Spacing**: Reduced padding and margins
- **Typography**: Adjusted font sizes

## Animations

### Scroll Effects
- **Navbar**: Background opacity change on scroll
- **Sections**: Fade-in animations on scroll
- **Links**: Smooth scrolling between sections

### Hover Effects
- **Cards**: Lift and shadow enhancement
- **Buttons**: Scale and color transitions
- **Links**: Underline animations

## Accessibility

### Focus States
- **Outline**: Clear focus indicators
- **Color**: High contrast focus colors
- **Navigation**: Keyboard navigation support

### Color Contrast
- **Text**: Meets WCAG AA standards
- **Interactive**: Proper contrast ratios
- **Gradients**: Readable text overlay

## Usage Guidelines

### When to Use
- SaaS landing pages
- Professional business websites
- Product marketing pages
- Corporate presentations

### Brand Consistency
- Use consistent spacing throughout
- Maintain color palette integrity
- Follow typography hierarchy
- Ensure responsive behavior

### Performance
- Optimized CSS variables
- Minimal external dependencies
- Efficient animations
- Fast loading times

## Implementation Notes

### CSS Variables
All colors and spacing use CSS custom properties for easy maintenance and theming.

### Responsive Grid
Uses CSS Grid with auto-fit for responsive layouts without media queries where possible.

### Modern Features
- Backdrop filters for glass effects
- CSS gradients for visual appeal
- Smooth animations and transitions
- Semantic HTML structure

This theme provides a professional, trustworthy appearance perfect for SaaS products while maintaining excellent usability and accessibility standards.
