<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/provinces') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Provinces
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<?php if (isset($province)): ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
            <span style="font-size: 3rem;">🏞️</span>
            <div>
                <h1 style="color: var(--text-primary); margin: 0; font-size: 2.5rem;">
                    <?= esc($province['name']) ?>
                </h1>
                <p style="color: var(--text-secondary); margin: var(--spacing-xs) 0 0 0; font-size: 1.1rem;">
                    Province Profile • Code: <?= esc($province['prov_code']) ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Province Details -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        
        <!-- Main Information -->
        <div class="card">
            <div class="card-header">
                <h2 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                    📋 Province Information
                </h2>
            </div>
            
            <div style="padding: var(--spacing-lg);">
                <div class="info-grid">
                    <div class="info-item">
                        <label class="info-label">Province Name</label>
                        <div class="info-value"><?= esc($province['name']) ?></div>
                    </div>

                    <div class="info-item">
                        <label class="info-label">Province Code</label>
                        <div class="info-value"><?= esc($province['prov_code']) ?></div>
                    </div>

                    <div class="info-item">
                        <label class="info-label">Country ID</label>
                        <div class="info-value"><?= esc($province['country_id']) ?></div>
                    </div>

                    <?php if (!empty($province['map_centre_gps'])): ?>
                    <div class="info-item">
                        <label class="info-label">Map Center GPS</label>
                        <div class="info-value"><?= esc($province['map_centre_gps']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($province['map_zoom'])): ?>
                    <div class="info-item">
                        <label class="info-label">Map Zoom Level</label>
                        <div class="info-value"><?= esc($province['map_zoom']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($province['geojson_id'])): ?>
                    <div class="info-item">
                        <label class="info-label">GeoJSON ID</label>
                        <div class="info-value"><?= esc($province['geojson_id']) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($province['created_at'])): ?>
                    <div class="info-item">
                        <label class="info-label">Created Date</label>
                        <div class="info-value"><?= date('F j, Y g:i A', strtotime($province['created_at'])) ?></div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($province['updated_at'])): ?>
                    <div class="info-item">
                        <label class="info-label">Last Updated</label>
                        <div class="info-value"><?= date('F j, Y g:i A', strtotime($province['updated_at'])) ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Actions & Quick Stats -->
        <div style="display: flex; flex-direction: column; gap: var(--spacing-lg);">
            <!-- Actions Card -->
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0;">⚡ Quick Actions</h3>
                </div>
                <div style="padding: var(--spacing-lg);">
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                        <a href="<?= base_url('dakoii/government/provinces/'.$province['id'].'/edit') ?>" class="btn btn-primary btn-full">
                            <i class="icon">✏️</i> Edit Province
                        </a>
                        
                        <a href="<?= base_url('dakoii/government/districts?province_id='.$province['id']) ?>" class="btn btn-outline btn-full">
                            <i class="icon">🏘️</i> View Districts
                        </a>
                        
                        <button type="button" class="btn btn-danger btn-full" onclick="deleteProvince(<?= $province['id'] ?>)">
                            <i class="icon">🗑️</i> Delete Province
                        </button>
                    </div>
                </div>
            </div>

            <!-- Status Card -->
            <div class="info-card glass-effect">
                <div class="info-header">
                    <h3>📊 Status Information</h3>
                </div>
                <div class="info-content">
                    <div style="text-align: center; padding: var(--spacing-md);">
                        <div style="display: inline-block; padding: var(--spacing-sm) var(--spacing-md); background: rgba(40, 167, 69, 0.2); border: 1px solid rgba(40, 167, 69, 0.4); border-radius: var(--radius-full); color: #28a745; font-weight: 500; margin-bottom: var(--spacing-md);">
                            ✅ Active Province
                        </div>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                            This province is currently active in the system and available for operations.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="info-card glass-effect">
                <div class="info-header">
                    <h3>💡 Quick Help</h3>
                </div>
                <div class="info-content">
                    <div style="font-size: 0.9rem; color: var(--text-secondary);">
                        <p style="margin: 0 0 var(--spacing-sm) 0;">
                            <strong>Edit:</strong> Update province information
                        </p>
                        <p style="margin: 0 0 var(--spacing-sm) 0;">
                            <strong>Districts:</strong> View child districts
                        </p>
                        <p style="margin: 0;">
                            <strong>Delete:</strong> Remove from system (with confirmation)
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form (Hidden) -->
<form id="deleteForm" action="<?= base_url('dakoii/government/provinces/'.$province['id'].'/delete') ?>" method="post" style="display: none;">
    <?= csrf_field() ?>
</form>

<script>
function deleteProvince(provinceId) {
    if (confirm('Are you sure you want to delete this province?\n\nThis action cannot be undone and may affect related districts and LLGs.')) {
        document.getElementById('deleteForm').submit();
    }
}
</script>

<style>
.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    min-height: 20px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
}

.btn-full {
    width: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.btn-outline {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-danger {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.4);
}

.btn-danger:hover {
    background: rgba(220, 53, 69, 0.3);
    transform: translateY(-1px);
}

.info-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.info-header {
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.info-content {
    padding: var(--spacing-lg);
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?php else: ?>
<div class="fade-in">
    <div style="text-align: center; padding: var(--spacing-xl);">
        <div style="font-size: 4rem; margin-bottom: var(--spacing-lg);">❌</div>
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Province Not Found</h1>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            The requested province could not be found in the system.
        </p>
        <a href="<?= base_url('dakoii/government/provinces') ?>" class="btn btn-primary">
            <i class="icon">←</i> Back to Provinces
        </a>
    </div>
</div>
<?php endif; ?>
<?= $this->endSection() ?> 