<?php

namespace App\Helpers;

/**
 * Helper class for extracting data from map JSON files
 */
class MapJsonHelper
{
    /**
     * Get province options from PNG province boundaries JSON
     */
    public static function getProvinceOptions(): array
    {
        $jsonPath = FCPATH . 'map_jsons/png_prov_boundaries_2011.json';
        
        if (!file_exists($jsonPath)) {
            return [];
        }
        
        $jsonData = json_decode(file_get_contents($jsonPath), true);
        
        if (!isset($jsonData['features'])) {
            return [];
        }
        
        $options = [];
        foreach ($jsonData['features'] as $feature) {
            if (isset($feature['properties']['PROVID']) && isset($feature['properties']['PROVNAME'])) {
                $options[] = [
                    'id' => $feature['properties']['PROVID'],
                    'name' => $feature['properties']['PROVNAME']
                ];
            }
        }
        
        // Sort by name
        usort($options, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        return $options;
    }
    
    /**
     * Get district options from PNG district boundaries JSON
     */
    public static function getDistrictOptions(): array
    {
        $jsonPath = FCPATH . 'map_jsons/png_dist_boundaries_2011.json';
        
        if (!file_exists($jsonPath)) {
            return [];
        }
        
        $jsonData = json_decode(file_get_contents($jsonPath), true);
        
        if (!isset($jsonData['features'])) {
            return [];
        }
        
        $options = [];
        foreach ($jsonData['features'] as $feature) {
            if (isset($feature['properties']['GEOCODE']) && isset($feature['properties']['DISTNAME'])) {
                $options[] = [
                    'id' => $feature['properties']['GEOCODE'],
                    'name' => $feature['properties']['DISTNAME']
                ];
            }
        }
        
        // Sort by name
        usort($options, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        return $options;
    }
    
    /**
     * Get LLG options from PNG LLG boundaries JSON
     */
    public static function getLlgOptions(): array
    {
        $jsonPath = FCPATH . 'map_jsons/png_llg_boundaries_2011.json';
        
        if (!file_exists($jsonPath)) {
            return [];
        }
        
        $jsonData = json_decode(file_get_contents($jsonPath), true);
        
        if (!isset($jsonData['features'])) {
            return [];
        }
        
        $options = [];
        foreach ($jsonData['features'] as $feature) {
            if (isset($feature['properties']['GEOCODE']) && isset($feature['properties']['LLGNAME'])) {
                $options[] = [
                    'id' => $feature['properties']['GEOCODE'],
                    'name' => $feature['properties']['LLGNAME']
                ];
            }
        }
        
        // Sort by name
        usort($options, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        return $options;
    }
    
    /**
     * Get all map JSON options for a specific type
     */
    public static function getMapJsonOptions(string $type): array
    {
        switch ($type) {
            case 'province':
                return self::getProvinceOptions();
            case 'district':
                return self::getDistrictOptions();
            case 'llg':
                return self::getLlgOptions();
            default:
                return [];
        }
    }
}
