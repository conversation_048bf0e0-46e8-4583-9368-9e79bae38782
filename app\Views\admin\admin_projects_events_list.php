<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-events" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/events/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-plus-circle me-2"></i>
    Add New Event
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-calendar-event me-2"></i>
            Project Events
        </h1>
        <p class="text-muted mb-0">
            Events and incidents for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Event Statistics -->
<div class="row g-4 mb-4">
    <!-- Total Events -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-calendar-event text-primary"></i>
                </div>
                <div class="h4 fw-bold text-primary mb-2">
                    <?= $eventStats['total'] ?>
                </div>
                <div class="text-muted small">Total Events</div>
            </div>
        </div>
    </div>

    <!-- Active Events -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-exclamation-circle text-warning"></i>
                </div>
                <div class="h4 fw-bold text-warning mb-2">
                    <?= $eventStats['active'] ?>
                </div>
                <div class="text-muted small">Active</div>
            </div>
        </div>
    </div>

    <!-- Resolved Events -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-check-circle text-success"></i>
                </div>
                <div class="h4 fw-bold text-success mb-2">
                    <?= $eventStats['resolved'] ?>
                </div>
                <div class="text-muted small">Resolved</div>
            </div>
        </div>
    </div>

    <!-- Monitoring Events -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-eye text-info"></i>
                </div>
                <div class="h4 fw-bold text-info mb-2">
                    <?= $eventStats['monitoring'] ?>
                </div>
                <div class="text-muted small">Monitoring</div>
            </div>
        </div>
    </div>

    <!-- Critical Events -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-exclamation-triangle text-danger"></i>
                </div>
                <div class="h4 fw-bold text-danger mb-2">
                    <?= $eventStats['critical'] ?>
                </div>
                <div class="text-muted small">Critical</div>
            </div>
        </div>
    </div>

    <!-- High Priority Events -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-arrow-up-circle text-warning"></i>
                </div>
                <div class="h4 fw-bold text-warning mb-2">
                    <?= $eventStats['high'] ?>
                </div>
                <div class="text-muted small">High Priority</div>
            </div>
        </div>
    </div>
</div>

<!-- Events List -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-list me-2"></i>
            Events List
        </h5>
    </div>

    <?php if (!empty($events)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Event</th>
                        <th>Type & Severity</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Impact</th>
                        <th>Milestone</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($events as $event): ?>
                        <tr>
                            <td>
                                <div class="fw-semibold text-primary mb-1">
                                    <?= esc($event['title']) ?>
                                </div>
                                <div class="text-muted small">
                                    <?= esc(substr($event['description'], 0, 100)) ?><?= strlen($event['description']) > 100 ? '...' : '' ?>
                                </div>
                            </td>
                            <td>
                                <div class="mb-1">
                                    <?php
                                    $typeIcons = [
                                        'delay' => 'clock',
                                        'suspension' => 'pause-circle',
                                        'resumption' => 'play-circle',
                                        'incident' => 'exclamation-triangle',
                                        'natural_disaster' => 'cloud-lightning',
                                        'funding_issue' => 'currency-dollar',
                                        'resource_issue' => 'tools',
                                        'stakeholder_issue' => 'people',
                                        'other' => 'question-circle'
                                    ];
                                    $icon = $typeIcons[$event['event_type']] ?? 'question-circle';
                                    ?>
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-<?= $icon ?> me-1"></i>
                                        <?= esc(ucfirst(str_replace('_', ' ', $event['event_type']))) ?>
                                    </span>
                                </div>
                                <div>
                                    <?php
                                    $severityClasses = [
                                        'low' => 'bg-success',
                                        'medium' => 'bg-warning',
                                        'high' => 'bg-danger',
                                        'critical' => 'bg-dark'
                                    ];
                                    $severityClass = $severityClasses[$event['severity']] ?? 'bg-secondary';
                                    ?>
                                    <span class="badge <?= $severityClass ?> text-uppercase">
                                        <?= esc($event['severity']) ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="fw-semibold">
                                    <?= date('M j, Y', strtotime($event['event_date'])) ?>
                                </div>
                                <div class="text-muted small">
                                    <?= date('g:i A', strtotime($event['event_date'])) ?>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'active' => 'bg-warning',
                                    'resolved' => 'bg-success',
                                    'monitoring' => 'bg-info'
                                ];
                                $statusClass = $statusClasses[$event['status']] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?= $statusClass ?> text-uppercase">
                                    <?= esc($event['status']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($event['impact_days']): ?>
                                    <div class="fw-semibold text-danger">
                                        <?= $event['impact_days'] ?> days
                                    </div>
                                <?php endif; ?>
                                <?php if ($event['impact_description']): ?>
                                    <div class="text-muted small">
                                        <?= esc(substr($event['impact_description'], 0, 50)) ?><?= strlen($event['impact_description']) > 50 ? '...' : '' ?>
                                    </div>
                                <?php endif; ?>
                                <?php if (!$event['impact_days'] && !$event['impact_description']): ?>
                                    <span class="text-muted">No impact recorded</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($event['milestone_title']): ?>
                                    <div class="fw-semibold">
                                        <?= esc($event['milestone_code']) ?>
                                    </div>
                                    <div class="text-muted small">
                                        <?= esc($event['milestone_title']) ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No milestone</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <div class="d-flex gap-2 justify-content-center">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/events/' . $event['id']) ?>"
                                       class="btn btn-outline-primary btn-sm" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/events/' . $event['id'] . '/edit') ?>"
                                       class="btn btn-outline-secondary btn-sm" title="Edit Event">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <?php if ($event['status'] !== 'resolved'): ?>
                                        <button onclick="showResolveModal(<?= $event['id'] ?>, '<?= esc($event['title']) ?>')"
                                                class="btn btn-outline-success btn-sm" title="Resolve Event">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button onclick="showDeleteModal(<?= $event['id'] ?>, '<?= esc($event['title']) ?>')"
                                            class="btn btn-outline-danger btn-sm" title="Delete Event">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="card-body text-center py-5">
            <div class="display-1 text-muted opacity-50 mb-4">
                <i class="bi bi-calendar-event"></i>
            </div>
            <h5 class="text-muted mb-3">No events found</h5>
            <p>No events have been recorded for this project yet.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/events/create') ?>" class="btn btn-primary promis-btn-gradient">
                <i class="bi bi-plus-circle me-2"></i>Add First Event
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Resolve Event Modal -->
<div class="modal fade" id="resolveModal" tabindex="-1" aria-labelledby="resolveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resolveModalLabel">Resolve Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="resolveForm" method="post">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <p>Are you sure you want to resolve the event <strong id="resolveEventName"></strong>?</p>
                    <div class="mb-3">
                        <label for="resolution_description" class="form-label fw-semibold">Resolution Description <span class="text-danger">*</span></label>
                        <textarea name="resolution_description" id="resolution_description" class="form-control border-danger" rows="3" required placeholder="Describe how this event was resolved..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Resolve Event</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Event Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the event <strong id="deleteEventName"></strong>?</p>
                <p class="text-danger small">This action cannot be undone. All event data will be permanently removed.</p>
            </div>
            <div class="modal-footer">
                <form id="deleteForm" method="post">
                    <?= csrf_field() ?>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Event</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function showResolveModal(eventId, eventName) {
    document.getElementById('resolveEventName').textContent = eventName;
    document.getElementById('resolveForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/events/') ?>' + eventId + '/resolve';
    new bootstrap.Modal(document.getElementById('resolveModal')).show();
}

function showDeleteModal(eventId, eventName) {
    document.getElementById('deleteEventName').textContent = eventName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/events/') ?>' + eventId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
