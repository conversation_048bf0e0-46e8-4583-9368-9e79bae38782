<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;
use App\Models\ProjectModel;

/**
 * Admin Search Controller
 * 
 * Handles global search functionality for the PROMIS Admin Portal including:
 * - Cross-module search
 * - Saved searches
 * - Search result display
 * - Advanced filtering
 */
class AdminSearchController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    protected $projectModel;
    protected $db;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
        $this->projectModel = new ProjectModel();
        $this->db = \Config\Database::connect();
    }

    /**
     * Global search - GET request
     */
    public function search()
    {
        $query = $this->request->getGet('q');
        $module = $this->request->getGet('module');
        $results = [];
        $stats = [];

        if (!empty($query)) {
            // Search across different modules
            $results = [
                'users' => $this->searchUsers($query),
                'organizations' => $this->searchOrganizations($query),
                'projects' => $this->searchProjects($query)
            ];

            // Calculate statistics
            $stats = [
                'total_results' => array_sum(array_map('count', $results)),
                'users_count' => count($results['users']),
                'organizations_count' => count($results['organizations']),
                'projects_count' => count($results['projects'])
            ];

            // Log search activity
            $this->logAuditEvent('search', session()->get('admin_user_id'), 'search', 0, [
                'description' => 'Global search performed: ' . $query,
                'search_query' => $query,
                'results_count' => $stats['total_results']
            ]);
        }

        $data = [
            'title' => 'Global Search - PROMIS Admin',
            'page_title' => 'Global Search',
            'query' => $query,
            'module' => $module,
            'results' => $results,
            'stats' => $stats
        ];

        return view('admin/admin_search_results', $data);
    }

    /**
     * Save search - POST request
     */
    public function saveSearch()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token');
        }

        $searchName = $this->request->getPost('search_name');
        $searchQuery = $this->request->getPost('search_query');
        $searchModule = $this->request->getPost('search_module');

        if (empty($searchName) || empty($searchQuery)) {
            return redirect()->back()->with('error', 'Search name and query are required');
        }

        // Store in session for now (in a real implementation, you'd store in database)
        $savedSearches = session()->get('admin_saved_searches') ?: [];
        $savedSearches[] = [
            'id' => uniqid(),
            'name' => $searchName,
            'query' => $searchQuery,
            'module' => $searchModule,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('admin_user_id')
        ];

        session()->set('admin_saved_searches', $savedSearches);

        return redirect()->back()->with('success', 'Search saved successfully');
    }

    /**
     * Load saved search - GET request
     */
    public function loadSavedSearch($searchId)
    {
        $savedSearches = session()->get('admin_saved_searches') ?: [];
        $search = null;

        foreach ($savedSearches as $savedSearch) {
            if ($savedSearch['id'] === $searchId) {
                $search = $savedSearch;
                break;
            }
        }

        if (!$search) {
            return redirect()->to(base_url('admin/search'))->with('error', 'Saved search not found');
        }

        // Redirect to search with saved parameters
        $params = [
            'q' => $search['query'],
            'module' => $search['module']
        ];

        return redirect()->to(base_url('admin/search') . '?' . http_build_query($params))
                        ->with('success', 'Loaded saved search: ' . $search['name']);
    }

    /**
     * Delete saved search - DELETE request
     */
    public function deleteSavedSearch($searchId)
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token');
        }

        $savedSearches = session()->get('admin_saved_searches') ?: [];
        $updatedSearches = [];

        foreach ($savedSearches as $savedSearch) {
            if ($savedSearch['id'] !== $searchId) {
                $updatedSearches[] = $savedSearch;
            }
        }

        session()->set('admin_saved_searches', $updatedSearches);

        return redirect()->back()->with('success', 'Saved search deleted successfully');
    }

    /**
     * Search users
     */
    private function searchUsers($query)
    {
        return $this->userModel->select('users.*, organizations.name as org_name')
                              ->join('organizations', 'organizations.id = users.organization_id', 'left')
                              ->where('users.deleted_at', null)
                              ->groupStart()
                              ->like('users.name', $query)
                              ->orLike('users.username', $query)
                              ->orLike('users.email', $query)
                              ->groupEnd()
                              ->limit(10)
                              ->findAll();
    }

    /**
     * Search organizations
     */
    private function searchOrganizations($query)
    {
        return $this->organizationModel->where('deleted_at', null)
                                      ->groupStart()
                                      ->like('name', $query)
                                      ->orLike('org_code', $query)
                                      ->orLike('description', $query)
                                      ->groupEnd()
                                      ->limit(10)
                                      ->findAll();
    }

    /**
     * Search projects
     */
    private function searchProjects($query)
    {
        return $this->projectModel->select('projects.*, organizations.name as org_name')
                                 ->join('organizations', 'organizations.id = projects.org_id', 'left')
                                 ->where('projects.deleted_at', null)
                                 ->groupStart()
                                 ->like('projects.title', $query)
                                 ->orLike('projects.pro_code', $query)
                                 ->orLike('projects.description', $query)
                                 ->groupEnd()
                                 ->limit(10)
                                 ->findAll();
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $primaryKey, $data = [])
    {
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => (string)$primaryKey,
            'action' => $action,
            'new_data' => json_encode($data),
            'user_id' => $userId,
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'search',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'session_id' => session()->session_id,
            'request_url' => current_url(),
            'description' => $data['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->db->table('audit_logs')->insert($auditData);
    }
}
