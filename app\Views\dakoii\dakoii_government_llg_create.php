<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to LLGs
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🏛️</span>
            Add New Local Level Government
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Create a new LLG entry in the government structure hierarchy.
        </p>
    </div>

    <!-- Create LLG Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">LLG Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Enter the basic information for the new Local Level Government.</p>

            <form method="POST" action="<?= base_url('dakoii/government/llgs/create') ?>" class="llg-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="district_id" class="form-label">District *</label>
                        <select id="district_id" name="district_id" class="form-input" required
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select District</option>
                            <?php if (isset($districts) && !empty($districts)): ?>
                                <?php foreach ($districts as $district): ?>
                                    <option value="<?= $district['id'] ?>" <?= (old('district_id') == $district['id']) ? 'selected' : '' ?>
                                            style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">
                                        <?= esc($district['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Select the district this LLG belongs to
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="name" class="form-label">LLG Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               value="<?= old('name') ?>" required
                               placeholder="Enter the full LLG name">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Official name of the Local Level Government as it should appear in the system
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="llg_code" class="form-label">LLG Code *</label>
                        <input type="text" id="llg_code" name="llg_code" class="form-input"
                               value="<?= old('llg_code') ?>" required
                               placeholder="e.g., DARU, KIWAI, PMNE"
                               style="text-transform: uppercase;">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Short code or abbreviation for the LLG
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="llg_type" class="form-label">LLG Type</label>
                        <select id="llg_type" name="llg_type" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select LLG Type (Optional)</option>
                            <option value="urban" <?= (old('llg_type') == 'urban') ? 'selected' : '' ?>
                                    style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">Urban LLG</option>
                            <option value="rural" <?= (old('llg_type') == 'rural') ? 'selected' : '' ?>
                                    style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">Rural LLG</option>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Classification of the Local Level Government (Urban or Rural)
                        </small>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>

                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <select id="geojson_id" name="geojson_id" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select LLG Boundary (Optional)</option>
                            <?php
                            // Load LLG options from JSON
                            $jsonPath = FCPATH . 'map_jsons/png_llg_boundaries_2011.json';
                            if (file_exists($jsonPath)) {
                                $jsonData = json_decode(file_get_contents($jsonPath), true);
                                if (isset($jsonData['features'])) {
                                    $llgs = [];
                                    foreach ($jsonData['features'] as $feature) {
                                        if (isset($feature['properties']['GEOCODE']) && isset($feature['properties']['LLGNAME'])) {
                                            $llgs[] = [
                                                'id' => $feature['properties']['GEOCODE'],
                                                'name' => $feature['properties']['LLGNAME']
                                            ];
                                        }
                                    }
                                    // Sort by name
                                    usort($llgs, function($a, $b) {
                                        return strcmp($a['name'], $b['name']);
                                    });

                                    foreach ($llgs as $llg) {
                                        $selected = (old('geojson_id') == $llg['id']) ? 'selected' : '';
                                        echo '<option value="' . esc($llg['id']) . '" ' . $selected . ' style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">' . esc($llg['name']) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Select the corresponding boundary from map data
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input"
                                   value="<?= old('map_centre_gps') ?>"
                                   placeholder="e.g., -6.314993,143.95555">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Latitude, Longitude coordinates
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input"
                                   value="<?= old('map_zoom', '12') ?>" min="1" max="20">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Default zoom level for map display (1-20)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Create LLG
                    </button>
                    <a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Help Information -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>LLG Creation Guide</h3>
            </div>
            <div class="info-content">
                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Required Fields</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);">District: Parent district</li>
                        <li style="margin-bottom: var(--spacing-xs);">LLG Name: Official name</li>
                        <li>LLG Code: Short abbreviation</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">LLG Types</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);"><strong>Urban:</strong> City or town councils</li>
                        <li style="margin-bottom: var(--spacing-xs);"><strong>Rural:</strong> Rural area governments</li>
                        <li><strong>Special:</strong> Special administrative areas</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">LLG Code Examples</h4>
                    <div style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                        <div>Daru Urban LLG: DARU</div>
                        <div>Kiwai Rural LLG: KIWAI</div>
                        <div>Port Moresby North East: PMNE</div>
                        <div>Port Moresby South: PMS</div>
                    </div>
                </div>

                <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                    <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                        <div style="font-size: 1.25rem;">💡</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">Tip</h4>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                                LLGs are the lowest level of government administration. They handle local community services and governance.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Force dropdown option styling */
#district_id option, #llg_type option, #geojson_id option {
    background: #1a1a2e !important;
    color: #ffffff !important;
    padding: 8px 12px !important;
}

#district_id option:hover, #llg_type option:hover, #geojson_id option:hover {
    background: #16213e !important;
    color: #ffffff !important;
}

#district_id option:selected, #llg_type option:selected, #geojson_id option:selected,
#district_id option:checked, #llg_type option:checked, #geojson_id option:checked {
    background: #00D4FF !important;
    color: #1a1a2e !important;
    font-weight: 600 !important;
}

/* Ensure select elements have proper styling */
#district_id, #llg_type, #geojson_id {
    background: rgba(26, 26, 46, 0.8) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}
</style>

<script>
// Auto-uppercase LLG code
document.getElementById('llg_code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});

// Form validation
document.querySelector('.llg-form').addEventListener('submit', function(e) {
    const districtId = document.getElementById('district_id').value;
    const name = document.getElementById('name').value.trim();
    const llgCode = document.getElementById('llg_code').value.trim();

    if (!districtId) {
        e.preventDefault();
        alert('Please select a district.');
        return false;
    }

    if (name.length < 2) {
        e.preventDefault();
        alert('LLG name must be at least 2 characters long.');
        return false;
    }

    if (llgCode.length < 2) {
        e.preventDefault();
        alert('LLG code must be at least 2 characters long.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>