 Server: 127.0.0.1
 Database: promis_two_db
Structure Structure
SQL SQL
Search Search
Query Query
Export Export
Import Import
Operations Operations
Privileges Privileges
Routines Routines
Events Events
Triggers Triggers
Designer <PERSON>
Click on the bar to scroll to top of page
SQL Query Console Console
ascendingdescendingOrder:Debug SQLExecution orderTime takenOrder by:Group queries
Some error occurred while getting SQL debug info.
OptionsSet default
Always expand query messages
Show query history at start
Show current browsing query
 Execute queries on Enter and insert new line with Shift+Enter. To make this permanent, view settings.
Switch to dark theme
Showing create queries
Tables
Table	Create table
audit_logs	CREATE TABLE `audit_logs` (
 `id` bigint(20) NOT NULL AUTO_INCREMENT,
 `table_name` varchar(100) NOT NULL COMMENT 'Name of the table that was modified',
 `primary_key` varchar(64) NOT NULL COMMENT 'Primary key value of the affected record',
 `action` enum('create','update','delete','login','logout','access') NOT NULL COMMENT 'Type of operation performed',
 `old_data` mediumtext DEFAULT NULL COMMENT 'JSON of data before change (for updates/deletes)',
 `new_data` mediumtext DEFAULT NULL COMMENT 'JSON of data after change (for creates/updates)',
 `user_id` bigint(20) DEFAULT NULL COMMENT 'ID of the user who performed the action',
 `username` varchar(50) DEFAULT NULL COMMENT 'Username of the user',
 `user_type` varchar(20) DEFAULT NULL COMMENT 'Type of user (dakoii_user, admin_user, project_officer, etc.)',
 `user_full_name` varchar(100) DEFAULT NULL COMMENT 'Full name of the user',
 `organization_id` bigint(20) DEFAULT NULL COMMENT 'ID of the organization the user belongs to',
 `organization_name` varchar(100) DEFAULT NULL COMMENT 'Name of the organization',
 `organization_type` varchar(50) DEFAULT NULL COMMENT 'Type of organization (NGO, Government, etc.)',
 `project_id` bigint(20) DEFAULT NULL COMMENT 'ID of the project if action is project-related',
 `project_title` varchar(200) DEFAULT NULL COMMENT 'Title of the project',
 `portal` enum('dakoii','admin','monitor') NOT NULL COMMENT 'Portal where the action was performed',
 `module` varchar(50) DEFAULT NULL COMMENT 'Module/section within the portal',
 `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the user',
 `user_agent` text DEFAULT NULL COMMENT 'Browser/client user agent string',
 `session_id` varchar(128) DEFAULT NULL COMMENT 'Session ID',
 `request_url` varchar(255) DEFAULT NULL COMMENT 'URL where the action was performed',
 `description` text DEFAULT NULL COMMENT 'Human-readable description of the action',
 `created_at` datetime DEFAULT NULL COMMENT 'When the audit log was created',
 PRIMARY KEY (`id`),
 KEY `table_name` (`table_name`),
 KEY `user_id` (`user_id`),
 KEY `portal` (`portal`),
 KEY `action` (`action`),
 KEY `created_at` (`created_at`),
 KEY `user_type` (`user_type`),
 KEY `module` (`module`),
 KEY `organization_id` (`organization_id`),
 KEY `project_id` (`project_id`),
 KEY `portal_organization_id` (`portal`,`organization_id`),
 KEY `portal_user_id` (`portal`,`user_id`),
 KEY `organization_id_user_id` (`organization_id`,`user_id`),
 KEY `project_id_action` (`project_id`,`action`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
countries	CREATE TABLE `countries` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `iso2` char(2) NOT NULL,
 `iso3` char(3) NOT NULL,
 `name` varchar(100) NOT NULL,
 `map_zoom` tinyint(3) unsigned DEFAULT NULL,
 `map_centre_gps` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `iso2` (`iso2`),
 UNIQUE KEY `iso3` (`iso3`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
dakoii_users	CREATE TABLE `dakoii_users` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `user_code` varchar(12) NOT NULL,
 `username` varchar(50) NOT NULL,
 `email` varchar(100) NOT NULL,
 `name` varchar(100) NOT NULL,
 `role` enum('admin','moderator','user') NOT NULL DEFAULT 'user',
 `id_photo_path` varchar(255) DEFAULT NULL,
 `password_hash` varchar(255) NOT NULL,
 `activation_token` char(64) DEFAULT NULL,
 `is_activated` tinyint(1) NOT NULL DEFAULT 0,
 `password_reset_token` char(64) DEFAULT NULL,
 `last_login_at` datetime DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `user_code` (`user_code`),
 UNIQUE KEY `username` (`username`),
 UNIQUE KEY `email` (`email`),
 KEY `idx_email` (`email`),
 KEY `idx_activation` (`activation_token`),
 KEY `idx_password_reset` (`password_reset_token`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
districts	CREATE TABLE `districts` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `province_id` bigint(20) unsigned NOT NULL,
 `dist_code` varchar(10) NOT NULL,
 `name` varchar(100) NOT NULL,
 `geojson_id` varchar(50) DEFAULT NULL COMMENT 'ID of the feature in the GeoJSON boundary layer',
 `map_zoom` tinyint(3) unsigned DEFAULT NULL,
 `map_centre_gps` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_province_code` (`province_id`,`dist_code`),
 KEY `idx_province` (`province_id`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
llgs	CREATE TABLE `llgs` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `district_id` bigint(20) unsigned NOT NULL,
 `llg_code` varchar(10) NOT NULL,
 `name` varchar(100) NOT NULL,
 `llg_type` enum('urban','rural') NOT NULL DEFAULT 'rural',
 `geojson_id` varchar(50) DEFAULT NULL,
 `map_zoom` tinyint(3) unsigned DEFAULT NULL,
 `map_centre_gps` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_district_code` (`district_id`,`llg_code`),
 KEY `idx_district` (`district_id`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
migrations	CREATE TABLE `migrations` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `version` varchar(255) NOT NULL,
 `class` varchar(255) NOT NULL,
 `group` varchar(255) NOT NULL,
 `namespace` varchar(255) NOT NULL,
 `time` int(11) NOT NULL,
 `batch` int(11) unsigned NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
organizations	CREATE TABLE `organizations` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `org_code` char(5) NOT NULL,
 `name` varchar(150) NOT NULL,
 `description` text DEFAULT NULL,
 `license_status` enum('paid','unpaid') NOT NULL DEFAULT 'paid',
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `logo_path` varchar(255) DEFAULT NULL,
 `wallpaper_path` varchar(255) DEFAULT NULL,
 `contact_email` varchar(100) DEFAULT NULL,
 `contact_phone` varchar(30) DEFAULT NULL,
 `address_line1` varchar(150) DEFAULT NULL,
 `address_line2` varchar(150) DEFAULT NULL,
 `city` varchar(100) DEFAULT NULL,
 `state` varchar(100) DEFAULT NULL,
 `postal_code` varchar(20) DEFAULT NULL,
 `country` varchar(100) DEFAULT NULL,
 `hq_lat` decimal(10,8) DEFAULT NULL,
 `hq_lng` decimal(11,8) DEFAULT NULL,
 `website_url` varchar(150) DEFAULT NULL,
 `facebook_url` varchar(150) DEFAULT NULL,
 `twitter_url` varchar(150) DEFAULT NULL,
 `linkedin_url` varchar(150) DEFAULT NULL,
 `instagram_url` varchar(150) DEFAULT NULL,
 `country_lock` varchar(20) DEFAULT NULL,
 `province_lock` varchar(20) DEFAULT NULL,
 `district_lock` varchar(20) DEFAULT NULL,
 `llg_lock` varchar(20) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `org_code` (`org_code`),
 KEY `idx_license` (`license_status`),
 KEY `idx_active` (`is_active`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
organization_images	CREATE TABLE `organization_images` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `organization_id` bigint(20) unsigned NOT NULL,
 `image_path` varchar(255) NOT NULL,
 `caption` varchar(150) DEFAULT NULL,
 `sort_order` tinyint(3) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_org` (`organization_id`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
projects	CREATE TABLE `projects` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` bigint(20) unsigned NOT NULL,
 `pro_code` varchar(20) NOT NULL,
 `other_project_ids` text DEFAULT NULL,
 `title` varchar(200) NOT NULL,
 `goal` text DEFAULT NULL,
 `description` mediumtext DEFAULT NULL,
 `initiation_date` date DEFAULT NULL,
 `start_date` date DEFAULT NULL,
 `end_date` date DEFAULT NULL,
 `address_line` varchar(200) DEFAULT NULL,
 `country_id` bigint(20) unsigned DEFAULT NULL,
 `province_id` bigint(20) unsigned DEFAULT NULL,
 `district_id` bigint(20) unsigned DEFAULT NULL,
 `llg_id` bigint(20) unsigned DEFAULT NULL,
 `ward_name` varchar(100) DEFAULT NULL,
 `village_name` varchar(100) DEFAULT NULL,
 `gps_point` varchar(20) DEFAULT NULL,
 `gps_kml_path` varchar(255) DEFAULT NULL,
 `status` enum('planning','active','on-hold','completed','cancelled') DEFAULT 'planning',
 `status_notes` varchar(255) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_by_id` bigint(20) unsigned DEFAULT NULL,
 `officer_certified` tinyint(1) DEFAULT 0,
 `officer_cert_at` datetime DEFAULT NULL,
 `officer_cert_by` bigint(20) unsigned DEFAULT NULL,
 `contractor_certified` tinyint(1) DEFAULT 0,
 `contractor_cert_at` datetime DEFAULT NULL,
 `contractor_cert_by` bigint(20) unsigned DEFAULT NULL,
 `evaluation_file` varchar(255) DEFAULT NULL,
 `evaluation_notes` text DEFAULT NULL,
 `evaluation_date` date DEFAULT NULL,
 `evaluation_by` bigint(20) unsigned DEFAULT NULL,
 `baseline_year` smallint(5) unsigned DEFAULT NULL,
 `target_year` smallint(5) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `pro_code` (`pro_code`),
 KEY `idx_org` (`org_id`),
 KEY `idx_status` (`status`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
project_budget_items	CREATE TABLE `project_budget_items` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `item_code` varchar(30) NOT NULL,
 `description` varchar(255) NOT NULL,
 `amount_planned` decimal(15,2) NOT NULL,
 `status` enum('active','removed') DEFAULT 'active',
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_status` (`status`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_contractors	CREATE TABLE `project_contractors` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `contractor_id` bigint(20) unsigned NOT NULL,
 `joined_at` date NOT NULL,
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `removal_reason` text DEFAULT NULL,
 `client_flag` enum('positive','neutral','negative') DEFAULT 'neutral',
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_contractor` (`project_id`,`contractor_id`),
 KEY `idx_active` (`is_active`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_documents	CREATE TABLE `project_documents` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `milestone_id` bigint(20) unsigned DEFAULT NULL,
 `doc_path` varchar(255) NOT NULL,
 `doc_type` varchar(50) DEFAULT NULL,
 `description` varchar(255) DEFAULT NULL,
 `version_no` smallint(5) unsigned DEFAULT 1,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_milestone` (`project_id`,`milestone_id`),
 KEY `idx_version` (`project_id`,`version_no`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_expenses	CREATE TABLE `project_expenses` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `milestone_id` bigint(20) unsigned DEFAULT NULL,
 `description` varchar(255) NOT NULL,
 `amount_paid` decimal(15,2) NOT NULL,
 `paid_on` date NOT NULL,
 `file_path` varchar(255) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_milestone` (`project_id`,`milestone_id`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_impact_indicators	CREATE TABLE `project_impact_indicators` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `indicator_text` varchar(255) NOT NULL,
 `baseline_value` decimal(15,4) DEFAULT NULL,
 `baseline_date` date DEFAULT NULL,
 `target_value` decimal(15,4) DEFAULT NULL,
 `target_date` date DEFAULT NULL,
 `actual_value` decimal(15,4) DEFAULT NULL,
 `actual_date` date DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_issues_addressed	CREATE TABLE `project_issues_addressed` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `issue_type` enum('direct','indirect') NOT NULL,
 `description` text NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_milestones	CREATE TABLE `project_milestones` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `phase_id` bigint(20) unsigned NOT NULL,
 `milestone_code` varchar(20) NOT NULL,
 `title` varchar(200) NOT NULL,
 `description` text DEFAULT NULL,
 `target_date` date DEFAULT NULL,
 `status` enum('pending','in-progress','completed','approved') DEFAULT 'pending',
 `completion_date` date DEFAULT NULL,
 `evidence_count` smallint(5) unsigned DEFAULT 0,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_phase` (`phase_id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_status` (`status`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_officers	CREATE TABLE `project_officers` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `user_id` bigint(20) unsigned NOT NULL,
 `role` enum('lead','certifier','support') NOT NULL,
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `removal_reason` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project_user` (`project_id`,`user_id`),
 KEY `idx_role` (`role`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_outcomes	CREATE TABLE `project_outcomes` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `outcome_text` varchar(255) NOT NULL,
 `quantity` decimal(12,2) NOT NULL DEFAULT 1.00,
 `unit` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_phases	CREATE TABLE `project_phases` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `phase_code` varchar(20) NOT NULL,
 `title` varchar(150) NOT NULL,
 `description` text DEFAULT NULL,
 `sort_order` smallint(5) unsigned NOT NULL DEFAULT 1,
 `start_date` date DEFAULT NULL,
 `end_date` date DEFAULT NULL,
 `status` enum('active','deactivated') NOT NULL DEFAULT 'active',
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_sort` (`project_id`,`sort_order`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
project_risks	CREATE TABLE `project_risks` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `project_id` bigint(20) unsigned NOT NULL,
 `milestone_id` bigint(20) unsigned DEFAULT NULL,
 `risk_type` enum('proposed','foreseen','witnessed') NOT NULL,
 `risk_level` enum('low','medium','high','critical') NOT NULL,
 `description` text NOT NULL,
 `mitigation` text DEFAULT NULL,
 `approval_status` enum('pending','approved','rejected') DEFAULT 'pending',
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_project` (`project_id`),
 KEY `idx_status` (`approval_status`),
 KEY `idx_soft` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
provinces	CREATE TABLE `provinces` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `country_id` bigint(20) unsigned NOT NULL,
 `prov_code` varchar(10) NOT NULL,
 `name` varchar(100) NOT NULL,
 `geojson_id` varchar(50) DEFAULT NULL COMMENT 'ID of the feature in the GeoJSON boundary layer',
 `map_zoom` tinyint(3) unsigned DEFAULT NULL,
 `map_centre_gps` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uk_country_code` (`country_id`,`prov_code`),
 KEY `idx_country` (`country_id`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
users	CREATE TABLE `users` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `organization_id` bigint(20) unsigned NOT NULL,
 `user_code` varchar(12) NOT NULL,
 `username` varchar(50) NOT NULL,
 `email` varchar(100) NOT NULL,
 `name` varchar(100) NOT NULL,
 `role` enum('admin','moderator','editor','user') NOT NULL DEFAULT 'user',
 `profile_photo_path` varchar(255) DEFAULT NULL,
 `password_hash` varchar(255) NOT NULL,
 `activation_token` char(64) DEFAULT NULL,
 `is_activated` tinyint(1) NOT NULL DEFAULT 0,
 `is_project_officer` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 = user is a Project Officer',
 `is_evaluator` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 = user is an M&E Officer',
 `is_supervisor` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 = user is a Supervisor / Approver',
 `password_reset_token` char(64) DEFAULT NULL,
 `last_login_at` datetime DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `created_by` bigint(20) unsigned DEFAULT NULL,
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `user_code` (`user_code`),
 UNIQUE KEY `username` (`username`),
 UNIQUE KEY `email` (`email`),
 KEY `idx_organization` (`organization_id`),
 KEY `idx_email` (`email`),
 KEY `idx_activation` (`activation_token`),
 KEY `idx_password_reset` (`password_reset_token`),
 KEY `idx_soft_delete` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci