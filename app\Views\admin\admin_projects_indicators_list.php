<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#impact-indicators" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-graph-up me-2"></i>
    Add Indicator
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-graph-up me-2"></i>
            Impact Indicators
        </h1>
        <p class="text-muted mb-0">
            Measurable impact metrics for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Indicator Statistics -->
<div class="row g-4 mb-4">
    <!-- Total Indicators -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-graph-up text-primary"></i>
                </div>
                <div class="h4 fw-bold text-primary mb-2">
                    <?= $indicatorStats['total_indicators'] ?>
                </div>
                <div class="text-muted small">Total Indicators</div>
            </div>
        </div>
    </div>

    <!-- With Actual Values -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-check-circle text-success"></i>
                </div>
                <div class="h4 fw-bold text-success mb-2">
                    <?= $indicatorStats['with_actual_values'] ?>
                </div>
                <div class="text-muted small">With Actual Values</div>
            </div>
        </div>
    </div>

    <!-- Average Baseline -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-bar-chart text-info"></i>
                </div>
                <div class="h4 fw-bold text-info mb-2">
                    <?= number_format($indicatorStats['averages']['baseline'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Avg Baseline</div>
    </div>

    <!-- Average Target -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">🎯</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-accent); margin-bottom: var(--spacing-xs);">
            <?= number_format($indicatorStats['averages']['target'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Avg Target</div>
    </div>
</div>

<!-- Performance Summary -->
<?php if (!empty($indicatorPerformance)): ?>
<div class="card mb-xl">
    <div class="card-header">
        📈 Performance Summary
    </div>
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">
            <?php
            $statusCounts = ['pending' => 0, 'achieved' => 0, 'exceeded' => 0, 'not_met' => 0];
            foreach ($indicatorPerformance as $perf) {
                $statusCounts[$perf['status']]++;
            }
            ?>
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); text-align: center;">
                <div style="font-weight: 700; color: var(--text-muted); margin-bottom: var(--spacing-xs);">
                    Pending
                </div>
                <div style="color: var(--text-secondary); font-size: 1.25rem; font-weight: 600;">
                    <?= $statusCounts['pending'] ?>
                </div>
            </div>
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); text-align: center;">
                <div style="font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    Achieved
                </div>
                <div style="color: var(--text-secondary); font-size: 1.25rem; font-weight: 600;">
                    <?= $statusCounts['achieved'] ?>
                </div>
            </div>
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); text-align: center;">
                <div style="font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    Exceeded
                </div>
                <div style="color: var(--text-secondary); font-size: 1.25rem; font-weight: 600;">
                    <?= $statusCounts['exceeded'] ?>
                </div>
            </div>
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); text-align: center;">
                <div style="font-weight: 700; color: var(--brand-danger); margin-bottom: var(--spacing-xs);">
                    Not Met
                </div>
                <div style="color: var(--text-secondary); font-size: 1.25rem; font-weight: 600;">
                    <?= $statusCounts['not_met'] ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Indicators Table -->
<div class="card">
    <div class="card-header">
        📊 Impact Indicators
    </div>

    <?php if (!empty($indicators)): ?>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-color);">
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Indicator Description
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Baseline
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Target
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actual
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Status
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($indicators as $indicator): ?>
                        <tr style="border-bottom: 1px solid var(--border-color);">
                            <td style="padding: var(--spacing-md); vertical-align: top;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc($indicator['indicator_text']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    Created: <?= date('M j, Y', strtotime($indicator['created_at'])) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <?php if ($indicator['baseline_value'] !== null && $indicator['baseline_value'] !== ''): ?>
                                    <div style="font-weight: 600; color: var(--text-primary);">
                                        <?= is_numeric($indicator['baseline_value']) ? number_format($indicator['baseline_value'], 2) : esc($indicator['baseline_value']) ?>
                                    </div>
                                    <?php if ($indicator['baseline_date']): ?>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            <?= date('M j, Y', strtotime($indicator['baseline_date'])) ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <?php if ($indicator['target_value'] !== null && $indicator['target_value'] !== ''): ?>
                                    <div style="font-weight: 600; color: var(--brand-accent);">
                                        <?= is_numeric($indicator['target_value']) ? number_format($indicator['target_value'], 2) : esc($indicator['target_value']) ?>
                                    </div>
                                    <?php if ($indicator['target_date']): ?>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            <?= date('M j, Y', strtotime($indicator['target_date'])) ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <?php if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== ''): ?>
                                    <div style="font-weight: 600; color: var(--brand-success);">
                                        <?= is_numeric($indicator['actual_value']) ? number_format($indicator['actual_value'], 2) : esc($indicator['actual_value']) ?>
                                    </div>
                                    <?php if ($indicator['actual_date']): ?>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            <?= date('M j, Y', strtotime($indicator['actual_date'])) ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">Pending</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <?php
                                $performance = null;
                                foreach ($indicatorPerformance as $perf) {
                                    if ($perf['id'] == $indicator['id']) {
                                        $performance = $perf;
                                        break;
                                    }
                                }
                                
                                if ($performance):
                                    $statusColors = [
                                        'pending' => 'var(--text-muted)',
                                        'achieved' => 'var(--brand-success)',
                                        'exceeded' => 'var(--brand-primary)',
                                        'not_met' => 'var(--brand-danger)'
                                    ];
                                    $statusColor = $statusColors[$performance['status']] ?? 'var(--text-muted)';
                                ?>
                                    <span style="background: <?= $statusColor ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                        <?= esc(str_replace('_', ' ', $performance['status'])) ?>
                                    </span>
                                    <?php if ($performance['achievement_rate'] !== null): ?>
                                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                                            <?= number_format($performance['achievement_rate'], 1) ?>%
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center; vertical-align: top;">
                                <div style="display: flex; gap: var(--spacing-xs); justify-content: center;">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/' . $indicator['id'] . '/edit') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        ✏️ Edit
                                    </a>
                                    <button onclick="showDeleteModal(<?= $indicator['id'] ?>, '<?= esc($indicator['indicator_text']) ?>')" class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📊</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Impact Indicators Yet</h3>
            <p style="margin-bottom: var(--spacing-lg);">Start defining measurable impact metrics for monitoring and evaluation.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary">
                📊 Create First Indicator
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 10000; align-items: center; justify-content: center;">
    <div style="background: var(--bg-primary); border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%; box-shadow: var(--shadow-xl);">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Delete Impact Indicator</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete the indicator "<span id="deleteIndicatorText" style="font-weight: 600;"></span>"? This action cannot be undone.
        </p>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Indicator</button>
        </div>
    </div>
</div>

<script>
let currentDeleteIndicatorId = null;

function showDeleteModal(indicatorId, indicatorText) {
    currentDeleteIndicatorId = indicatorId;
    document.getElementById('deleteIndicatorText').textContent = indicatorText;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteIndicatorId = null;
}

function confirmDelete() {
    if (currentDeleteIndicatorId) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/indicators/') ?>' + currentDeleteIndicatorId + '/delete';

        // Add CSRF token if available
        <?php if (csrf_token()): ?>
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);
        <?php endif; ?>

        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
