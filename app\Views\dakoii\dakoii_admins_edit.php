<?php /** @var array $admin */ ?>
<?php /** @var array $organization */ ?>

<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins') ?>" class="btn btn-secondary">
    ← Back to Administrators
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <ul style="margin: 0; padding-left: 20px;">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Current Admin Info -->
    <div class="current-info">
        <div class="avatar-placeholder">
            <?= strtoupper(substr($admin['name'], 0, 1)) ?>
        </div>

        <div class="current-details">
            <h3><?= esc($admin['name']) ?></h3>
            <p><strong>Username:</strong> <?= esc($admin['username']) ?></p>
            <p><strong>Email:</strong> <?= esc($admin['email']) ?></p>
            <p><strong>Role:</strong>
                <span class="role-badge role-<?= $admin['role'] ?>">
                    <?= ucfirst($admin['role']) ?>
                </span>
            </p>
            <p><strong>Organization:</strong> <?= esc($organization['name']) ?></p>
        </div>
    </div>

    <form action="<?= base_url('dakoii/organizations/admins/' . $admin['id'] . '/update') ?>" method="post" id="editAdminForm">
        <?= csrf_field() ?>

        <!-- Basic Information -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Administrator Information</div>

            <div class="form-group">
                <label for="name" class="form-label">Full Name *</label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    class="form-input"
                    placeholder="Enter full name"
                    value="<?= esc($admin['name']) ?>"
                    required
                    maxlength="100"
                >
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address *</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    class="form-input"
                    placeholder="Enter email address"
                    value="<?= esc($admin['email']) ?>"
                    required
                    maxlength="100"
                >
            </div>

            <div class="form-group">
                <label for="username_display" class="form-label">Username</label>
                <input
                    type="text"
                    id="username_display"
                    class="form-input"
                    value="<?= esc($admin['username']) ?>"
                    disabled
                    style="background: var(--glass-bg); color: var(--text-muted);"
                >
                <div class="form-help">Username cannot be changed</div>
            </div>

            <div class="form-group">
                <label for="role" class="form-label">Role *</label>
                <select id="role" name="role" class="form-input" required>
                    <option value="">Select Role</option>
                    <option value="admin" <?= $admin['role'] === 'admin' ? 'selected' : '' ?>>Administrator</option>
                    <option value="moderator" <?= $admin['role'] === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                    <option value="editor" <?= $admin['role'] === 'editor' ? 'selected' : '' ?>>Editor</option>
                    <option value="user" <?= $admin['role'] === 'user' ? 'selected' : '' ?>>User</option>
                </select>
                <div class="form-help">Select the appropriate role for this administrator</div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
            <a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins') ?>" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <span id="submitText">Update Administrator</span>
                <span id="submitLoader" class="loading" style="display: none;"></span>
            </button>
        </div>
    </form>
</div>

<style>
.form-help {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid;
    backdrop-filter: blur(10px);
}

.alert-error {
    background: rgba(255, 0, 110, 0.1);
    border-color: rgba(255, 0, 110, 0.3);
    color: #FF006E;
}

.current-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--glass-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.current-details h3 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
}

.current-details p {
    margin: var(--spacing-xs) 0;
    color: var(--text-secondary);
}

.role-badge {
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.role-admin { background: rgba(255, 0, 110, 0.1); color: #FF006E; }
.role-moderator { background: rgba(255, 183, 0, 0.1); color: #FFB700; }
.role-editor { background: rgba(0, 255, 163, 0.1); color: #00FFA3; }
.role-user { background: rgba(128, 128, 128, 0.1); color: #808080; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editAdminForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitLoader = document.getElementById('submitLoader');

    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        submitText.style.display = 'none';
        submitLoader.style.display = 'inline-block';

        // Re-enable after 30 seconds as fallback
        setTimeout(function() {
            submitBtn.disabled = false;
            submitText.style.display = 'inline';
            submitLoader.style.display = 'none';
        }, 30000);
    });
});
</script>
<?= $this->endSection() ?>
