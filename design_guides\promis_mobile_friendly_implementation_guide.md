# PROMIS Mobile-Friendly Implementation Guide

## Overview
This guide provides the standardized approach for implementing mobile-friendly buttons and forms across the PROMIS Admin Portal while maintaining the original desktop appearance.

## Design Philosophy
**Progressive Enhancement**: Original desktop design is preserved, with mobile-specific enhancements applied only on mobile devices (768px and below).

## Button Implementation

### HTML Structure
```html
<!-- Standard Button -->
<button class="btn btn-primary btn-mobile">
    <span class="btn-icon">✅</span>
    <span class="btn-text">Create Phase</span>
</button>

<!-- Link Button -->
<a href="#" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Project</span>
</a>
```

### CSS Implementation
```css
/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Mobile enhancements */
@media (max-width: 768px) {
    .btn-mobile {
        min-height: 48px;
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
    }
}
```

## Form Implementation

### Form Actions Container
```html
<div class="form-actions">
    <a href="#" class="btn btn-secondary btn-mobile">
        <span class="btn-icon">←</span>
        <span class="btn-text">Cancel</span>
    </a>
    <button type="submit" class="btn btn-primary btn-mobile">
        <span class="btn-icon">✅</span>
        <span class="btn-text">Save</span>
    </button>
</div>
```

### Form Actions CSS
```css
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .form-actions {
        justify-content: stretch;
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}
```

## Form Input Enhancements

### Enhanced Form Inputs
```css
/* Enhanced form inputs */
.form-input {
    min-height: 44px;
    font-size: 0.875rem;
    padding: var(--spacing-md);
}

/* Mobile enhancements */
@media (max-width: 768px) {
    .form-input {
        min-height: 48px;
        font-size: 16px; /* Prevents iOS zoom */
    }
}

/* Enhanced select dropdowns */
select.form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 100px;
}
```

## Responsive Grid Layout

### Grid Implementation
```css
/* Responsive grid for forms */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }
}
```

## Accessibility Enhancements

### Focus Indicators
```css
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

.form-input:focus {
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

### Loading States
```css
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}
```

## Implementation Checklist

### For Each Page
- [ ] Replace standard buttons with `.btn-mobile` structure
- [ ] Add icon-text separation using spans
- [ ] Implement `.form-actions` container
- [ ] Add responsive grid classes
- [ ] Test on mobile devices

### CSS Requirements
- [ ] Add mobile-friendly button styles
- [ ] Add form action container styles
- [ ] Add enhanced input styles
- [ ] Add responsive breakpoints
- [ ] Add accessibility improvements

### Testing Requirements
- [ ] Desktop appearance matches original
- [ ] Mobile touch targets are 48px minimum
- [ ] Buttons stack properly on mobile
- [ ] Form inputs prevent iOS zoom
- [ ] Focus indicators are visible

## Rollout Strategy

### Phase 1: Template Update
Add base styles to `promis_admin_template.php`

### Phase 2: High-Traffic Pages
- Dashboard
- Project list
- User management

### Phase 3: CRUD Forms
- Create forms
- Edit forms
- Settings pages

### Phase 4: Remaining Pages
- Reports
- Analytics
- Admin tools

## Browser Support
- iOS Safari 12+
- Android Chrome 70+
- Desktop Chrome 80+
- Desktop Firefox 75+
- Desktop Safari 13+

## Performance Notes
- CSS animations use GPU acceleration
- Touch events optimized with `touch-action: manipulation`
- Minimal JavaScript for enhanced performance
- Efficient CSS selectors for faster rendering
