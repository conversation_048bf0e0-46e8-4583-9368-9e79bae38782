<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-contractors" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-building-plus me-2"></i>
    Assign Contractor
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-building me-2"></i>
            Project Contractors
        </h1>
        <p class="text-muted mb-0">
            Manage contractor assignments for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Project Context Card -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center">
            <div class="rounded-circle d-flex align-items-center justify-content-center text-white me-3 fw-bold"
                 style="width: 40px; height: 40px; background: var(--promis-gradient-primary);">
                <i class="bi bi-folder"></i>
            </div>
            <div>
                <h5 class="fw-semibold text-primary mb-1">
                    <?= esc($project['title']) ?>
                </h5>
                <div class="text-muted small">
                    Project Code: <?= esc($project['pro_code']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contractors Summary -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-bar-chart me-2"></i>
            Contractors Summary
        </h5>
    </div>

    <div class="card-body">
        <div class="row g-3">
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-primary mb-1">
                        <?= $stats['total_contractors'] ?>
                    </div>
                    <div class="text-muted small">Total Contractors</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-success mb-1">
                        <?= $stats['active_contractors'] ?>
                    </div>
                    <div class="text-muted small">Active Contractors</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-success mb-1">
                        <?= $stats['by_client_flag']['positive'] ?>
                    </div>
                    <div class="text-muted small">Positive Rating</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-warning mb-1">
                        <?= $stats['by_client_flag']['neutral'] ?>
                    </div>
                    <div class="text-muted small">Neutral Rating</div>
                </div>
            </div>
            <div class="col-lg col-md-6">
                <div class="text-center bg-light p-3 rounded">
                    <div class="h4 fw-bold text-danger mb-1">
                        <?= $stats['by_client_flag']['negative'] ?>
                    </div>
                    <div class="text-muted small">Negative Rating</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contractors List -->
<div class="card mb-4">
    <div class="card-header bg-light d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="bi bi-building me-2"></i>
            Assigned Contractors
        </h5>
        <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary promis-btn-gradient btn-sm">
            <i class="bi bi-building-plus me-2"></i>
            Assign New Contractor
        </a>
    </div>

    <div class="card-body">
        <?php if (!empty($contractors)): ?>
            <div class="vstack gap-4">
                <?php foreach ($contractors as $contractor): ?>
                    <div class="bg-light rounded p-4 border-start border-4 <?= $contractor['is_active'] ? 'border-success' : 'border-secondary' ?>">
                        <div class="d-flex align-items-start justify-content-between">
                            <!-- Contractor Info -->
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center flex-wrap gap-2 mb-3">
                                    <h6 class="fw-semibold text-primary mb-0">
                                        <?= esc($contractor['contractor_name']) ?>
                                    </h6>
                                    <?php if ($contractor['is_active']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-x-circle me-1"></i>
                                            Removed
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($contractor['is_active']): ?>
                                        <?php
                                        $flagClass = match($contractor['client_flag']) {
                                            'positive' => 'bg-success',
                                            'negative' => 'bg-danger',
                                            default => 'bg-warning'
                                        };
                                        $flagIcon = match($contractor['client_flag']) {
                                            'positive' => 'bi-hand-thumbs-up',
                                            'negative' => 'bi-hand-thumbs-down',
                                            default => 'bi-dash-circle'
                                        };
                                        ?>
                                        <span class="badge <?= $flagClass ?>">
                                            <i class="<?= $flagIcon ?> me-1"></i>
                                            <?= ucfirst($contractor['client_flag']) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="row g-2 small mb-3">
                                    <div class="col-md-6">
                                        <span class="text-muted fw-medium">Organization Code:</span>
                                        <span class="text-secondary font-monospace"><?= esc($contractor['contractor_code']) ?></span>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="text-muted fw-medium">Joined:</span>
                                        <span class="text-secondary"><?= date('M j, Y', strtotime($contractor['joined_at'])) ?></span>
                                    </div>
                                    <?php if (!$contractor['is_active'] && $contractor['removal_reason']): ?>
                                    <div class="col-12">
                                        <span class="text-muted fw-medium">Removal Reason:</span>
                                        <span class="text-secondary"><?= esc($contractor['removal_reason']) ?></span>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Contractor Actions -->
                            <?php if ($contractor['is_active']): ?>
                                <div class="d-flex flex-column gap-2">
                                    <button onclick="showRemoveContractorModal(<?= $contractor['contractor_id'] ?>, '<?= esc($contractor['contractor_name']) ?>')"
                                            class="btn btn-outline-danger btn-sm">
                                        <i class="bi bi-trash me-1"></i>
                                        Remove
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <div class="display-1 text-muted opacity-50 mb-4">
                    <i class="bi bi-building"></i>
                </div>
                <h5 class="text-muted mb-3">No Contractors Assigned</h5>
                <p class="text-muted mb-4">Start building your project team by assigning the first contractor.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/contractors/create') ?>" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-building-plus me-2"></i>
                    Assign First Contractor
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Remove Contractor Modal -->
<div class="modal fade" id="removeContractorModal" tabindex="-1" aria-labelledby="removeContractorModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger" id="removeContractorModalLabel">
                    <i class="bi bi-trash me-2"></i>
                    Remove Project Contractor
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="removeContractorForm" method="POST">
                <div class="modal-body">
                    <?= csrf_field() ?>
                    <div class="mb-4">
                        <p class="text-secondary">
                            Are you sure you want to remove <strong id="contractorName"></strong> from this project?
                        </p>
                        <p class="text-muted small">
                            This action will mark the contractor as inactive but preserve the assignment history.
                        </p>
                    </div>

                    <div class="mb-3">
                        <label for="removal_reason" class="form-label fw-semibold">
                            Removal Reason <span class="text-danger">*</span>
                        </label>
                        <textarea name="removal_reason" id="removal_reason" rows="3"
                                  class="form-control border-danger" required
                                  placeholder="Enter reason for removing this contractor..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>
                        Remove Contractor
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRemoveContractorModal(contractorId, contractorName) {
    document.getElementById('contractorName').textContent = contractorName;
    document.getElementById('removeContractorForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/contractors/') ?>' + contractorId + '/remove';

    // Show Bootstrap modal
    const modal = new bootstrap.Modal(document.getElementById('removeContractorModal'));
    modal.show();
}
document.getElementById('removeContractorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideRemoveContractorModal();
    }
});
</script>

<?= $this->endSection() ?>
