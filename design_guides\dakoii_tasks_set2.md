# Dakoii Portal Functions 1-7 Development Task List

## Application Context Summary

**Framework:** CodeIgniter 4
**Base URL:** http://localhost/promis_two/
**Development Environment:** XAMPP
**Database:** promis_two_db (MySQL)

**Key Database Tables:**
- `dakoii_users` - Portal admin users (user_code VARCHAR(12), roles: admin/moderator/user)
- `organizations` - Organizations (org_code CHAR(5), license_status: paid/unpaid)
- `users` - Organization users/admins (organization_id, user_code, roles: admin/moderator/editor/user)
- `organization_images` - Organization image gallery (up to 5 images)
- `countries`, `provinces`, `districts`, `llgs` - Government hierarchy

**Templates:**
- `dakoii_public_template.php` - For login/public pages
- `dakoii_portal_template.php` - For authenticated admin interface

**View Naming Convention:**
- Folder: `Views/dakoii/`
- Files: `dakoii_[module]_[action].php` (e.g., `dakoii_organizations_list.php`)

**Authentication:**
- Session-based with `dakoii_user_id`, `dakoii_logged_in`
- 8-hour session timeout
- Argon2id password hashing
- Soft delete with `deleted_at`, `deleted_by` audit fields

**Project Rules:**
- No AJAX form submissions - use standard CI4 form handling
- RESTful approach with separate GET/POST methods
- Soft delete for all entities
- Audit trail using `created_by`, `updated_by`, `deleted_by` fields

## Function 1: Core Authentication & Session

### 1.1 showLoginForm

**Task:** Create login form view
**Description:** Develop the main login page with dark theme styling that renders at `/dakoii` endpoint

- Create `Views/dakoii/dakoii_login.php` with dark theme CSS using dakoii_public_template
- Implement CSRF token injection using CI4's security helper
- Add form validation display areas for error messages
- Include responsive design for mobile and desktop
- Integrate glassmorphic design elements from dakoii_theme_design_json.md specifications
- Add proper meta tags and page title
- Implement loading states for form submission

**Task:** Setup routing configuration
**Description:** Configure CI4 routes to handle the login endpoint properly

- Define route in `Config/Routes.php` for GET `/dakoii` (already implemented)
- Set up route constraints and dakoii_auth filters (already implemented)
- Configure default route redirection logic
- Add route caching optimization

### 1.2 authenticateUser

**Task:** Implement credential validation logic
**Description:** Create secure authentication method that verifies user credentials and manages sessions

- Create validation rules for email/username and password fields
- Implement email/username lookup in dakoii_users table with proper indexing
- Add Argon2id password verification using `password_verify()`
- Implement account lockout after failed attempts (security feature)
- Add logging for successful and failed login attempts
- Create proper error messaging without revealing system details

**Task:** Session management implementation
**Description:** Secure session handling with proper regeneration and security measures

- Configure CI4 session settings for security (already implemented in DakoiiAuthController)
- Implement session ID regeneration on successful login
- Store dakoii_user_id and dakoii_logged_in in session with minimal exposure
- Set proper session timeout (8 hours as implemented in DakoiiAuthFilter)
- Add "remember me" functionality with secure tokens
- Implement session fixation protection

**Task:** Post-authentication routing
**Description:** Handle redirection logic after successful authentication

- Create dashboard redirect to `/dakoii/dashboard` for successful authentication
- Implement "intended URL" functionality for deep links (already in DakoiiAuthFilter)
- Add role-based redirection logic for admin/moderator/user roles
- Handle first-time login scenarios
- Create audit trail entry for login events

### 1.3 logoutUser

**Task:** Secure session termination
**Description:** Properly destroy user sessions and clean up resources

- Implement complete session destruction using session->destroy()
- Clear any persistent login tokens
- Add logout confirmation modal (optional)
- Create audit trail entry for logout events in dakoii_users table (last_login_at)
- Implement CSRF protection for logout action
- Handle concurrent session termination

**Task:** Post-logout redirection
**Description:** Ensure proper cleanup and user experience after logout

- Redirect to `/dakoii` login page with success message
- Clear any cached user data
- Implement logout from all devices functionality
- Add proper cache headers to prevent back-button issues

## Function 2: Dashboard

### 2.1 getDashboardStats

**Task:** Database query optimization
**Description:** Create efficient queries to gather dashboard statistics

- Design optimized COUNT queries for organizations table
- Create query for organization users count from users table with organization_id joins
- Implement government units statistics aggregation (countries, provinces, districts, llgs)
- Add Dakoii users count from dakoii_users table with role filtering
- Optimize queries with proper indexing strategy
- Implement query caching for frequently accessed data

**Task:** Dashboard data preparation
**Description:** Format and structure data for dashboard presentation

- Create data structure for quick statistics cards (already implemented in DakoiiDashboardController)
- Implement recent activity feed logic
- Add time-based filtering for activity data
- Calculate percentage changes and trends
- Format numbers with proper localization
- Prepare data for chart visualizations

**Task:** Dashboard view implementation
**Description:** Create responsive dashboard interface with modern UI components

- Implement glassmorphic cards for statistics display in `Views/dakoii/dakoii_dashboard.php`
- Create responsive grid layout using dakoii_portal_template
- Add interactive charts using the dakoii theme color palette
- Implement real-time data updates (avoid AJAX, use standard CI4 form submission)
- Add loading states and skeleton screens
- Create empty states for when no data exists

**Task:** Performance monitoring setup
**Description:** Implement monitoring and caching for dashboard performance

- Add query performance monitoring
- Implement database caching for dashboard data (CI4 built-in caching)
- Create cache invalidation strategy
- Add database query optimization monitoring
- Implement progressive data loading

## Function 3: Organizations Management

### 3.1 listOrganisations

**Task:** Advanced filtering and search implementation
**Description:** Create comprehensive search and filter functionality for organizations

- Implement full-text search across organization fields (name, description, org_code)
- Add status-based filtering (is_active true/false, license_status paid/unpaid)
- Create date range filters for created_at/updated_at dates
- Implement sorting by multiple columns
- Add export functionality for filtered results
- Create saved filter presets

**Task:** Pagination and data loading optimization
**Description:** Efficient data loading with proper pagination controls

- Implement CI4 Pager with custom styling for dakoii theme
- Add per-page options (10, 25, 50, 100 records)
- Use standard CI4 pagination (avoid AJAX, follow project rules)
- Implement server-side pagination for performance
- Add total count display and page information
- Optimize database queries with proper LIMIT/OFFSET

**Task:** List view interface design
**Description:** Create modern, responsive organization listing interface

- Design card-based layout with glassmorphic styling in `Views/dakoii/dakoii_organizations_list.php`
- Implement table view option with sortable columns
- Add bulk selection and actions functionality
- Create quick preview modals for organization details
- Add contextual action menus for each organization
- Implement responsive design using dakoii_portal_template

### 3.2 showCreateOrganisationForm

**Task:** Form design and validation setup
**Description:** Create comprehensive organization creation form with proper validation

- Design multi-step form with progress indicator in `Views/dakoii/dakoii_organizations_create.php`
- Implement real-time validation for all fields using CI4 validation
- Add file upload areas for logo_path and wallpaper_path
- Create license_status selection interface (paid/unpaid enum)
- Add address and contact information sections (address_line1, address_line2, city, state, postal_code, country)
- Implement form autosave functionality

**Task:** Default value population
**Description:** Set up intelligent defaults and data pre-population

- Pre-populate license_status as 'paid' (default in organizations table)
- Set is_active default to true (default in organizations table)
- Add country/timezone detection for new organizations
- Implement template-based organization creation
- Add data import functionality from CSV/Excel
- Create organization type templates

### 3.3 createOrganisation

**Task:** Data validation and sanitization
**Description:** Comprehensive server-side validation and data processing

- Implement robust validation rules for all required fields using OrganizationModel allowedFields
- Add business rule validation (unique org_code, unique names, valid formats)
- Sanitize all input data to prevent XSS attacks
- Validate file uploads for logo_path and wallpaper_path (size, type, dimensions)
- Implement duplicate detection logic
- Add data normalization for consistent storage

**Task:** Organization code generation system
**Description:** Create unique 5-digit organization code generation

- Implement collision-resistant code generation algorithm for org_code (CHAR(5))
- Add retry logic for code conflicts
- Create code format validation (exactly 5 characters)
- Implement code reservation system for concurrent requests
- Add manual code override capability for admin users
- Store code generation history for audit purposes

**Task:** Database transaction management
**Description:** Ensure data integrity during organization creation

- Implement database transactions for organizations table insert
- Add rollback logic for failed operations
- Create audit trail entries using created_by, updated_by fields
- Implement optimistic locking for concurrent access
- Add error handling with proper user feedback
- Create success confirmation with next action options

### 3.4 generateOrganisationCode

**Task:** Unique code algorithm implementation
**Description:** Develop robust algorithm for generating unique 5-digit codes

- Create random number generator with proper entropy for org_code (CHAR(5))
- Implement collision detection with organizations table queries
- Add blacklist functionality for reserved codes
- Create code format validation (exactly 5 characters, alphanumeric)
- Implement retry logic with maximum attempt limits
- Add performance monitoring for code generation time

**Task:** Code validation and reservation system
**Description:** Ensure code uniqueness and handle concurrent requests

- Implement atomic check-and-reserve operations
- Add temporary code reservation during form processing
- Create cleanup process for expired reservations
- Implement code recycling from soft-deleted organizations (deleted_at IS NOT NULL)
- Add manual code assignment capability
- Create code usage analytics and reporting

### 3.5 viewOrganisationProfile

**Task:** Comprehensive profile data aggregation
**Description:** Gather and present complete organization information

- Join organization data with organization_images table and users table (organization admins)
- Calculate organization metrics and usage statistics
- Aggregate user activity and engagement data from users table
- Load related government unit associations using location locks (country_lock, province_lock, district_lock, llg_lock)
- Compile audit trail and change history using created_by, updated_by, created_at, updated_at
- Generate organization health score

**Task:** Rich media display implementation
**Description:** Create engaging visual presentation of organization data

- Implement image gallery with lightbox functionality for organization_images
- Create logo_path display with fallback options
- Add wallpaper_path/header image presentation
- Implement image lazy loading for performance
- Add image zoom and download capabilities
- Create slideshow functionality for organization images (up to 5 images)

**Task:** Interactive profile interface
**Description:** Build modern, interactive profile viewing experience

- Create tabbed interface for different data sections using dakoii_portal_template
- Implement quick edit functionality for authorized users
- Add social sharing capabilities using website_url, facebook_url, twitter_url, linkedin_url, instagram_url
- Create print-friendly profile version
- Implement bookmark/favorite functionality
- Add activity timeline visualization

### 3.6 showEditOrganisationModal

**Task:** Dynamic modal content loading
**Description:** Create flexible modal system for different edit contexts

- Implement context-aware modal content loading
- Create separate modal templates for different edit types
- Add form state management for modal interactions
- Implement unsaved changes detection and warnings
- Create modal resizing for different content types
- Add keyboard navigation and accessibility features

**Task:** Real-time form validation
**Description:** Provide immediate feedback during editing

- Implement client-side validation with real-time feedback
- Add server-side validation with AJAX responses
- Create field-specific validation messages
- Implement progressive enhancement for validation
- Add visual indicators for valid/invalid states
- Create validation summary display

### 3.7 updateOrganisation

**Task:** Selective field update system
**Description:** Implement efficient update mechanism for modified fields only

- Create change detection algorithm for form fields
- Implement partial update queries for performance
- Add validation for each updateable field
- Create update conflict resolution
- Implement optimistic locking for concurrent edits
- Generate detailed change logs for audit trail

**Task:** File upload processing
**Description:** Handle logo, wallpaper, and image uploads securely

- Implement secure file upload with type validation
- Add image processing for multiple sizes/formats
- Create file organization structure on server
- Implement old file cleanup during updates
- Add image optimization and compression
- Create backup system for replaced files

### 3.8 uploadOrganisationImages

**Task:** Multi-image upload system
**Description:** Handle upload of up to 5 organization images with management

- Implement drag-and-drop image upload interface
- Add image preview functionality before upload
- Create image reordering with drag-and-drop
- Implement bulk image operations (delete, reorder)
- Add image metadata capture (title, description)
- Create image approval workflow for moderated content

**Task:** Image storage and optimization
**Description:** Efficient image processing and storage management

- Implement multiple image size generation (thumbnail, medium, large)
- Add image format optimization (WebP, JPEG, PNG)
- Create CDN integration for image delivery
- Implement image compression with quality settings
- Add watermarking capability for branded images
- Create image backup and recovery system

### 3.9 toggleOrganisationStatus

**Task:** Status change workflow
**Description:** Implement secure status toggle with proper validation

- Add authorization check for status change permissions
- Implement status change validation rules
- Create confirmation dialog for status changes
- Add bulk status change functionality
- Implement status change notifications
- Create audit trail for all status changes

**Task:** Cascade effect management
**Description:** Handle implications of organization status changes

- Implement cascading status updates for related entities
- Add user notification system for status changes
- Create grace period for temporary deactivations
- Implement automatic reactivation scheduling
- Add impact assessment before status changes
- Create rollback capability for accidental changes

### 3.10 changeOrganisationLicenseStatus

**Task:** License management system
**Description:** Handle paid/unpaid license status transitions

- Implement license validation and verification
- Add billing integration for license changes
- Create license renewal notification system
- Implement grace period for expired licenses
- Add license usage tracking and reporting
- Create automatic license suspension workflow

**Task:** License impact assessment
**Description:** Manage feature access based on license status

- Implement feature restriction for unpaid licenses
- Add license status display throughout application
- Create license upgrade/downgrade workflows
- Implement license analytics and reporting
- Add license compliance monitoring
- Create license renewal reminder system

### 3.11 softDeleteOrganisation

**Task:** Safe deletion implementation
**Description:** Implement soft delete with proper data preservation

- Add confirmation workflow with impact assessment
- Implement soft delete with timestamp and user tracking
- Create data anonymization for deleted records
- Add cascade soft delete for related records
- Implement deletion approval workflow for large organizations
- Create deletion audit trail and reporting

**Task:** Data recovery system
**Description:** Provide mechanisms for recovering deleted organizations

- Implement undelete functionality with time limits
- Add deleted record browsing for administrators
- Create bulk recovery operations
- Implement permanent deletion after retention period
- Add data export before permanent deletion
- Create deletion impact reporting

## Function 4: Organisation Administrators Management

### 4.1 listOrgAdmins

**Task:** Admin listing with organization context
**Description:** Create filtered view of administrators for specific organizations

- Implement organization-specific admin filtering using users table with organization_id
- Add role-based access control for admin viewing (admin/moderator/editor/user roles)
- Create search functionality across admin fields (username, email, name, user_code)
- Implement status-based filtering (is_activated true/false)
- Add sorting and pagination for admin lists using `Views/dakoii/dakoii_admins_list.php`
- Create export functionality for admin data

**Task:** Admin relationship visualization
**Description:** Show admin hierarchy and relationships within organization

- Create organizational chart for admin hierarchy based on role field
- Implement permission level visualization for different roles
- Add admin activity summary display using last_login_at
- Create admin contact information display (email from users table)
- Implement admin performance metrics
- Add admin assignment history tracking

### 4.2 showCreateAdminForm

**Task:** Admin creation form design
**Description:** Comprehensive form for creating new organization administrators

- Design step-by-step admin creation wizard in `Views/dakoii/dakoii_admins_create.php`
- Implement role selection interface (admin/moderator/editor/user enum)
- Add contact information collection forms (email, name)
- Create admin profile setup sections (username, profile_photo_path)
- Implement organization assignment interface using organization_id
- Add admin notification preferences setup

**Task:** Admin code generation preview
**Description:** Show admin code generation and validation in real-time

- Implement real-time user_code generation preview (alphanumeric, 8-12 chars)
- Add code availability checking against users table
- Create custom code option for manual assignment
- Implement code format validation display
- Add code generation history for reference
- Create code reservation during form completion

### 4.3 createOrgAdmin

**Task:** Admin account creation workflow
**Description:** Complete admin creation process with validation and setup

- Implement comprehensive admin data validation using UserModel allowedFields
- Add duplicate admin detection across organizations (unique username, email, user_code)
- Create admin profile initialization with default values
- Implement permission assignment based on role (admin/moderator/editor/user)
- Add admin welcome package generation
- Create admin onboarding task list

**Task:** Activation email system
**Description:** Send secure activation emails to new administrators

- Design professional activation email template
- Implement secure activation_token generation for activation
- Add email delivery tracking and confirmation
- Create email resend functionality
- Implement activation link expiration handling
- Add email delivery failure handling

### 4.4 generateAdminCode

**Task:** Admin code generation algorithm
**Description:** Create unique alphanumeric codes for admin identification

- Implement alphanumeric user_code generation (8-12 characters as per users table)
- Add code uniqueness validation across all users table records
- Create code format consistency (prefix options)
- Implement code collision resolution
- Add manual code override capability
- Create code generation performance optimization

**Task:** Code management system
**Description:** Comprehensive code lifecycle management

- Implement code reservation system
- Add code expiration and recycling from soft-deleted users (deleted_at IS NOT NULL)
- Create code format validation rules
- Implement code blacklist functionality
- Add code usage analytics
- Create code audit trail

### 4.5 viewOrgAdminProfile

**Task:** Comprehensive admin profile display
**Description:** Complete admin information presentation with organization context

- Load admin data with organization relationships
- Display admin permissions and role information
- Show admin activity timeline and history
- Create admin contact information display
- Implement admin document management
- Add admin performance metrics display

**Task:** Admin profile interactivity
**Description:** Interactive elements for admin profile management

- Implement quick edit functionality for authorized users
- Add admin status change controls
- Create admin communication tools (messaging)
- Implement admin task assignment interface
- Add admin schedule and availability display
- Create admin export functionality

### 4.6 showEditAdminModal

**Task:** Context-aware edit modal system
**Description:** Flexible modal system for different admin editing scenarios

- Create ID photo upload modal with cropping
- Implement status change confirmation modal
- Add permission modification interface
- Create contact information edit modal
- Implement role change workflow modal
- Add admin notes and comments modal

**Task:** Modal form validation and UX
**Description:** Smooth editing experience with proper validation

- Implement real-time validation in modal forms
- Add unsaved changes detection and warnings
- Create modal form autosave functionality
- Implement keyboard shortcuts for modal navigation
- Add modal resizing for different content types
- Create modal state persistence across sessions

### 4.7 updateOrgAdmin

**Task:** Admin data update processing
**Description:** Handle admin information updates with proper validation

- Implement selective field update processing
- Add change detection and audit logging
- Create permission change validation
- Implement contact information updates
- Add admin status change processing
- Create update notification system

**Task:** ID photo management
**Description:** Handle admin identification photo uploads and processing

- Implement secure photo upload with validation
- Add image processing for standard formats
- Create photo approval workflow
- Implement photo history and versioning
- Add photo quality and format requirements
- Create photo backup and recovery system

### 4.8 sendAdminActivationEmail

**Task:** Professional email template system
**Description:** Create branded, professional activation email templates

- Design responsive email template with organization branding
- Implement personalized activation message
- Add clear call-to-action buttons
- Create mobile-friendly email design
- Implement email tracking and analytics
- Add email preference management links

**Task:** Activation token security
**Description:** Secure token generation and management for admin activation

- Implement cryptographically secure token generation
- Add token expiration and validation logic
- Create token storage with proper indexing
- Implement token usage tracking
- Add token revocation capability
- Create token audit trail for security

### 4.9 completeAdminActivation

**Task:** Activation validation and processing
**Description:** Handle admin activation link processing securely

- Implement token validation with timing attack protection
- Add activation attempt rate limiting
- Create activation success confirmation page
- Implement activation failure handling with clear messaging
- Add activation analytics and reporting
- Create activation troubleshooting guide

**Task:** Post-activation workflow
**Description:** Complete admin setup after successful activation

- Generate secure temporary password
- Send password delivery email with instructions
- Create admin onboarding checklist
- Implement first login workflow
- Add admin welcome package delivery
- Create admin training resource access

### 4.10 resetAdminPassword

**Task:** Secure password reset system
**Description:** Administrative password reset with proper security measures

- Implement admin authorization for password resets
- Add password reset confirmation workflow
- Create secure temporary password generation
- Implement password reset notification system
- Add password reset audit logging
- Create bulk password reset capability

**Task:** Password delivery and security
**Description:** Secure delivery of new passwords to administrators

- Design secure password delivery email template
- Implement password expiration enforcement
- Add password complexity requirements
- Create password change tracking
- Implement password reuse prevention
- Add password security best practices guide

### 4.11 toggleAdminStatus

**Task:** Admin status management workflow
**Description:** Handle admin activation/deactivation with proper controls

- Implement status change authorization checks
- Add impact assessment for status changes
- Create status change confirmation dialogs
- Implement cascading effects for related data
- Add status change notification system
- Create status change audit trail

**Task:** Status change implications
**Description:** Manage system-wide effects of admin status changes

- Implement session termination for deactivated admins
- Add permission revocation for inactive admins
- Create grace period handling for temporary deactivation
- Implement automatic reactivation scheduling
- Add status change impact reporting
- Create status change rollback capability

### 4.12 softDeleteOrgAdmin

**Task:** Safe admin deletion process
**Description:** Implement soft delete with data preservation and security

- Add deletion authorization and approval workflow
- Implement soft delete with comprehensive audit trail
- Create data anonymization for deleted admin records
- Add deletion impact assessment and reporting
- Implement related data handling for deleted admins
- Create deletion confirmation with multiple verifications

**Task:** Admin deletion recovery system
**Description:** Provide recovery mechanisms for accidentally deleted admins

- Implement undelete functionality with time limits
- Add deleted admin browsing for super administrators
- Create deletion recovery workflow with approval
- Implement permanent deletion after retention period
- Add pre-deletion data export capability
- Create deletion impact analysis and reporting

## Function 5: Government Structure Management (Country → Province → District → LLG)

### 5.1 listGovUnits(level, parentId)

**Task:** Hierarchical data querying system
**Description:** Create efficient queries for multi-level government structure browsing

- Implement level-specific table queries (countries, provinces, districts, llgs tables)
- Add parent-child relationship filtering using country_id, province_id fields with proper indexing
- Create recursive query optimization for deep hierarchies
- Implement breadcrumb navigation data generation
- Add sibling and child count calculations
- Create cross-level search functionality

**Task:** Government unit listing interface
**Description:** Design intuitive hierarchical browsing interface

- Create tree-view navigation with expand/collapse functionality using dakoii_portal_template
- Implement card-based layout for government units
- Add search and filter controls for each hierarchy level
- Create geographic visualization integration using map_centre_gps, map_zoom fields
- Implement unit comparison functionality
- Add export capabilities for government structure data

**Task:** Performance optimization for large datasets
**Description:** Ensure efficient loading of government hierarchies

- Implement lazy loading for child nodes
- Add database query optimization with proper indexing on parent ID fields
- Create caching strategy for frequently accessed hierarchies
- Implement pagination for large government unit lists
- Add search result highlighting and relevance scoring
- Create preloading strategy for common navigation paths

### 5.2 showCreateGovUnitModal

**Task:** Dynamic modal creation interface
**Description:** Context-aware modal for creating government units at different levels

- Design responsive modal layout for government unit creation using dakoii_portal_template
- Implement level-specific form fields and validation (countries: iso2, iso3, name; provinces: prov_code, name; districts: dist_code, name; llgs: llg_code, name)
- Add parent unit selection with hierarchy browsing (country_id for provinces, province_id for districts, district_id for llgs)
- Create GeoJSON ID dropdown with search functionality
- Implement geographic coordinate input with map integration using map_centre_gps (POINT SRID 4326)
- Add government unit code generation and validation

**Task:** GeoJSON integration system
**Description:** Integrate geographic data selection into creation workflow

- Load GeoJSON file data for government unit selection
- Implement searchable dropdown for geographic IDs
- Add map preview for selected geographic areas
- Create geographic boundary validation
- Implement coordinate system conversion utilities (SRID 4326)
- Add geographic data caching for performance

**Task:** Form validation and user experience
**Description:** Comprehensive validation and smooth user experience

- Implement real-time validation for government unit data
- Add duplicate detection across hierarchy levels (unique iso2, iso3 for countries; unique codes within parent)
- Create form autosave for complex data entry
- Implement step-by-step creation wizard for complex units
- Add data import functionality from government sources
- Create template-based unit creation for efficiency

### 5.3 createGovUnit

**Task:** Government unit data validation
**Description:** Comprehensive validation for government unit creation

- Implement level-specific validation rules (name, code, boundaries)
- Add parent-child relationship validation
- Create geographic boundary conflict detection
- Implement government code format validation
- Add duplicate prevention across all hierarchy levels
- Create data integrity checks for government structures

**Task:** Multi-table insertion workflow
**Description:** Handle creation across different government level tables

- Implement dynamic table selection based on government level
- Add database transaction management for complex insertions
- Create audit trail for government unit creation
- Implement relationship establishment with parent units
- Add geographic data association and validation
- Create success confirmation with navigation options

**Task:** Government code generation and management
**Description:** Generate and manage unique codes for government units

- Implement level-specific code generation algorithms
- Add code format validation and standardization
- Create collision detection and resolution
- Implement manual code override capabilities
- Add code history tracking and audit
- Create code recycling for deleted units

### 5.4 viewGovUnit

**Task:** Comprehensive government unit profile
**Description:** Complete information display for government units

- Load government unit data with full hierarchy context
- Display geographic information and boundaries
- Show population and demographic statistics
- Create related government services listing
- Implement subordinate units display
- Add administrative contact information

**Task:** Geographic visualization integration
**Description:** Interactive maps and geographic data presentation

- Integrate interactive map display with unit boundaries
- Add satellite and terrain view options
- Implement boundary overlay with neighboring units
- Create zoom and pan functionality for detailed viewing
- Add geographic measurement tools
- Implement geographic data export functionality

**Task:** Government unit analytics and reporting
**Description:** Statistical analysis and reporting capabilities

- Calculate and display unit statistics (area, population, etc.)
- Create historical data visualization
- Implement comparative analysis with similar units
- Add performance metrics display
- Create demographic breakdown visualizations
- Implement administrative efficiency reporting

### 5.5 showEditGovUnitModal

**Task:** Context-sensitive edit modal system
**Description:** Flexible editing interface for different government unit types

- Create level-appropriate edit forms with proper field sets
- Implement geographic boundary editing with map interface
- Add contact information management
- Create administrative data editing capabilities
- Implement status and classification changes
- Add notes and documentation editing

**Task:** Geographic data editing tools
**Description:** Advanced tools for editing geographic information

- Implement boundary editing with map drawing tools
- Add coordinate system conversion and validation
- Create geographic area calculation tools
- Implement boundary conflict detection with neighboring units
- Add GPS coordinate input and validation
- Create geographic data import/export functionality

### 5.6 updateGovUnit

**Task:** Government unit update processing
**Description:** Handle updates to government unit information

- Implement selective field update processing
- Add validation for geographic and administrative changes
- Create change impact assessment for updates
- Implement approval workflow for significant changes
- Add update conflict resolution for concurrent edits
- Create comprehensive audit trail for all changes

**Task:** Geographic update validation
**Description:** Ensure geographic data integrity during updates

- Validate geographic boundary changes
- Add conflict detection with neighboring units
- Implement area calculation and validation
- Create coordinate system consistency checks
- Add geographic data backup before updates
- Implement rollback capability for geographic changes

### 5.7 toggleGovUnitStatus

**Task:** Government unit status management
**Description:** Handle activation/deactivation of government units

- Implement authorization checks for status changes
- Add impact assessment for unit deactivation
- Create cascade effect management for subordinate units
- Implement confirmation workflow for status changes
- Add status change notification system
- Create audit trail for all status modifications

**Task:** Administrative impact management
**Description:** Handle administrative implications of status changes

- Implement service suspension for deactivated units
- Add user access restriction for inactive units
- Create data archival process for deactivated units
- Implement reactivation workflow with data restoration
- Add status change reporting and analytics
- Create emergency activation procedures

### 5.8 softDeleteGovUnit

**Task:** Safe government unit deletion
**Description:** Implement soft delete with proper data preservation

- Add authorization and approval workflow for deletion
- Implement soft delete with comprehensive audit trail
- Create impact assessment for unit deletion
- Add cascade handling for subordinate units
- Implement data anonymization for deleted units
- Create deletion confirmation with multiple verifications

**Task:** Government structure integrity
**Description:** Maintain hierarchy integrity during deletions

- Implement orphan prevention for subordinate units
- Add automatic reparenting for child units
- Create structure validation after deletions
- Implement deletion impact analysis
- Add recovery procedures for accidental deletions
- Create permanent deletion with data archival

### 5.9 generateHierarchyChart

**Task:** Dynamic hierarchy visualization
**Description:** Generate interactive charts showing government structure relationships

- Implement recursive data fetching for complete hierarchies
- Create JSON data structure for chart libraries
- Add interactive navigation within hierarchy charts
- Implement zoom and pan functionality for large hierarchies
- Create customizable chart layouts and styling
- Add export functionality for hierarchy visualizations

**Task:** Chart data optimization
**Description:** Efficient data processing for large government hierarchies

- Implement lazy loading for large hierarchy branches
- Add data caching for frequently viewed hierarchies
- Create chart data compression for performance
- Implement progressive loading for complex structures
- Add search and filter capabilities within charts
- Create chart update mechanisms for data changes

### 5.10 loadGeoJSONIDDropdown

**Task:** GeoJSON data processing
**Description:** Server-side processing of GeoJSON files for dropdown population

- Implement efficient GeoJSON file parsing
- Create searchable dropdown option generation
- Add geographic feature extraction and indexing
- Implement data validation for GeoJSON integrity
- Create geographic ID standardization
- Add geographic data caching for performance

**Task:** Geographic data management
**Description:** Comprehensive management of geographic reference data

- Implement GeoJSON file upload and validation
- Add geographic data versioning and updates
- Create geographic feature search functionality
- Implement coordinate system validation
- Add geographic data backup and recovery
- Create geographic data export capabilities

## Function 6: Dakoii Users Management

### 6.1 listDakoiiUsers

**Task:** User listing with advanced filtering
**Description:** Comprehensive user management interface with search and filter capabilities

- Implement advanced search across dakoii_users fields (username, email, name, user_code)
- Add role-based filtering (admin, moderator, user enum values)
- Create status-based filtering (is_activated true/false)
- Implement date range filters for created_at and last_login_at
- Add sorting by multiple columns with proper indexing
- Create export functionality for user data

**Task:** User analytics and reporting
**Description:** User management analytics and insights

- Calculate user statistics and engagement metrics from dakoii_users table
- Create user activity timeline visualization using last_login_at
- Implement login frequency and patterns analysis
- Add user role distribution reporting (admin/moderator/user)
- Create user growth and churn analytics
- Implement user behavior pattern analysis

**Task:** Bulk user management operations
**Description:** Efficient bulk operations for user management

- Implement bulk status changes (is_activated toggle)
- Add bulk role assignment and modification (admin/moderator/user)
- Create bulk email functionality for user communications
- Implement bulk password reset capabilities using password_reset_token
- Add bulk user import from CSV/Excel
- Create bulk soft deletion with confirmation (deleted_at, deleted_by)

### 6.2 showCreateDakoiiUserForm

**Task:** User creation form design
**Description:** Comprehensive form for creating new Dakoii system users

- Design step-by-step user creation wizard using dakoii_portal_template
- Implement role selection interface (admin/moderator/user) with permission preview
- Add username availability checking in real-time against dakoii_users table
- Create email validation and duplicate detection
- Implement password policy enforcement for password_hash (Argon2id)
- Add user profile setup sections (name, id_photo_path)

**Task:** User onboarding workflow design
**Description:** Design smooth onboarding experience for new users

- Create user welcome package generation
- Implement role-based permission assignment (admin/moderator/user)
- Add user training resource assignment
- Create user mentor assignment system
- Implement user goal setting interface
- Add user progress tracking setup

### 6.3 createDakoiiUser

**Task:** User account creation workflow
**Description:** Complete user creation process with validation and setup

- Implement comprehensive user data validation using DakoiiUserModel allowedFields
- Add duplicate user detection across all identifier fields (username, email, user_code)
- Create user profile initialization with defaults (is_activated = false)
- Implement role-based permission assignment (admin/moderator/user)
- Add user activation workflow initiation using activation_token
- Create user creation audit trail using created_by, created_at

**Task:** User code generation system
**Description:** Generate unique alphanumeric codes for user identification

- Implement collision-resistant user_code generation (10-12 chars as per dakoii_users table)
- Add user code format validation and standardization
- Create code reservation system for concurrent requests
- Implement code recycling from soft-deleted users (deleted_at IS NOT NULL)
- Add manual code assignment capability
- Create code usage analytics and reporting

### 6.4 generateUserCode

**Task:** Advanced user code generation
**Description:** Robust algorithm for generating unique user identification codes

- Implement cryptographically secure random generation for user_code
- Add collision detection with dakoii_users table queries
- Create code format consistency enforcement (VARCHAR(12))
- Implement retry logic with maximum attempt limits
- Add code blacklist functionality for reserved codes
- Create code generation performance monitoring

**Task:** User code management system
**Description:** Comprehensive lifecycle management for user codes

- Implement code reservation and release mechanisms
- Add code expiration and cleanup processes
- Create code audit trail and history tracking
- Implement code validation and verification utilities
- Add code migration tools for system updates
- Create code analytics and usage reporting

### 6.5 viewDakoiiUserProfile

**Task:** Comprehensive user profile display
**Description:** Complete user information presentation with system context

- Load user data with role and permission information
- Display user activity timeline and login history
- Show user-created content and contributions
- Create user performance metrics display
- Implement user contact and communication history
- Add user preference and settings display

**Task:** User activity analytics
**Description:** Detailed analysis of user behavior and engagement

- Create user login pattern visualization
- Implement user activity heat maps
- Add user feature usage analytics
- Create user performance scoring system
- Implement user engagement trend analysis
- Add user productivity metrics display

### 6.6 showEditDakoiiUserModal

**Task:** Multi-context user editing modal
**Description:** Flexible modal system for different user editing scenarios

- Create ID photo upload modal with image cropping
- Implement role and permission modification interface
- Add password reset and security settings modal
- Create user status change confirmation modal
- Implement user preference editing interface
- Add user notes and administrative comments modal

**Task:** User security management interface
**Description:** Advanced security settings and management

- Implement two-factor authentication setup
- Add session management and device tracking
- Create security audit log display
- Implement password policy enforcement interface
- Add account lockout and security restriction controls
- Create security notification preferences

### 6.7 updateDakoiiUser

**Task:** User data update processing
**Description:** Handle user information updates with proper validation

- Implement selective field update processing
- Add change detection and audit logging
- Create role change validation and approval workflow
- Implement user permission modification handling
- Add user status change processing with notifications
- Create update conflict resolution for concurrent edits

**Task:** User security update handling
**Description:** Handle security-related user updates

- Implement secure password change processing
- Add two-factor authentication update handling
- Create security setting change validation
- Implement session invalidation for security updates
- Add security audit trail for sensitive changes
- Create security notification system for updates

### 6.8 sendUserActivationEmail

**Task:** Professional activation email system
**Description:** Branded, secure activation email delivery

- Design responsive activation email template
- Implement personalized activation message generation
- Add clear call-to-action with branded styling
- Create mobile-friendly email design
- Implement email delivery tracking and analytics
- Add email preference management links

**Task:** Activation security and tracking
**Description:** Secure activation token management and tracking

- Implement cryptographically secure token generation
- Add token expiration and validation logic
- Create activation attempt tracking and rate limiting
- Implement token usage analytics and monitoring
- Add activation troubleshooting and support tools
- Create activation success/failure reporting

### 6.9 completeUserActivation

**Task:** Secure activation processing
**Description:** Handle user activation with comprehensive security measures

- Implement token validation with timing attack protection
- Add activation attempt rate limiting and fraud detection
- Create activation success confirmation with next steps
- Implement activation failure handling with clear guidance
- Add activation analytics and success rate monitoring
- Create activation troubleshooting and support system

**Task:** Post-activation user setup
**Description:** Complete user onboarding after successful activation

- Generate secure temporary password with complexity requirements
- Send welcome package with system orientation materials
- Create user onboarding checklist and progress tracking
- Implement first login workflow with guided tour
- Add user training resource assignment
- Create user goal setting and expectation management

### 6.10 resetDakoiiUserPassword

**Task:** Administrative password reset system
**Description:** Secure password reset with proper authorization and audit

- Implement administrative authorization for password resets
- Add password reset reason tracking and documentation
- Create secure temporary password generation
- Implement password reset notification and confirmation
- Add password reset audit logging and reporting
- Create bulk password reset capability for emergencies

**Task:** Password security and delivery
**Description:** Secure password handling and delivery mechanisms

- Design secure password delivery email template
- Implement password expiration enforcement
- Add password complexity requirement validation
- Create password history tracking and reuse prevention
- Implement password security best practices guidance
- Add password change reminder and enforcement system

### 6.11 toggleDakoiiUserStatus

**Task:** User status management workflow
**Description:** Handle user activation/deactivation with proper controls

- Implement authorization checks for status changes
- Add impact assessment for user deactivation
- Create status change confirmation with reason tracking
- Implement cascading effects for user-created content
- Add status change notification system for affected users
- Create status change audit trail and reporting

**Task:** User status implications management
**Description:** Handle system-wide effects of user status changes

- Implement session termination for deactivated users
- Add permission revocation for inactive users
- Create grace period handling for temporary deactivation
- Implement automatic reactivation scheduling
- Add user status change impact analysis and reporting
- Create status change rollback capability for errors

### 6.12 softDeleteDakoiiUser

**Task:** Safe user deletion process
**Description:** Implement soft delete with comprehensive data preservation

- Add deletion authorization and multi-level approval
- Implement soft delete with comprehensive audit trail
- Create data anonymization for deleted user records
- Add deletion impact assessment for user-created content
- Implement related data handling and preservation
- Create deletion confirmation with security verification

**Task:** User deletion recovery and cleanup
**Description:** Recovery mechanisms and data cleanup for deleted users

- Implement undelete functionality with time-limited recovery
- Add deleted user browsing for system administrators
- Create deletion recovery workflow with approval process
- Implement permanent deletion after retention period
- Add pre-deletion data export and backup
- Create deletion impact analysis and cleanup reporting

## Function 7: Shared Utility Helpers

### 7.1 emailTempPassword(recipient, tempPwd)

**Task:** Professional password email template
**Description:** Create branded, secure temporary password delivery system

- Design responsive email template with security messaging using CI4 email library
- Implement personalized greeting and instructions
- Add clear password display with copy functionality
- Create security warnings and best practices guidance
- Implement branded styling consistent with dakoii theme
- Add expiration time and usage instructions

**Task:** Email delivery and tracking
**Description:** Reliable email delivery with comprehensive tracking

- Implement robust email delivery with retry logic using CI4 email service
- Add email delivery status tracking and confirmation
- Create email bounce handling and error reporting
- Implement email analytics and delivery metrics
- Add email preference management and opt-out handling
- Create email delivery audit trail and logging

### 7.2 hashPasswordArgon2(password)

**Task:** Secure password hashing implementation
**Description:** Industry-standard password hashing with Argon2id

- Implement Argon2id hashing with optimal parameters using PHP password_hash()
- Add salt generation and management (automatic with password_hash)
- Create hash verification and validation utilities
- Implement hash migration for algorithm updates
- Add performance monitoring for hashing operations
- Create hash strength validation and testing

**Task:** Password security optimization
**Description:** Optimize password security parameters and performance

- Configure optimal Argon2id memory and time costs for XAMPP environment
- Implement adaptive hashing based on system performance
- Add hash validation and integrity checking
- Create password hash audit and compliance reporting
- Implement hash algorithm migration utilities
- Add password security best practices documentation

### 7.3 verifyPassword(password, hash)

**Task:** Secure password verification
**Description:** Timing-attack resistant password verification

- Implement constant-time password verification using PHP password_verify()
- Add hash format validation and compatibility checking
- Create verification attempt tracking and rate limiting
- Implement verification failure logging and analysis
- Add password verification performance monitoring
- Create verification audit trail and security reporting

**Task:** Password verification security
**Description:** Advanced security measures for password verification

- Implement brute force protection and account lockout
- Add suspicious activity detection and alerting
- Create verification attempt pattern analysis
- Implement geographic verification anomaly detection
- Add verification security audit and compliance
- Create verification failure investigation tools

### 7.4 generateUniqueCode(length, charset)

**Task:** Flexible code generation system
**Description:** Configurable unique code generation for various use cases

- Implement customizable length and character set options for org_code (5 chars) and user_code (8-12 chars)
- Add collision detection with configurable retry limits
- Create code format validation and standardization
- Implement code generation performance optimization
- Add code uniqueness verification across multiple tables (organizations, users, dakoii_users)
- Create code generation analytics and monitoring

**Task:** Code management and validation
**Description:** Comprehensive code lifecycle management

- Implement code reservation and expiration handling
- Add code blacklist and reserved code management
- Create code format standardization and validation
- Implement code recycling and cleanup processes for soft-deleted records
- Add code usage tracking and analytics
- Create code audit trail and history management

### 7.5 handleFileUpload(field, path)

**Task:** Secure file upload processing
**Description:** Comprehensive file upload handling with security validation

- Implement file type validation with MIME type checking for logo_path, wallpaper_path, profile_photo_path, id_photo_path
- Add file size limits and quota management for XAMPP environment
- Create secure file storage with proper permissions in public/uploads directory
- Implement virus scanning and malware detection
- Add file content validation and sanitization
- Create file upload audit trail and logging

**Task:** File management and optimization
**Description:** Efficient file handling and storage management

- Implement file compression and optimization for web delivery
- Add duplicate file detection and deduplication
- Create file versioning and backup systems
- Implement file cleanup and retention policies
- Add file access control and security measures
- Create file usage analytics and storage reporting

### 7.6 softDelete(table, id, userId)

**Task:** Generic soft delete implementation
**Description:** Standardized soft delete functionality across all entities

- Implement configurable soft delete with deleted_at timestamp and deleted_by user tracking
- Add soft delete validation and authorization checking
- Create cascade soft delete for related records
- Implement soft delete audit trail and logging
- Add soft delete recovery and restoration utilities
- Create soft delete cleanup and purging processes

**Task:** Soft delete management system
**Description:** Comprehensive soft delete lifecycle management

- Implement soft delete browsing and search functionality (WHERE deleted_at IS NOT NULL)
- Add bulk soft delete operations with confirmation
- Create soft delete impact analysis and reporting
- Implement automatic purging after retention periods
- Add soft delete recovery workflow with approval (SET deleted_at = NULL, deleted_by = NULL)
- Create soft delete audit and compliance reporting

### 7.7 recordAuditTrail(userId, action, entity, entityId)

**Task:** Comprehensive audit system
**Description:** Complete audit trail recording for all system activities

- Implement detailed audit record creation using created_by, updated_by, deleted_by fields
- Add automatic audit trail generation for all CRUD operations
- Create audit record categorization and tagging
- Implement audit data compression and archival
- Add audit search and filtering capabilities using created_at, updated_at timestamps
- Create audit analytics and pattern analysis

**Task:** Audit reporting and compliance
**Description:** Audit trail analysis and compliance reporting

- Implement audit report generation with customizable formats
- Add audit trail visualization and timeline display using timestamp fields
- Create compliance reporting for regulatory requirements
- Implement audit data export and backup systems
- Add audit integrity verification and validation
- Create audit analytics dashboard and insights

### 7.8 paginate(queryBuilder, perPage)

**Task:** Advanced pagination system
**Description:** Flexible pagination with performance optimization

- Implement efficient pagination using CI4 Pager with query optimization
- Add cursor-based pagination for large datasets
- Create pagination caching and performance monitoring
- Implement pagination state management and bookmarking
- Add pagination analytics and usage tracking
- Create responsive pagination controls with accessibility using dakoii theme

**Task:** Pagination user experience
**Description:** Enhanced pagination interface and functionality

- Use standard CI4 pagination (avoid infinite scroll, follow project rules)
- Add pagination jump-to-page functionality
- Create pagination size options and user preferences (10, 25, 50, 100)
- Implement pagination search within results
- Add pagination export functionality for current page/all
- Create pagination keyboard navigation and shortcuts

### 7.9 sendEmail(to, subject, view, data)

**Task:** Professional email system
**Description:** Comprehensive email delivery system with templates

- Implement HTML and plain text email template engine using CI4 email library
- Add email personalization and dynamic content
- Create email delivery queue and scheduling system for XAMPP environment
- Implement email tracking and analytics
- Add email bounce handling and list management
- Create email compliance and anti-spam measures

**Task:** Email delivery optimization
**Description:** Reliable and efficient email delivery infrastructure

- Implement SMTP configuration for XAMPP/local development
- Add email delivery rate limiting and throttling
- Create email reputation management and monitoring
- Implement email delivery retry logic with exponential backoff
- Add email delivery analytics and performance monitoring
- Create email security and encryption handling for production deployment