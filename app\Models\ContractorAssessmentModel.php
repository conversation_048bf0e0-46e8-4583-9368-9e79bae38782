<?php

namespace App\Models;

/**
 * Contractor Assessment Model
 * 
 * Manages contractor performance assessments including
 * quality, timeliness, communication scores and recommendations.
 */
class ContractorAssessmentModel extends BaseModel
{
    protected $table      = 'contractor_assessments';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'contractor_id', 'project_id', 'milestone_id', 'assessment_date',
        'quality_score', 'timeliness_score', 'communication_score', 'overall_score',
        'strengths', 'weaknesses', 'recommendations', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'contractor_id'       => 'required|integer',
        'project_id'          => 'required|integer',
        'milestone_id'        => 'integer',
        'assessment_date'     => 'required|valid_date',
        'quality_score'       => 'decimal|greater_than_equal_to[1.0]|less_than_equal_to[5.0]',
        'timeliness_score'    => 'decimal|greater_than_equal_to[1.0]|less_than_equal_to[5.0]',
        'communication_score' => 'decimal|greater_than_equal_to[1.0]|less_than_equal_to[5.0]',
        'overall_score'       => 'decimal|greater_than_equal_to[1.0]|less_than_equal_to[5.0]'
    ];
    
    protected $validationMessages = [
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'assessment_date' => [
            'required' => 'Assessment date is required'
        ]
    ];
    
    /**
     * Get assessments by contractor
     */
    public function getByContractor(int $contractorId): array
    {
        return $this->select('contractor_assessments.*, projects.title as project_title, projects.pro_code, project_milestones.title as milestone_title')
                   ->join('projects', 'projects.id = contractor_assessments.project_id')
                   ->join('project_milestones', 'project_milestones.id = contractor_assessments.milestone_id', 'left')
                   ->where('contractor_assessments.contractor_id', $contractorId)
                   ->orderBy('contractor_assessments.assessment_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get assessments by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->select('contractor_assessments.*, contractors.name as contractor_name, contractors.contractor_code, project_milestones.title as milestone_title')
                   ->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                   ->join('project_milestones', 'project_milestones.id = contractor_assessments.milestone_id', 'left')
                   ->where('contractor_assessments.project_id', $projectId)
                   ->orderBy('contractor_assessments.assessment_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get assessments by milestone
     */
    public function getByMilestone(int $milestoneId): array
    {
        return $this->select('contractor_assessments.*, contractors.name as contractor_name, contractors.contractor_code, projects.title as project_title')
                   ->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                   ->join('projects', 'projects.id = contractor_assessments.project_id')
                   ->where('contractor_assessments.milestone_id', $milestoneId)
                   ->orderBy('contractor_assessments.assessment_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Calculate overall score from individual scores
     */
    public function calculateOverallScore(float $qualityScore, float $timelinessScore, float $communicationScore): float
    {
        // Equal weight for all three components
        return round(($qualityScore + $timelinessScore + $communicationScore) / 3, 1);
    }
    
    /**
     * Get contractor performance summary
     */
    public function getPerformanceSummary(int $contractorId): array
    {
        $assessments = $this->where('contractor_id', $contractorId)->findAll();
        
        if (empty($assessments)) {
            return [
                'total_assessments' => 0,
                'average_scores' => null,
                'latest_assessment' => null,
                'performance_trend' => null
            ];
        }
        
        $summary = [
            'total_assessments' => count($assessments),
            'average_scores' => [
                'quality' => 0,
                'timeliness' => 0,
                'communication' => 0,
                'overall' => 0
            ],
            'latest_assessment' => null,
            'performance_trend' => []
        ];
        
        $totalQuality = $totalTimeliness = $totalCommunication = $totalOverall = 0;
        $validScores = 0;
        
        foreach ($assessments as $assessment) {
            if ($assessment['quality_score'] && $assessment['timeliness_score'] && $assessment['communication_score']) {
                $totalQuality += $assessment['quality_score'];
                $totalTimeliness += $assessment['timeliness_score'];
                $totalCommunication += $assessment['communication_score'];
                $totalOverall += $assessment['overall_score'];
                $validScores++;
            }
        }
        
        if ($validScores > 0) {
            $summary['average_scores'] = [
                'quality' => round($totalQuality / $validScores, 1),
                'timeliness' => round($totalTimeliness / $validScores, 1),
                'communication' => round($totalCommunication / $validScores, 1),
                'overall' => round($totalOverall / $validScores, 1)
            ];
        }
        
        // Get latest assessment
        $latest = $this->where('contractor_id', $contractorId)
                      ->orderBy('assessment_date', 'DESC')
                      ->first();
        $summary['latest_assessment'] = $latest;
        
        // Performance trend (last 6 assessments)
        $recent = $this->where('contractor_id', $contractorId)
                      ->orderBy('assessment_date', 'DESC')
                      ->limit(6)
                      ->findAll();
        
        $summary['performance_trend'] = array_reverse($recent);
        
        return $summary;
    }
    
    /**
     * Get top performing contractors
     */
    public function getTopPerformers(int $orgId, int $limit = 10): array
    {
        return $this->select('contractor_assessments.contractor_id, contractors.name as contractor_name, contractors.contractor_code, AVG(contractor_assessments.overall_score) as avg_score, COUNT(*) as assessment_count')
                   ->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->where('contractor_assessments.overall_score IS NOT NULL')
                   ->groupBy('contractor_assessments.contractor_id')
                   ->having('assessment_count >=', 3) // At least 3 assessments
                   ->orderBy('avg_score', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
    
    /**
     * Get assessment statistics by organization
     */
    public function getAssessmentStatistics(int $orgId): array
    {
        $stats = [];
        
        // Total assessments
        $stats['total'] = $this->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->countAllResults();
        
        // Average scores
        $averages = $this->select('AVG(quality_score) as avg_quality, AVG(timeliness_score) as avg_timeliness, AVG(communication_score) as avg_communication, AVG(overall_score) as avg_overall')
                        ->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                        ->where('contractors.org_id', $orgId)
                        ->where('overall_score IS NOT NULL')
                        ->first();
        
        $stats['averages'] = [
            'quality' => $averages ? round($averages['avg_quality'], 1) : 0,
            'timeliness' => $averages ? round($averages['avg_timeliness'], 1) : 0,
            'communication' => $averages ? round($averages['avg_communication'], 1) : 0,
            'overall' => $averages ? round($averages['avg_overall'], 1) : 0
        ];
        
        // Score distribution
        $scoreRanges = [
            'excellent' => ['min' => 4.5, 'max' => 5.0],
            'good' => ['min' => 3.5, 'max' => 4.4],
            'satisfactory' => ['min' => 2.5, 'max' => 3.4],
            'poor' => ['min' => 1.0, 'max' => 2.4]
        ];
        
        $stats['score_distribution'] = [];
        foreach ($scoreRanges as $range => $bounds) {
            $count = $this->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                         ->where('contractors.org_id', $orgId)
                         ->where('overall_score >=', $bounds['min'])
                         ->where('overall_score <=', $bounds['max'])
                         ->countAllResults();
            $stats['score_distribution'][$range] = $count;
        }
        
        return $stats;
    }
    
    /**
     * Get recent assessments for organization
     */
    public function getRecentAssessments(int $orgId, int $limit = 10): array
    {
        return $this->select('contractor_assessments.*, contractors.name as contractor_name, contractors.contractor_code, projects.title as project_title, projects.pro_code')
                   ->join('contractors', 'contractors.id = contractor_assessments.contractor_id')
                   ->join('projects', 'projects.id = contractor_assessments.project_id')
                   ->where('contractors.org_id', $orgId)
                   ->orderBy('contractor_assessments.assessment_date', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
    
    /**
     * Check if assessment exists for contractor and milestone
     */
    public function assessmentExists(int $contractorId, int $projectId, ?int $milestoneId = null): bool
    {
        $query = $this->where('contractor_id', $contractorId)
                     ->where('project_id', $projectId);
        
        if ($milestoneId) {
            $query = $query->where('milestone_id', $milestoneId);
        } else {
            $query = $query->where('milestone_id IS NULL');
        }
        
        return $query->countAllResults() > 0;
    }
}
