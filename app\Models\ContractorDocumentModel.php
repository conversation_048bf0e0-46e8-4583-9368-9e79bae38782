<?php

namespace App\Models;

/**
 * Contractor Document Model
 * 
 * Manages contractor documents including registration, licenses,
 * certifications, and compliance documents with verification tracking.
 */
class ContractorDocumentModel extends BaseModel
{
    protected $table      = 'contractor_documents';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'contractor_id', 'doc_type', 'doc_number', 'doc_title', 'doc_path',
        'issue_date', 'expiry_date', 'is_verified', 'verified_by', 'verified_at',
        'notes', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'contractor_id' => 'required|integer',
        'doc_type'      => 'required|in_list[registration,tax,license,certification,insurance,compliance,other]',
        'doc_number'    => 'max_length[50]',
        'doc_title'     => 'required|max_length[150]',
        'doc_path'      => 'required|max_length[255]',
        'issue_date'    => 'valid_date',
        'expiry_date'   => 'valid_date',
        'is_verified'   => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'doc_type' => [
            'required' => 'Document type is required'
        ],
        'doc_title' => [
            'required' => 'Document title is required'
        ],
        'doc_path' => [
            'required' => 'Document file is required'
        ]
    ];
    
    /**
     * Get documents by contractor
     */
    public function getByContractor(int $contractorId, ?string $docType = null): array
    {
        $query = $this->where('contractor_id', $contractorId);
        
        if ($docType) {
            $query = $query->where('doc_type', $docType);
        }
        
        return $query->orderBy('doc_type', 'ASC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get documents with verification details
     */
    public function getWithVerificationDetails(int $contractorId): array
    {
        return $this->select('contractor_documents.*, users.name as verified_by_name')
                   ->join('users', 'users.id = contractor_documents.verified_by', 'left')
                   ->where('contractor_documents.contractor_id', $contractorId)
                   ->orderBy('contractor_documents.doc_type', 'ASC')
                   ->orderBy('contractor_documents.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get expiring documents
     */
    public function getExpiringDocuments(int $contractorId, int $daysAhead = 30): array
    {
        $futureDate = date('Y-m-d', strtotime("+{$daysAhead} days"));
        
        return $this->where('contractor_id', $contractorId)
                   ->where('expiry_date IS NOT NULL')
                   ->where('expiry_date <=', $futureDate)
                   ->where('expiry_date >=', date('Y-m-d'))
                   ->orderBy('expiry_date', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get expired documents
     */
    public function getExpiredDocuments(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->where('expiry_date IS NOT NULL')
                   ->where('expiry_date <', date('Y-m-d'))
                   ->orderBy('expiry_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Verify document
     */
    public function verifyDocument(int $id, int $verifiedBy, ?string $notes = null): bool
    {
        $data = [
            'is_verified' => 1,
            'verified_by' => $verifiedBy,
            'verified_at' => date('Y-m-d H:i:s'),
            'notes' => $notes
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * Unverify document
     */
    public function unverifyDocument(int $id, ?string $notes = null): bool
    {
        $data = [
            'is_verified' => 0,
            'verified_by' => null,
            'verified_at' => null,
            'notes' => $notes
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * Get document statistics by contractor
     */
    public function getDocumentStatistics(int $contractorId): array
    {
        $stats = [];
        
        // Total documents
        $stats['total'] = $this->where('contractor_id', $contractorId)->countAllResults();
        
        // Verified documents
        $stats['verified'] = $this->where('contractor_id', $contractorId)
                                 ->where('is_verified', 1)
                                 ->countAllResults();
        
        // Expiring documents (next 30 days)
        $stats['expiring'] = count($this->getExpiringDocuments($contractorId, 30));
        
        // Expired documents
        $stats['expired'] = count($this->getExpiredDocuments($contractorId));
        
        // By document type
        $typeCounts = $this->select('doc_type, COUNT(*) as count')
                          ->where('contractor_id', $contractorId)
                          ->groupBy('doc_type')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'doc_type');
        
        return $stats;
    }
    
    /**
     * Check if contractor has required documents
     */
    public function hasRequiredDocuments(int $contractorId, array $requiredTypes = ['registration', 'tax']): array
    {
        $existing = $this->select('doc_type')
                        ->where('contractor_id', $contractorId)
                        ->where('is_verified', 1)
                        ->whereIn('doc_type', $requiredTypes)
                        ->findAll();
        
        $existingTypes = array_column($existing, 'doc_type');
        $missing = array_diff($requiredTypes, $existingTypes);
        
        return [
            'has_all' => empty($missing),
            'missing' => $missing,
            'existing' => $existingTypes
        ];
    }
    
    /**
     * Get documents by type across all contractors in organization
     */
    public function getByTypeForOrganization(int $orgId, string $docType): array
    {
        return $this->select('contractor_documents.*, contractors.name as contractor_name, contractors.contractor_code')
                   ->join('contractors', 'contractors.id = contractor_documents.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->where('contractor_documents.doc_type', $docType)
                   ->orderBy('contractors.name', 'ASC')
                   ->orderBy('contractor_documents.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get organization-wide document expiry alerts
     */
    public function getOrganizationExpiryAlerts(int $orgId, int $daysAhead = 30): array
    {
        $futureDate = date('Y-m-d', strtotime("+{$daysAhead} days"));
        
        return $this->select('contractor_documents.*, contractors.name as contractor_name, contractors.contractor_code')
                   ->join('contractors', 'contractors.id = contractor_documents.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->where('contractor_documents.expiry_date IS NOT NULL')
                   ->where('contractor_documents.expiry_date <=', $futureDate)
                   ->where('contractor_documents.expiry_date >=', date('Y-m-d'))
                   ->orderBy('contractor_documents.expiry_date', 'ASC')
                   ->findAll();
    }
}
