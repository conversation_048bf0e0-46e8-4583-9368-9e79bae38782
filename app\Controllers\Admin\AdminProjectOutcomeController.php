<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectOutcomeModel;

/**
 * Admin Project Outcomes Controller
 * 
 * Handles CRUD operations for project outcomes in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectOutcomeController extends BaseController
{
    protected $projectModel;
    protected $projectOutcomeModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectOutcomeModel = new ProjectOutcomeModel();
    }

    /**
     * Show outcomes list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get outcomes for this project
        $outcomes = $this->projectOutcomeModel->getByProject($projectId);

        // Get outcome statistics
        $outcomeStats = $this->projectOutcomeModel->getOutcomeStatistics($projectId);

        $data = [
            'title' => 'Project Outcomes - PROMIS Admin',
            'page_title' => 'Project Outcomes',
            'project' => $project,
            'outcomes' => $outcomes,
            'outcomeStats' => $outcomeStats
        ];

        return view('admin/admin_projects_outcomes_list', $data);
    }

    /**
     * Show create outcome form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Create Project Outcome - PROMIS Admin',
            'page_title' => 'Create Project Outcome',
            'project' => $project
        ];

        return view('admin/admin_projects_outcomes_create', $data);
    }

    /**
     * Store outcome - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'outcome_text' => 'required|max_length[255]',
            'quantity' => 'required|decimal|greater_than[0]',
            'unit' => 'permit_empty|max_length[50]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data
        $outcomeData = [
            'project_id' => $projectId,
            'outcome_text' => $this->request->getPost('outcome_text'),
            'quantity' => $this->request->getPost('quantity'),
            'unit' => $this->request->getPost('unit'),
            'created_by' => $adminUserId
        ];

        try {
            $outcomeId = $this->projectOutcomeModel->insert($outcomeData);

            if ($outcomeId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                               ->with('success', 'Project outcome created successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectOutcomeModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating project outcome: ' . $e->getMessage());
        }
    }

    /**
     * Show edit outcome form - GET request
     */
    public function edit($projectId, $outcomeId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get outcome
        $outcome = $this->projectOutcomeModel->where('id', $outcomeId)
                                            ->where('project_id', $projectId)
                                            ->where('deleted_at', null)
                                            ->first();

        if (!$outcome) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                           ->with('error', 'Project outcome not found.');
        }

        $data = [
            'title' => 'Edit Project Outcome - PROMIS Admin',
            'page_title' => 'Edit Project Outcome',
            'project' => $project,
            'outcome' => $outcome
        ];

        return view('admin/admin_projects_outcomes_edit', $data);
    }

    /**
     * Update outcome - POST request
     */
    public function update($projectId, $outcomeId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get outcome
        $outcome = $this->projectOutcomeModel->where('id', $outcomeId)
                                            ->where('project_id', $projectId)
                                            ->where('deleted_at', null)
                                            ->first();

        if (!$outcome) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                           ->with('error', 'Project outcome not found.');
        }

        // Validation rules
        $rules = [
            'outcome_text' => 'required|max_length[255]',
            'quantity' => 'required|decimal|greater_than[0]',
            'unit' => 'permit_empty|max_length[50]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data
        $outcomeData = [
            'outcome_text' => $this->request->getPost('outcome_text'),
            'quantity' => $this->request->getPost('quantity'),
            'unit' => $this->request->getPost('unit'),
            'updated_by' => $adminUserId
        ];

        try {
            $result = $this->projectOutcomeModel->update($outcomeId, $outcomeData);

            if ($result) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                               ->with('success', 'Project outcome updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectOutcomeModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating project outcome: ' . $e->getMessage());
        }
    }

    /**
     * Delete outcome - POST request
     */
    public function delete($projectId, $outcomeId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get outcome
        $outcome = $this->projectOutcomeModel->where('id', $outcomeId)
                                            ->where('project_id', $projectId)
                                            ->where('deleted_at', null)
                                            ->first();

        if (!$outcome) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                           ->with('error', 'Project outcome not found.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectOutcomeModel->update($outcomeId, ['deleted_by' => $adminUserId]);
            $this->projectOutcomeModel->delete($outcomeId);

            return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                           ->with('success', 'Project outcome deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/outcomes'))
                           ->with('error', 'Error deleting project outcome: ' . $e->getMessage());
        }
    }

    /**
     * Get outcomes data for project show view
     * This method is called by AdminProjectController to get outcomes data for the project profile
     */
    public function getOutcomesForProjectShow($projectId)
    {
        // Get outcomes for this project
        $outcomes = $this->projectOutcomeModel->getByProject($projectId);

        // Get outcome statistics
        $outcomeStats = $this->projectOutcomeModel->getOutcomeStatistics($projectId);

        return [
            'outcomes' => $outcomes,
            'outcomeStats' => $outcomeStats
        ];
    }
}
