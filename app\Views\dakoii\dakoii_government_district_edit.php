<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Districts
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">✏️</span>
            Edit District
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Update the district information in the government structure hierarchy.
        </p>
    </div>

    <!-- Validation Errors -->
    <?php if (isset($validation) && !empty($validation)): ?>
        <div style="margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: var(--radius-md);">
            <h4 style="margin: 0 0 var(--spacing-sm) 0; color: #dc3545;">Validation Errors</h4>
            <ul style="margin: 0; padding-left: var(--spacing-md); color: #dc3545;">
                <?php foreach ($validation as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Edit District Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">District Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Update the district information below.</p>

            <form method="POST" action="<?= base_url('dakoii/government/districts/'.$district['id'].'/edit') ?>" class="district-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="province_id" class="form-label">Province *</label>
                        <select id="province_id" name="province_id" class="form-input" required
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select Province</option>
                            <?php if (isset($provinces) && !empty($provinces)): ?>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>" <?= ($district['province_id'] == $province['id']) ? 'selected' : '' ?>
                                            style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">
                                        <?= esc($province['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Select the province this district belongs to
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="name" class="form-label">District Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               value="<?= esc($district['name']) ?>" required
                               placeholder="Enter the full district name">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Official name of the district as it should appear in the system
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="dist_code" class="form-label">District Code *</label>
                        <input type="text" id="dist_code" name="dist_code" class="form-input"
                               value="<?= esc($district['dist_code']) ?>" required
                               placeholder="e.g., SFD, PMD, LAE"
                               style="text-transform: uppercase;">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Short code or abbreviation for the district
                        </small>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>

                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <select id="geojson_id" name="geojson_id" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select District Boundary (Optional)</option>
                            <?php
                            // Load district options from JSON
                            $jsonPath = FCPATH . 'map_jsons/png_dist_boundaries_2011.json';
                            if (file_exists($jsonPath)) {
                                $jsonData = json_decode(file_get_contents($jsonPath), true);
                                if (isset($jsonData['features'])) {
                                    $districts = [];
                                    foreach ($jsonData['features'] as $feature) {
                                        if (isset($feature['properties']['GEOCODE']) && isset($feature['properties']['DISTNAME'])) {
                                            $districts[] = [
                                                'id' => $feature['properties']['GEOCODE'],
                                                'name' => $feature['properties']['DISTNAME']
                                            ];
                                        }
                                    }
                                    // Sort by name
                                    usort($districts, function($a, $b) {
                                        return strcmp($a['name'], $b['name']);
                                    });

                                    foreach ($districts as $districtOption) {
                                        $selected = (($district['geojson_id'] ?? null) == $districtOption['id']) ? 'selected' : '';
                                        echo '<option value="' . esc($districtOption['id']) . '" ' . $selected . ' style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">' . esc($districtOption['name']) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Select the corresponding boundary from map data
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input"
                                   value="<?= esc($district['map_centre_gps'] ?? '') ?>"
                                   placeholder="e.g., -6.314993,143.95555">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Latitude, Longitude coordinates
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input"
                                   value="<?= esc($district['map_zoom'] ?? '10') ?>" min="1" max="20">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Default zoom level for map display (1-20)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Update District
                    </button>
                    <a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Help Information -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>District Update Guide</h3>
            </div>
            <div class="info-content">
                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Required Fields</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);">Province: Parent province</li>
                        <li style="margin-bottom: var(--spacing-xs);">District Name: Official name</li>
                        <li>District Code: Short abbreviation</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">District Code Examples</h4>
                    <div style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                        <div>South Fly District: SFD</div>
                        <div>Port Moresby District: PMD</div>
                        <div>Lae District: LAE</div>
                        <div>Mount Hagen District: MHD</div>
                    </div>
                </div>

                <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                    <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                        <div style="font-size: 1.25rem;">💡</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">Tip</h4>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                                Make sure to update all related information when changing the parent province of a district.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Form styling consistency */
.form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.info-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.info-header {
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.info-content {
    padding: var(--spacing-lg);
}
</style>
<?= $this->endSection() ?> 