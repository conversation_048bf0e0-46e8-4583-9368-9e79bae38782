<?php

namespace App\Traits;

use Config\Services;
use App\Models\AuditLogModel;

/**
 * Unified Auditable Trait
 *
 * This trait provides automatic audit logging for all portals in the PROMIS application.
 * It automatically detects the portal context and logs activities accordingly.
 */
trait Auditable
{
    protected bool $auditSkip = false;   // allows manual opt-out per call

    // Automatically attached in Model::$beforeInsert / $beforeUpdate / $beforeDelete
    protected function audit(string $action, array $data): array
    {
        if ($this->auditSkip) {
            return $data;
        }   // skip when needed

        // Debug: Log the data structure to understand what we're working with
        log_message('debug', "Auditable: Action '{$action}' - Data structure: " . print_r($data, true));

        $portalContext = $this->detectPortalContext();
        if (!$portalContext) {
            return $data; // No valid portal context found
        }

        $request   = Services::request();
        $session   = session();
        $pkField   = $this->primaryKey;

        // Debug: Log the primary key extraction
        log_message('debug', "Auditable: pkField = {$pkField}");
        log_message('debug', "Auditable: data['id'] = " . (isset($data['id']) ? print_r($data['id'], true) : 'NOT SET'));
        log_message('debug', "Auditable: data['data'][{$pkField}] = " . (isset($data['data'][$pkField]) ? print_r($data['data'][$pkField], true) : 'NOT SET'));

        $pk        = $data['id'] ?? $data['data'][$pkField] ?? null;

        // Debug: Log the final pk value
        log_message('debug', "Auditable: Final pk = " . print_r($pk, true));

        // Handle array primary keys by converting to string safely
        if (is_array($pk)) {
            log_message('debug', "Auditable: Primary key is an array, converting to string");
            $pk = is_array($pk) ? implode(',', $pk) : (string) $pk;
        }

        // Get user information based on portal context
        $userInfo = $this->getUserInfo($portalContext, $session);

        // Get project context if available
        $projectInfo = $this->getProjectContext($session);

        $old = null;
        $new = null;

        if ($action === 'update') {
            $old = json_encode($this->asArray()
                    ->where($pkField, $pk)
                    ->first(), JSON_UNESCAPED_UNICODE);
            // Debug: Log the exact data structure before processing
            log_message('debug', "Auditable: Raw data structure for update: " . print_r($data, true));
            // Safely handle arrays in data before JSON encoding
            $safeData = $this->sanitizeDataForJson($data['data']);
            $new = json_encode($safeData, JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'create') {
            // Debug: Log the exact data structure before processing
            log_message('debug', "Auditable: Raw data structure for create: " . print_r($data, true));
            // Safely handle arrays in data before JSON encoding
            $safeData = $this->sanitizeDataForJson($data['data']);
            $new = json_encode($safeData, JSON_UNESCAPED_UNICODE);
        } elseif ($action === 'delete') {
            $old = json_encode($this->asArray()->where($pkField, $pk)->first(), JSON_UNESCAPED_UNICODE);
        }

        (new AuditLogModel())->insert([
            'table_name'        => $this->table,
            'primary_key'       => (string) $pk,
            'action'            => $action,
            'old_data'          => $old,
            'new_data'          => $new,
            'user_id'           => $userInfo['user_id'],
            'username'          => $userInfo['username'],
            'user_type'         => $userInfo['user_type'],
            'user_full_name'    => $userInfo['user_full_name'],
            'organization_id'   => $userInfo['organization_id'],
            'organization_name' => $userInfo['organization_name'],
            'organization_type' => $userInfo['organization_type'],
            'project_id'        => $projectInfo['project_id'],
            'project_title'     => $projectInfo['project_title'],
            'portal'            => $portalContext['portal'],
            'module'            => $portalContext['module'],
            'ip_address'        => $request->getIPAddress(),
            'user_agent'        => is_cli() ? 'CLI' : $request->getUserAgent()->getAgentString(),
            'session_id'        => $session->session_id ?? null,
            'request_url'       => current_url(),
            'description'       => $this->generateDescription($action, $pk),
            'created_at'        => date('Y-m-d H:i:s'),
        ]);

        return $data; // MUST return to keep the normal pipeline flowing
    }

    /**
     * Detect which portal context we're currently in
     */
    protected function detectPortalContext(): ?array
    {
        $session = session();
        $request = Services::request();
        $uri = $request->getUri()->getPath();

        // Check for Dakoii portal
        if ($session->get('dakoii_user_id') || strpos($uri, '/dakoii/') !== false) {
            return [
                'portal' => 'dakoii',
                'module' => $this->extractModuleFromUrl($uri, 'dakoii')
            ];
        }

        // Check for Admin portal
        if ($session->get('admin_user_id') || strpos($uri, '/admin/') !== false) {
            return [
                'portal' => 'admin',
                'module' => $this->extractModuleFromUrl($uri, 'admin')
            ];
        }

        // Check for Monitor portal
        if ($session->get('monitor_user_id') || strpos($uri, '/monitor/') !== false) {
            return [
                'portal' => 'monitor',
                'module' => $this->extractModuleFromUrl($uri, 'monitor')
            ];
        }

        return null;
    }

    /**
     * Get user information based on portal context
     */
    protected function getUserInfo(array $portalContext, $session): array
    {
        switch ($portalContext['portal']) {
            case 'dakoii':
                return [
                    'user_id' => $session->get('dakoii_user_id'),
                    'username' => $session->get('dakoii_username') ?? 'system',
                    'user_type' => 'dakoii_user',
                    'user_full_name' => $session->get('dakoii_user_full_name'),
                    'organization_id' => null, // Dakoii users are system admins, not tied to specific organizations
                    'organization_name' => 'Dakoii System Administration',
                    'organization_type' => 'System',
                ];
            case 'admin':
                return [
                    'user_id' => $session->get('admin_user_id'),
                    'username' => $session->get('admin_username') ?? 'system',
                    'user_type' => 'admin_user',
                    'user_full_name' => $session->get('admin_user_full_name'),
                    'organization_id' => $session->get('admin_organization_id'),
                    'organization_name' => $session->get('admin_organization_name'),
                    'organization_type' => $session->get('admin_organization_type'),
                ];
            case 'monitor':
                return [
                    'user_id' => $session->get('monitor_user_id'),
                    'username' => $session->get('monitor_username') ?? 'system',
                    'user_type' => 'project_officer',
                    'user_full_name' => $session->get('monitor_user_full_name'),
                    'organization_id' => $session->get('monitor_organization_id'),
                    'organization_name' => $session->get('monitor_organization_name'),
                    'organization_type' => $session->get('monitor_organization_type'),
                ];
            default:
                return [
                    'user_id' => null,
                    'username' => 'system',
                    'user_type' => 'system',
                    'user_full_name' => null,
                    'organization_id' => null,
                    'organization_name' => null,
                    'organization_type' => null,
                ];
        }
    }

    /**
     * Get project context if available
     */
    protected function getProjectContext($session): array
    {
        return [
            'project_id' => $session->get('current_project_id'),
            'project_title' => $session->get('current_project_title'),
        ];
    }

    /**
     * Extract module name from URL
     */
    protected function extractModuleFromUrl(string $uri, string $portal): ?string
    {
        $pattern = "/{$portal}\/([^\/]+)/";
        if (preg_match($pattern, $uri, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * Sanitize data for JSON encoding to prevent array-to-string conversion errors
     */
    protected function sanitizeDataForJson(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                // Log array fields for debugging
                log_message('debug', "Auditable: Found array field '{$key}' with value: " . print_r($value, true));

                // Convert arrays to comma-separated strings or JSON strings
                if (empty($value)) {
                    $sanitized[$key] = '';
                } else {
                    // Check if it's a simple array of scalars
                    $isSimpleArray = true;
                    foreach ($value as $item) {
                        if (!is_scalar($item) && !is_null($item)) {
                            $isSimpleArray = false;
                            break;
                        }
                    }

                    if ($isSimpleArray) {
                        // Convert to comma-separated string
                        $sanitized[$key] = implode(', ', array_filter($value, function($v) {
                            return $v !== null && $v !== '';
                        }));
                    } else {
                        // Convert complex array to JSON string
                        $sanitized[$key] = json_encode($value, JSON_UNESCAPED_UNICODE);
                    }
                }
            } else {
                // Keep scalar values as-is
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Generate human-readable description
     */
    protected function generateDescription(string $action, $pk): string
    {
        return "Record {$action} in {$this->table}" . ($pk ? " with ID: {$pk}" : "");
    }
}
