TITLE: Configuring Root Route in CodeIgniter 4 PHP
DESCRIPTION: This PHP snippet defines the default route for a CodeIgniter 4 application. It maps the root URL ('/') to the 'index' method of the 'Home' controller, which is the entry point for the application when no specific URI segment is provided. This configuration is typically found in `app/Config/Routes.php` and uses the `RouteCollection` class to manage routes.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/tutorial/static_pages.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

use CodeIgniter\\Router\\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
```

----------------------------------------

TITLE: Configuring Nginx for CodeIgniter 4 with PHP 8.1 FPM
DESCRIPTION: This Nginx server block configuration enables clean URLs by removing index.php and routes all requests through CodeIgniter's front controller. It sets the document root to the application's public directory, handles PHP requests via PHP 8.1 FPM using Unix sockets, and denies access to hidden files, ensuring proper routing and security for a CodeIgniter 4 application.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/running.html#_snippet_16

LANGUAGE: NginxConf
CODE:
```
server {
    listen 80;
    listen [::]:80;

    server_name example.com;

    root  /var/www/example.com/public;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;

        # With php-fpm:
        fastcgi_pass unix:/run/php/php8.1-fpm.sock;
        # With php-cgi:
        # fastcgi_pass 127.0.0.1:9000;
    }

    error_page 404 /index.php;

    # deny access to hidden files such as .htaccess
    location ~ /\. {
        deny all;
    }
}
```

----------------------------------------

TITLE: Defining a UserModel for User Entity in CodeIgniter 4
DESCRIPTION: This PHP snippet defines `UserModel`, extending `CodeIgniter\Model`, to interact with the `users` database table. It specifies `allowedFields` for mass assignment, sets `returnType` to `\\App\\Entities\\User::class` to ensure model methods return Entity instances, and enables automatic timestamp management. This model handles the persistence logic for the `User` Entity.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/models/entities.html#_snippet_1

LANGUAGE: PHP
CODE:
```
<?php

namespace App\\Models;

use CodeIgniter\\Model;

class UserModel extends Model
{
    protected $table         = 'users';
    protected $allowedFields = [
        'username', 'email', 'password',
    ];
    protected $returnType    = \\App\\Entities\\User::class;
    protected $useTimestamps = true;
}
```

----------------------------------------

TITLE: Removing Development Dependencies with Composer (Shell)
DESCRIPTION: Executes the Composer `install` command with the `--no-dev` flag to prevent the installation of development-only packages. This action significantly reduces the `vendor` directory size, optimizing the application for production environments by removing unnecessary dependencies.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/deployment.html#_snippet_0

LANGUAGE: Shell
CODE:
```
composer install --no-dev
```

----------------------------------------

TITLE: Escaping Values with $db->escape() in CodeIgniter
DESCRIPTION: Illustrates the use of `escape()` to securely prepare data for database submission. This function automatically determines the data type, escapes only string data, and adds single quotes around the escaped value.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/queries.html#_snippet_8

LANGUAGE: PHP
CODE:
```
$sql = 'INSERT INTO table (title) VALUES(' . $db->escape($title) . ')';
```

----------------------------------------

TITLE: Escaping User Data for CodeIgniter 4 `where` Method (Custom String)
DESCRIPTION: This snippet highlights the critical importance of manually escaping user-supplied data when using raw SQL strings with the `where` method to prevent SQL injection vulnerabilities. It uses `$builder->db->escape()` to safely prepare values.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/query_builder.html#_snippet_29

LANGUAGE: PHP
CODE:
```
$name  = $builder->db->escape('Joe');
$where = "name={$name} AND status='boss' OR status='active'";
$builder->where($where);
```

----------------------------------------

TITLE: Standard PHP Echo Syntax in CodeIgniter Views
DESCRIPTION: This snippet demonstrates the traditional PHP syntax for echoing a variable, typically used within CodeIgniter view files. It utilizes the `esc()` helper function for output escaping to prevent XSS vulnerabilities.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/outgoing/alternative_php.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php echo esc($variable); ?>
```

----------------------------------------

TITLE: Updating Records with Array Data in CodeIgniter 4
DESCRIPTION: This example shows how to perform an update operation using `$builder->update()` by passing an associative array of data. It also demonstrates the use of `$builder->where()` to specify the update condition, ensuring that only targeted records are modified. All values are automatically escaped for security.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/query_builder.html#_snippet_102

LANGUAGE: PHP
CODE:
```
$data = [
    'title' => $title,
    'name'  => $name,
    'date'  => $date
];

$builder->where('id', $id);
$builder->update($data);
/*
 * Produces:
 * UPDATE mytable
 * SET title = '{$title}', name = '{$name}', date = '{$date}'
 * WHERE id = $id
 */
```

----------------------------------------

TITLE: Accessing Old Form Data with `old()` in CodeIgniter 4 (PHP/HTML)
DESCRIPTION: This snippet demonstrates how to use the `old()` helper function in CodeIgniter 4 to repopulate form fields with previously submitted data after a validation error or redirect. It shows both the controller-side logic for saving input and the view-side usage for single and array-based form fields. The `withInput()` method is crucial for making data available.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/general/common_functions.html#_snippet_1

LANGUAGE: PHP
CODE:
```
// in controller, checking form submittal
if (! $model->save($user)) {
    // 'withInput()' is what specifies "old data" should be saved.
    return redirect()->back()->withInput();
}
```

LANGUAGE: HTML
CODE:
```
<input type="email" name="email" value="<?= old('email') ?>">
```

LANGUAGE: HTML
CODE:
```
<input type="email" name="user[email]" value="<?= old('user.email') ?>">
```

----------------------------------------

TITLE: Displaying a Single View in CodeIgniter 4 (PHP)
DESCRIPTION: This snippet demonstrates the basic usage of the `view()` function in a CodeIgniter 4 controller to load and display a single view file. The `name` parameter should be replaced with the actual name of the view file, which is expected to have a `.php` extension if omitted.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/outgoing/views.html#_snippet_1

LANGUAGE: PHP
CODE:
```
return view('name');
```

----------------------------------------

TITLE: Redirecting Back with Flash Message in CodeIgniter
DESCRIPTION: This snippet shows how to redirect back to the previous page and simultaneously set a flash message. The message will be available for retrieval on the next request, typically used for displaying success or error notifications.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/outgoing/response.html#_snippet_10

LANGUAGE: PHP
CODE:
```
return redirect()->back()->with('foo', 'message');
```

----------------------------------------

TITLE: Configuring CSP Directives at Runtime in CodeIgniter 4
DESCRIPTION: This PHP snippet demonstrates how to dynamically configure Content Security Policy (CSP) directives within a CodeIgniter 4 controller. It shows how to obtain the CSP instance from the response object and use various methods like reportOnly(), setDefaultSrc(), setReportURI(), upgradeInsecureRequests(), and add*Src() to define allowed content sources and behaviors. The second parameter in add*Src() methods allows overriding the default reporting treatment for specific directives.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/outgoing/csp.html#_snippet_1

LANGUAGE: PHP
CODE:
```
<?php

// get the CSP instance
$csp = $this->response->getCSP();

// specify the default directive treatment
$csp->reportOnly(false);

// specify the origin to use if none provided for a directive
$csp->setDefaultSrc('cdn.example.com');

// specify the URL that "report-only" reports get sent to
$csp->setReportURI('http://example.com/csp/reports');

// specify that HTTP requests be upgraded to HTTPS
$csp->upgradeInsecureRequests(true);

// add types or origins to CSP directives
// assuming that the default treatment is to block rather than just report
$csp->addBaseURI('example.com', true); // report only
$csp->addChildSrc('https://youtube.com'); // blocked
$csp->addConnectSrc('https://*.facebook.com', false); // blocked
$csp->addFontSrc('fonts.example.com');
$csp->addFormAction('self');
$csp->addFrameAncestor('none', true); // report this one
$csp->addImageSrc('cdn.example.com');
$csp->addMediaSrc('cdn.example.com');
$csp->addManifestSrc('cdn.example.com');
$csp->addObjectSrc('cdn.example.com', false); // reject from here
$csp->addPluginType('application/pdf', false); // reject this media type
$csp->addScriptSrc('scripts.example.com', true); // allow but report requests from here
$csp->addStyleSrc('css.example.com');
$csp->addSandbox(['allow-forms', 'allow-scripts']);
```

----------------------------------------

TITLE: Limiting and Offsetting Results with Query Builder get() in CodeIgniter 4 PHP
DESCRIPTION: This snippet demonstrates using the `get()` method with limit and offset parameters. The first parameter (10) specifies the number of rows to return, and the second (20) specifies the offset. This translates to a `LIMIT 20, 10` clause in MySQL, retrieving 10 records starting from the 21st record.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/query_builder.html#_snippet_2

LANGUAGE: PHP
CODE:
```
$query = $builder->get(10, 20);
/*
 * Executes: SELECT * FROM mytable LIMIT 20, 10
 * (in MySQL. Other databases have slightly different syntax)
 */
```

----------------------------------------

TITLE: Creating a New Migration in CodeIgniter 4 PHP
DESCRIPTION: This snippet demonstrates how to create a basic database migration in CodeIgniter 4 using the `Migration` class. The `up()` method defines the schema changes to apply, such as adding a 'blog' table with specific columns and a primary key, while the `down()` method defines how to revert these changes, typically by dropping the table. It utilizes the `$this->forge` object for database schema manipulation.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/dbmgmt/migration.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddBlog extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'blog_id' => [
                'type'           => 'INT',
                'constraint'     => 5,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'blog_title' => [
                'type'       => 'VARCHAR',
                'constraint' => '100',
            ],
            'blog_description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
        ]);
        $this->forge->addKey('blog_id', true);
        $this->forge->createTable('blog');
    }

    public function down()
    {
        $this->forge->dropTable('blog');
    }
}
```

----------------------------------------

TITLE: Accessing HTTP Request Data with CodeIgniter's IncomingRequest
DESCRIPTION: This PHP snippet demonstrates how to interact with incoming HTTP requests using CodeIgniter's `IncomingRequest` class. It shows methods for retrieving URI paths, GET and POST variables, combined REQUEST variables, JSON payloads, server variables, and HTTP headers. It also includes methods for checking the request's HTTP method, abstracting raw PHP superglobals for a consistent interface.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/concepts/http.html#_snippet_2

LANGUAGE: PHP
CODE:
```
<?php

use CodeIgniter\\HTTP\\IncomingRequest;

$request = request();

// the URI path being requested (i.e., /about)
$request->getUri()->getPath();

// Retrieve $_GET and $_POST variables
$request->getGet('foo');
$request->getPost('foo');

// Retrieve from $_REQUEST which should include
// both $_GET and $_POST contents
$request->getVar('foo');

// Retrieve JSON from AJAX calls
$request->getJSON();

// Retrieve server variables
$request->getServer('Host');

// Retrieve an HTTP Request header, with case-insensitive names
$request->header('host');
$request->header('Content-Type');

// Checks the HTTP method
$request->is('get');
$request->is('post');
```

----------------------------------------

TITLE: Running Basic Validation in CodeIgniter 4 (PHP)
DESCRIPTION: This snippet demonstrates how to execute validation using the `run()` method of the CodeIgniter 4 Validation class. It shows examples for validating data with default rules or a predefined rule group, returning `true` on success and `false` on failure.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/libraries/validation.html#_snippet_13

LANGUAGE: PHP
CODE:
```
if (! $validation->run($data)) {
    // handle validation errors
}
// or
if (! $validation->run($data, 'signup')) {
    // handle validation errors
}
```

----------------------------------------

TITLE: Configuring CodeIgniter Database Connection in .env
DESCRIPTION: These lines represent the necessary configuration settings for the '.env' file to connect CodeIgniter to a MySQL database. They specify the hostname, database name ('ci4tutorial'), username, password, and the database driver ('MySQLi') for the default connection.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/tutorial/news_section.html#_snippet_2

LANGUAGE: CodeIgniter .env Configuration
CODE:
```
database.default.hostname = localhost
database.default.database = ci4tutorial
database.default.username = root
database.default.password = root
database.default.DBDriver = MySQLi
```

----------------------------------------

TITLE: Implementing IP-Based Rate Limiting with CodeIgniter 4 Filter (PHP)
DESCRIPTION: This PHP snippet demonstrates how to create a custom CodeIgniter filter (`Throttle.php`) to implement site-wide rate limiting based on the client's IP address. It utilizes the `Throttler` service to check if an IP has exceeded the allowed request rate (e.g., 1 request per second). If the limit is exceeded, it returns an HTTP 429 (Too Many Attempts) response, preventing further script execution. This functionality requires the Cache library to be configured with a non-dummy handler for the `Throttler` class to operate.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/libraries/throttler.html#_snippet_1

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class Throttle implements FilterInterface
{
    /**
     * This is a demo implementation of using the Throttler class
     * to implement rate limiting for your application.
     *
     * @param list<string>|null $arguments
     *
     * @return ResponseInterface|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $throttler = service('throttler');

        // Restrict an IP address to no more than 1 request
        // per second across the entire site.
        if ($throttler->check(md5($request->getIPAddress()), 60, MINUTE) === false) {
            return service('response')->setStatusCode(429);
        }
    }

    /**
     * We don't have anything to do here.
     *
     * @param list<string>|null $arguments
     *
     * @return void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // ...
    }
}
```

----------------------------------------

TITLE: Configuring NewsModel for Data Saving - PHP
DESCRIPTION: This PHP snippet shows the NewsModel class, extending CodeIgniter\Model. It defines the $table property as 'news' and, crucially, the $allowedFields property, which lists the database columns ('title', 'slug', 'body') that are permitted for mass assignment via the save() method, preventing mass assignment vulnerabilities.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/tutorial/create_news_items.html#_snippet_7

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Models;

use CodeIgniter\Model;

class NewsModel extends Model
{
    protected $table = 'news';

    protected $allowedFields = ['title', 'slug', 'body'];
}
```

----------------------------------------

TITLE: Finding a Single Record by Primary Key in CodeIgniter 4 Model (PHP)
DESCRIPTION: This snippet demonstrates how to retrieve a single row from a CodeIgniter 4 model by passing the primary key value to the `find()` method. The format of the returned value is determined by the model's `$returnType` property.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/models/model.html#_snippet_12

LANGUAGE: PHP
CODE:
```
<?php

$user = $userModel->find($userId);
```

----------------------------------------

TITLE: Setting Input Field Value with `set_value` in CodeIgniter 4 (HTML/PHP)
DESCRIPTION: This snippet demonstrates how to use CodeIgniter 4's `set_value` helper function within an HTML input tag. It automatically populates the `value` attribute of an input or textarea field with previously submitted data, or a specified default value ('0' in this case) if no data is present. This helps in repopulating forms after validation errors.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/helpers/form_helper.html#_snippet_38

LANGUAGE: HTML
CODE:
```
<input type="text" name="quantity" value="<?= set_value('quantity', '0') ?>" size="50">
```

----------------------------------------

TITLE: Generating Routes by Name in CodeIgniter 4 PHP
DESCRIPTION: This example illustrates defining a named route and subsequently generating its URL path using route_to() with the route's alias. It's useful for creating stable links independent of controller changes.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/general/common_functions.html#_snippet_9

LANGUAGE: PHP
CODE:
```
<?php

// The route is defined as:
$routes->get('users/(:num)/gallery/(:num)', 'Galleries::showUserGallery/$1/$2', ['as' => 'user_gallery']);
```

LANGUAGE: PHP
CODE:
```
<?php

// Generate the route with user ID 15, gallery 12:
route_to('user_gallery', 15, 12);
// Result: '/users/15/gallery/12'
```

----------------------------------------

TITLE: Defining Validation Rules in CI4 Controller
DESCRIPTION: This snippet illustrates the new array-based syntax for defining validation rules in CodeIgniter 4. Rules are now passed as an associative array directly to the `validateData()` method, specifying field names and their corresponding rules.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/upgrade_validations.html#_snippet_5

LANGUAGE: PHP
CODE:
```
<?php

$isValid = $this->validateData($data, [
    'name'  => 'required|min_length[3]',
    'email' => 'required|valid_email',
    'phone' => 'required|numeric|max_length[10]',
]);
```

----------------------------------------

TITLE: Implementing a Basic CodeIgniter ResourceController (PHP)
DESCRIPTION: This PHP snippet shows a basic implementation of a `ResourceController` in CodeIgniter. It extends `CodeIgniter\RESTful\ResourceController`, overrides `modelName` and `format` properties, and includes an `index` method to respond with all records from the associated model, serving as a foundation for RESTful APIs.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/restful.html#_snippet_8

LANGUAGE: PHP
CODE:
```
namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class Photos extends ResourceController
{
    protected $modelName = 'App\Models\Photos';
    protected $format    = 'json';

    public function index()
    {
        return $this->respond($this->model->findAll());
    }

    // ...
}
```

----------------------------------------

TITLE: Retrieving Raw JSON Input - PHP
DESCRIPTION: Demonstrates how to retrieve the entire contents of `php://input` as a JSON stream using `getJSON()`. By default, JSON objects are returned as PHP objects, but can be converted to associative arrays by passing `true` as the first parameter.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/incomingrequest.html#_snippet_17

LANGUAGE: PHP
CODE:
```
$json = $request->getJSON();
```

----------------------------------------

TITLE: Enabling Controller Testing with ControllerTestTrait in PHP
DESCRIPTION: This snippet demonstrates how to enable controller testing in CodeIgniter 4 by using the `ControllerTestTrait` within a test class that extends `CIUnitTestCase`. It also includes `DatabaseTestTrait` for database-related testing capabilities.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/testing/controllers.html#_snippet_0

LANGUAGE: PHP
CODE:
```
namespace App\Controllers;

use CodeIgniter\Test\CIUnitTestCase;
use CodeIgniter\Test\ControllerTestTrait;
use CodeIgniter\Test\DatabaseTestTrait;

class FooControllerTest extends CIUnitTestCase
{
    use ControllerTestTrait;
    use DatabaseTestTrait;
}
```

----------------------------------------

TITLE: Example HTTP GET Request
DESCRIPTION: This snippet illustrates a typical HTTP GET request sent from a client (e.g., web browser) to a server. It specifies the request method (GET), the requested path (/), the HTTP version, the target host, accepted content types, and the user agent. This message provides the server with all necessary information about what the client is requesting.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/concepts/http.html#_snippet_0

LANGUAGE: HTTP
CODE:
```
GET / HTTP/1.1
Host codeigniter.com
Accept: text/html
User-Agent: Chrome/46.0.2490.80
```

----------------------------------------

TITLE: Retrieving All Records with Query Builder get() in CodeIgniter 4 PHP
DESCRIPTION: This example shows how to retrieve all records from 'mytable' using the Query Builder's `get()` method. It first initializes the builder for 'mytable' and then executes `get()` to produce a `SELECT * FROM mytable` query, returning the result object.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/query_builder.html#_snippet_1

LANGUAGE: PHP
CODE:
```
$builder = $db->table('mytable');
$query   = $builder->get();  // Produces: SELECT * FROM mytable
```

----------------------------------------

TITLE: Configuring Default MySQLi Database Connection in CodeIgniter 4
DESCRIPTION: This PHP snippet defines the default database connection settings within the `Config\Database` class for a MySQLi database. It includes common parameters like hostname, username, password, database name, driver, character set, and port, providing a template for initial database setup.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/configuration.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

namespace Config;

use CodeIgniter\\Database\\Config;

class Database extends Config
{
    // ...

    public array $default \= [
        'DSN'      \=> '',
        'hostname' \=> 'localhost',
        'username' \=> 'root',
        'password' \=> '',
        'database' \=> 'database\_name',
        'DBDriver' \=> 'MySQLi',
        'DBPrefix' \=> '',
        'pConnect' \=> false,
        'DBDebug'  \=> true,
        'charset'  \=> 'utf8mb4',
        'DBCollat' \=> 'utf8mb4\_general\_ci',
        'swapPre'  \=> '',
        'encrypt'  \=> false,
        'compress' \=> false,
        'strictOn' \=> false,
        'failover' \=> [],
        'port'     \=> 3306,
    ];

    // ...
}
```

----------------------------------------

TITLE: Creating News Controller in CodeIgniter (PHP)
DESCRIPTION: This PHP code defines the News controller, extending BaseController to manage news-related requests. It includes an index() method to fetch and prepare data for displaying all news items using NewsModel::getNews(), and a show() method to retrieve a specific news item by passing a $slug to the model. The model() helper function is used to instantiate the NewsModel.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/tutorial/news_section.html#_snippet_6

LANGUAGE: PHP
CODE:
```
<?php

namespace App\Controllers;

use App\Models\NewsModel;

class News extends BaseController
{
    public function index()
    {
        $model = model(NewsModel::class);

        $data['news_list'] = $model->getNews();
    }

    public function show(?string $slug = null)
    {
        $model = model(NewsModel::class);

        $data['news'] = $model->getNews($slug);
    }
}
```

----------------------------------------

TITLE: Using Positional Query Bindings in CodeIgniter 4
DESCRIPTION: This snippet demonstrates how to use positional query bindings where question marks (`?`) in the SQL query are automatically replaced by values from an array. This method automatically escapes values, enhancing query security.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/queries.html#_snippet_11

LANGUAGE: PHP
CODE:
```
$sql = 'SELECT * FROM some_table WHERE id = ? AND status = ? AND author = ?';
$db->query($sql, [3, 'live', 'Rick']);
```

----------------------------------------

TITLE: Adding GROUP BY Clause with CodeIgniter Query Builder (PHP)
DESCRIPTION: This method allows adding a `GROUP BY` clause to the SQL query. It accepts a single column name as a string, which will be used to group the result set.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/query_builder.html#_snippet_35

LANGUAGE: PHP
CODE:
```
$builder->groupBy('title');
// Produces: GROUP BY title
```

----------------------------------------

TITLE: Defining Classname Mapping for Factories in CodeIgniter 4 PHP
DESCRIPTION: This snippet shows how to define a classname mapping for CodeIgniter 4 Factories. This is necessary when you are using third-party packages that reference models (or other components) with their original namespaces (e.g., Myth\Auth\Models\UserModel), but you want to load your overridden application-specific version (e.g., App\Models\UserModel). The Factories::define() method allows you to specify the alias, the original class, and the class to be loaded.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/upgrade_440.html#_snippet_3

LANGUAGE: PHP
CODE:
```
Factories::define('models', 'Myth\\Auth\\Models\\UserModel', 'App\\Models\\UserModel');
```

----------------------------------------

TITLE: Passing Parameters and Custom Method Mapping with `_remap()` in CodeIgniter PHP
DESCRIPTION: This snippet illustrates how `_remap()` can capture additional URI segments as parameters using variadic arguments (`...$params`). It demonstrates a pattern for mapping URI segments to internal controller methods (e.g., by prefixing with 'process_') and handling non-existent methods by throwing a `PageNotFoundException`, providing flexible and robust custom routing.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/controllers.html#_snippet_19

LANGUAGE: PHP
CODE:
```
namespace App\Controllers;

class Products extends BaseController
{
    public function \_remap($method, ...$params)
    {
        $method \= 'process\_' . $method;

        if (method\_exists($this, $method)) {
            return $this->{$method}(...$params);
        }

        throw \\CodeIgniter\\Exceptions\\PageNotFoundException::forPageNotFound();
    }
}
```

----------------------------------------

TITLE: Creating a User Sign-up Form View in CodeIgniter
DESCRIPTION: This HTML view file (`signup.php`) defines a user registration form. It uses CodeIgniter's `validation_list_errors()` to display validation errors and `form_open()`/`form_close()` helpers for form submission. Input fields utilize `set_value()` to repopulate data after submission, enhancing user experience during validation failures.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/libraries/validation.html#_snippet_0

LANGUAGE: HTML
CODE:
```
<html>
<head>
    <title>My Form</title>
</head>
<body>

    <?= validation_list_errors() ?>

    <?= form_open('form') ?>

        <h5>Username</h5>
        <input type="text" name="username" value="<?= set_value('username') ?>" size="50">

        <h5>Password</h5>
        <input type="text" name="password" value="<?= set_value('password') ?>" size="50">

        <h5>Password Confirm</h5>
        <input type="text" name="passconf" value="<?= set_value('passconf') ?>" size="50">

        <h5>Email Address</h5>
        <input type="text" name="email" value="<?= set_value('email') ?>" size="50">

        <div><input type="submit" value="Submit"></div>

    <?= form_close() ?>

</body>
</html>
```

----------------------------------------

TITLE: Filtering POST Input Data in CodeIgniter 4 PHP
DESCRIPTION: Demonstrates how to filter a POST variable using the `getPost()` method of the `Request` object in CodeIgniter 4. The second parameter specifies the filter type, leveraging PHP's native `filter_var()` function for security. This example sanitizes an email input.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/incomingrequest.html#_snippet_23

LANGUAGE: PHP
CODE:
```
$email = $request->getPost('email', FILTER_SANITIZE_EMAIL);
```

----------------------------------------

TITLE: Fixing Current Time for Time-Dependent Tests in CodeIgniter (PHP)
DESCRIPTION: This snippet illustrates how to fix or manipulate the current time within CodeIgniter tests using `Time::setTestNow()`. It allows developers to test time-sensitive logic without relying on the system's actual time, ensuring consistent and reproducible test results. The `tearDown` method is used to reset the time after each test.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/testing/overview.html#_snippet_19

LANGUAGE: PHP
CODE:
```
namespace Tests;

use CodeIgniter\I18n\Time;
use CodeIgniter\Test\CIUnitTestCase;

final class TimeDependentCodeTest extends CIUnitTestCase
{
    protected function tearDown(): void
    {
        parent::tearDown();

        // Reset the current time.
        Time::setTestNow();
    }

    public function testFixTime(): void
    {
        // Fix the current time to "2023-11-25 12:00:00".
        Time::setTestNow('2023-11-25 12:00:00');

        // This assertion always passes.
        $this->assertSame('2023-11-25 12:00:00', (string) Time::now());
    }
}
```

----------------------------------------

TITLE: Defining Validation Rules with Placeholders in CodeIgniter Model (PHP)
DESCRIPTION: Demonstrates how to define validation rules, specifically the `is_unique` rule, using placeholders like `{id}` within a CodeIgniter model. This allows the rule to dynamically ignore a specific record during uniqueness checks based on the value of an incoming data field.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/models/model.html#_snippet_45

LANGUAGE: PHP
CODE:
```
namespace App\Models;

use CodeIgniter\Model;

class MyModel extends Model
{
    // ...

    protected $validationRules = [
        'id'    => 'max_length[19]|is_natural_no_zero',
        'email' => 'required|max_length[254]|valid_email|is_unique[users.email,id,{id}]',
    ];
}
```

----------------------------------------

TITLE: Retrieving All Uploaded Files in CodeIgniter 4 PHP
DESCRIPTION: Demonstrates how to retrieve an array of all uploaded files using the `$request->getFiles()` method. Each element in the returned array is an instance of `CodeIgniter\HTTP\Files\UploadedFile`, providing a secure and convenient way to manage file uploads.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/incomingrequest.html#_snippet_31

LANGUAGE: PHP
CODE:
```
$files = $request->getFiles();
```

----------------------------------------

TITLE: Updating CodeIgniter 4 Dependencies with Composer (Shell)
DESCRIPTION: This command updates all Composer dependencies in the project to their latest compatible versions as defined in `composer.json`. It's used for general upgrades and keeping packages current.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/installing_composer.html#_snippet_4

LANGUAGE: Shell
CODE:
```
composer update
```

----------------------------------------

TITLE: Updating Composer Dependencies (CodeIgniter 4)
DESCRIPTION: This command updates all Composer dependencies for the CodeIgniter 4 project, ensuring all required packages are at their latest compatible versions as defined in `composer.json`.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/upgrade_420.html#_snippet_1

LANGUAGE: shell
CODE:
```
composer update
```

----------------------------------------

TITLE: Encrypting and Decrypting Data with CodeIgniter Encryption in PHP
DESCRIPTION: This snippet illustrates the basic usage of the CodeIgniter Encryption library for encrypting and decrypting a plain-text message. It shows how to pass a string to the `encrypt()` method and then retrieve the original text using the `decrypt()` method.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/libraries/encryption.html#_snippet_1

LANGUAGE: PHP
CODE:
```
$plainText  = 'This is a plain-text message!';
$ciphertext = $encrypter->encrypt($plainText);

// Outputs: This is a plain-text message!
echo $encrypter->decrypt($ciphertext);
```

----------------------------------------

TITLE: Configuring Custom Validation Error Messages (Inline) - CodeIgniter 4 PHP
DESCRIPTION: This snippet illustrates an alternative method for defining custom validation error messages directly within the rule definition array for each field. Each field's configuration includes a 'rules' key for validation rules and an 'errors' key for specific error messages, allowing for granular control over messages for individual rules like 'required' or 'valid_email'.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/libraries/validation.html#_snippet_21

LANGUAGE: PHP
CODE:
```
<?php

namespace Config;

// ...

class Validation extends BaseConfig
{
    // ...

    public array $signup = [
        'username' => [
            'rules'  => 'required|max_length[30]',
            'errors' => [
                'required' => 'You must choose a Username.',
            ],
        ],
        'email' => [
            'rules'  => 'required|max_length[254]|valid_email',
            'errors' => [
                'valid_email' => 'Please check the Email field. It does not appear to be valid.',
            ],
        ],
    ];

    // ...
}
```

----------------------------------------

TITLE: Defining GET Routes with and without Parameters in CodeIgniter
DESCRIPTION: This example demonstrates defining GET routes for the 'users' path. The first route maps 'users' to the `list` method of the `Users` controller without parameters. The second route maps 'users/1/23' to the `list` method, passing '1' and '23' as parameters to the controller method.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/routing.html#_snippet_1

LANGUAGE: PHP
CODE:
```
// Calls $Users->list()
$routes->get('users', 'Users::list');

// Calls $Users->list(1, 23)
$routes->get('users/1/23', 'Users::list/1/23');
```

----------------------------------------

TITLE: Generating Standard RESTful Routes with CodeIgniter 4
DESCRIPTION: This snippet demonstrates how to use the `resource()` method in CodeIgniter 4 to automatically generate a set of common RESTful routes for a given resource, such as 'photos'. It includes routes for creating, listing, showing, updating, and deleting resources, simplifying API development.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/restful.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

$routes->resource('photos');

// Equivalent to the following:
$routes->get('photos/new', 'Photos::new');
$routes->post('photos', 'Photos::create');
$routes->get('photos', 'Photos::index');
$routes->get('photos/(:segment)', 'Photos::show/$1');
$routes->get('photos/(:segment)/edit', 'Photos::edit/$1');
$routes->put('photos/(:segment)', 'Photos::update/$1');
$routes->patch('photos/(:segment)', 'Photos::update/$1');
$routes->delete('photos/(:segment)', 'Photos::delete/$1');
```

----------------------------------------

TITLE: Retrieving POST Data with getPost() in CodeIgniter (PHP)
DESCRIPTION: Illustrates the use of the `getPost()` method to retrieve data from the `$_POST` superglobal. This method is the recommended way to access POST parameters, offering advantages like automatic null return for non-existent keys.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/incomingrequest.html#_snippet_11

LANGUAGE: PHP
CODE:
```
$request->getPost()
```

----------------------------------------

TITLE: Displaying Variables in CodeIgniter Views (PHP)
DESCRIPTION: CodeIgniter views primarily consist of HTML augmented with minimal PHP, typically used for displaying data passed from controllers. This snippet illustrates the basic method of outputting a variable's content using a simple `echo` call within a view file.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/concepts/mvc.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php echo $variableName; ?>
```

----------------------------------------

TITLE: Connecting to Default Database Group using Config\Database::connect (PHP)
DESCRIPTION: This snippet demonstrates how to establish a connection to the default database group defined in the CodeIgniter 4 database configuration file. It can be placed in any function or a class constructor to make the database globally available within that class. The `connect()` method, when called without parameters, automatically uses the `Config\Database::$defaultGroup`.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/connecting.html#_snippet_0

LANGUAGE: PHP
CODE:
```
$db = \Config\Database::connect();
```

----------------------------------------

TITLE: Performing an Insert with Query Builder - PHP
DESCRIPTION: This snippet shows how to perform an `INSERT` operation using CodeIgniter's Query Builder. Data is provided as an associative array, and the `insert()` method automatically constructs and executes the SQL query, simplifying data insertion.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/database/examples.html#_snippet_7

LANGUAGE: PHP
CODE:
```
$data = [
    'title' => $title,
    'name'  => $name,
    'date'  => $date,
];

$db->table('mytable')->insert($data);
// Produces: INSERT INTO mytable (title, name, date) VALUES ('{$title}', '{$name}', '{$date}')
```

----------------------------------------

TITLE: Creating a Basic Controller Filter in PHP
DESCRIPTION: This snippet demonstrates the fundamental structure of a CodeIgniter 4 controller filter. It shows how to implement `CodeIgniter\Filters\FilterInterface` and define the required `before()` and `after()` methods, which serve as placeholders for logic executed before and after the controller respectively. Both methods must be present, even if left empty.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/incoming/filters.html#_snippet_0

LANGUAGE: PHP
CODE:
```
<?php

namespace App\\Filters;

use CodeIgniter\\Filters\\FilterInterface;
use CodeIgniter\\HTTP\\RequestInterface;
use CodeIgniter\\HTTP\\ResponseInterface;

class MyFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Do something here
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Do something here
    }
}
```

----------------------------------------

TITLE: Optimizing CodeIgniter 4 for Production with Composer (Shell)
DESCRIPTION: This command is crucial for production deployments. It removes development-only Composer packages, significantly reducing the `vendor` folder size and improving security by excluding unnecessary dependencies.
SOURCE: https://github.com/codeigniter4/userguide/blob/master/docs/installation/installing_composer.html#_snippet_1

LANGUAGE: Shell
CODE:
```
composer install --no-dev
```