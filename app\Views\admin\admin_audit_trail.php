<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/audit') ?>?<?= http_build_query($filters) ?>&export=csv" class="btn btn-outline-success">
    <i class="bi bi-download me-2"></i>
    Export CSV
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-shield-check me-2"></i>
            Audit Trail
        </h1>
        <p class="text-muted mb-0">
            Monitor system activities and user actions
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-5">

    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h4 fw-bold text-primary mb-1">
                            <?= number_format($stats['total_logs']) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Total Logs
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 50px; height: 50px; background: var(--promis-gradient-primary);">
                        <i class="bi bi-clipboard-data fs-5"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h4 fw-bold text-primary mb-1">
                            <?= number_format($stats['logs_today']) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Today's Activity
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 50px; height: 50px; background: var(--promis-gradient-secondary);">
                        <i class="bi bi-calendar-day fs-5"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h4 fw-bold text-primary mb-1">
                            <?= number_format($stats['logs_this_week']) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            This Week
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 50px; height: 50px; background: var(--promis-gradient-accent);">
                        <i class="bi bi-graph-up fs-5"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h6 class="fw-bold text-primary mb-1">
                            Most Active
                        </h6>
                        <p class="text-muted mb-0 small">
                            <?= esc($stats['most_active_user']) ?>
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 50px; height: 50px; background: var(--promis-gradient-secondary);">
                        <i class="bi bi-trophy fs-5"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-funnel me-2"></i>
            Filters & Search
        </h5>
    </div>
    <div class="card-body">
        <form method="get" action="<?= base_url('admin/audit') ?>" class="audit-filters">

            <div class="row g-3 mb-4">

                <!-- Search -->
                <div class="col-lg-3 col-md-6">
                    <label class="form-label fw-semibold">Search</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input
                            type="text"
                            name="search"
                            class="form-control"
                            placeholder="Search descriptions, users..."
                            value="<?= esc($filters['search']) ?>"
                        >
                    </div>
                </div>

                <!-- User Filter -->
                <div class="col-lg-3 col-md-6">
                    <label class="form-label fw-semibold">User</label>
                    <select name="user" class="form-select">
                        <option value="">All Users</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?= $user['user_id'] ?>" <?= ($filters['user'] == $user['user_id']) ? 'selected' : '' ?>>
                                <?= esc($user['username']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Action Filter -->
                <div class="col-lg-3 col-md-6">
                    <label class="form-label fw-semibold">Action</label>
                    <select name="action" class="form-select">
                        <option value="">All Actions</option>
                        <option value="create" <?= ($filters['action'] === 'create') ? 'selected' : '' ?>>Create</option>
                        <option value="update" <?= ($filters['action'] === 'update') ? 'selected' : '' ?>>Update</option>
                        <option value="delete" <?= ($filters['action'] === 'delete') ? 'selected' : '' ?>>Delete</option>
                        <option value="login" <?= ($filters['action'] === 'login') ? 'selected' : '' ?>>Login</option>
                        <option value="logout" <?= ($filters['action'] === 'logout') ? 'selected' : '' ?>>Logout</option>
                    </select>
                </div>

                <!-- Module Filter -->
                <div class="col-lg-3 col-md-6">
                    <label class="form-label fw-semibold">Module</label>
                    <select name="module" class="form-select">
                        <option value="">All Modules</option>
                        <?php foreach ($modules as $module): ?>
                            <option value="<?= $module['module'] ?>" <?= ($filters['module'] === $module['module']) ? 'selected' : '' ?>>
                                <?= esc(ucfirst(str_replace('_', ' ', $module['module']))) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="row g-3 mb-4">

                <!-- Date From -->
                <div class="col-lg-4 col-md-6">
                    <label class="form-label fw-semibold">Date From</label>
                    <input
                        type="date"
                        name="date_from"
                        class="form-control"
                        value="<?= esc($filters['date_from']) ?>"
                    >
                </div>

                <!-- Date To -->
                <div class="col-lg-4 col-md-6">
                    <label class="form-label fw-semibold">Date To</label>
                    <input
                        type="date"
                        name="date_to"
                        class="form-control"
                        value="<?= esc($filters['date_to']) ?>"
                    >
                </div>

                <!-- Per Page -->
                <div class="col-lg-4 col-md-6">
                    <label class="form-label fw-semibold">Per Page</label>
                    <select name="per_page" class="form-select">
                        <option value="25" <?= ($filters['per_page'] == 25) ? 'selected' : '' ?>>25</option>
                        <option value="50" <?= ($filters['per_page'] == 50) ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= ($filters['per_page'] == 100) ? 'selected' : '' ?>>100</option>
                        <option value="200" <?= ($filters['per_page'] == 200) ? 'selected' : '' ?>>200</option>
                    </select>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-check-circle me-2"></i>
                    Apply Filters
                </button>
                <a href="<?= base_url('admin/audit') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-2"></i>
                    Clear All
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Audit Logs Table -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-table me-2"></i>
            Audit Logs (<?= count($audit_logs) ?> shown)
        </h5>
    </div>

    <?php if (!empty($audit_logs)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th scope="col">Date/Time</th>
                        <th scope="col">User</th>
                        <th scope="col">Action</th>
                        <th scope="col">Details</th>
                        <th scope="col">IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($audit_logs as $log): ?>
                        <tr>
                            <td>
                                <div class="fw-semibold text-primary small">
                                    <?= date('M j, Y', strtotime($log['created_at'])) ?>
                                </div>
                                <div class="text-muted small">
                                    <?= date('g:i:s A', strtotime($log['created_at'])) ?>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white me-2 fw-bold small"
                                         style="width: 32px; height: 32px; background: var(--promis-gradient-primary);">
                                        <?= strtoupper(substr($log['username'] ?: 'S', 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-primary small">
                                            <?= esc($log['username'] ?: 'System') ?>
                                        </div>
                                        <div class="text-muted small">
                                            <?= esc($log['user_type']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $actionBadges = [
                                    'create' => 'bg-success',
                                    'update' => 'bg-primary',
                                    'delete' => 'bg-danger',
                                    'login' => 'bg-info',
                                    'logout' => 'bg-secondary'
                                ];
                                $badgeClass = $actionBadges[$log['action']] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?= $badgeClass ?> text-uppercase small">
                                    <?= esc($log['action']) ?>
                                </span>
                                <div class="text-muted small mt-1">
                                    <i class="bi bi-box me-1"></i>
                                    <?= esc(ucfirst(str_replace('_', ' ', $log['module']))) ?>
                                </div>
                            </td>
                            <td>
                                <div class="fw-semibold text-primary small mb-1">
                                    <?= esc($log['description']) ?>
                                </div>
                                <div class="text-muted small">
                                    <i class="bi bi-table me-1"></i>
                                    Table: <?= esc($log['table_name']) ?> | ID: <?= esc($log['primary_key']) ?>
                                </div>
                            </td>
                            <td>
                                <div class="fw-semibold text-secondary small">
                                    <i class="bi bi-geo-alt me-1"></i>
                                    <?= esc($log['ip_address']) ?>
                                </div>
                                <div class="text-muted small">
                                    <i class="bi bi-browser-chrome me-1"></i>
                                    <?= esc(substr($log['user_agent'], 0, 30)) ?>...
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h5 class="mt-3 text-muted">No audit logs found</h5>
            <p class="text-muted">Try adjusting your filters or check back later.</p>
        </div>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>
