{"page_profile": {"page_title": "Create New Phase - PROMIS Admin", "route": "http://localhost/promis_two/admin/projects/7/phases/create", "template": "promis_admin_template", "last_analyzed": "2025-01-26", "layout_structure": {"container": {"type": "admin-layout", "display": "flex", "min_height": "100vh", "background": "var(--bg-secondary)"}, "sidebar": {"width": "280px", "background": "var(--surface-sidebar)", "position": "fixed", "height": "100vh", "z_index": 1000, "mobile_behavior": "slide-in overlay"}, "main_content": {"margin_left": "280px", "padding": "var(--spacing-xl)", "width": "calc(100% - 280px)", "mobile_width": "100%", "mobile_margin": "0"}}, "header_section": {"container": {"display": "flex", "justify_content": "space-between", "align_items": "center", "margin_bottom": "var(--spacing-xl)"}, "page_title": {"element": "h1", "color": "var(--text-primary)", "font_size": "1.75rem", "font_weight": "700", "margin_bottom": "var(--spacing-sm)"}, "subtitle": {"element": "p", "color": "var(--text-secondary)", "margin": "0", "content": "Add a new phase to project: [Project Title]"}, "header_actions": {"position": "top-right", "buttons": [{"text": "← Back to Project Profile", "class": "btn btn-secondary", "href": "/admin/projects/{id}", "mobile_friendly": false}]}}, "form_container": {"wrapper": {"class": "card", "background": "var(--surface-card)", "border_radius": "var(--radius-lg)", "box_shadow": "var(--shadow-md)", "border": "1px solid #E5E7EB"}, "card_header": {"padding": "var(--spacing-lg) var(--spacing-xl)", "border_bottom": "1px solid #E5E7EB", "background": "var(--bg-tertiary)", "content": "📋 Phase Information", "font_weight": "600"}, "card_body": {"padding": "var(--spacing-xl)", "background": "var(--surface-card)"}}, "form_layout": {"form_class": "phase-create-form", "method": "POST", "csrf_protection": true, "grid_system": {"container": {"display": "grid", "grid_template_columns": "1fr 1fr", "gap": "var(--spacing-xl)", "margin_bottom": "var(--spacing-xl)", "mobile_columns": "1fr"}, "left_column": {"fields": ["phase_code", "title", "status"]}, "right_column": {"fields": ["start_date", "end_date"]}, "full_width": {"fields": ["description"]}}}, "form_fields": {"phase_code": {"type": "text", "label": "Phase Code", "required": true, "placeholder": "e.g., PH001, INIT, PLAN", "max_length": 20, "help_text": "Unique identifier for this phase (max 20 characters)", "validation": "required|max_length[20]"}, "title": {"type": "text", "label": "Phase Title", "required": true, "placeholder": "e.g., Planning Phase, Implementation Phase", "max_length": 150, "help_text": "Descriptive name for this phase (max 150 characters)", "validation": "required|max_length[150]"}, "status": {"type": "select", "label": "Status", "required": true, "options": [{"value": "", "text": "Select Status"}, {"value": "active", "text": "Active"}, {"value": "deactivated", "text": "Deactivated"}], "help_text": "Current status of this phase", "validation": "required|in_list[active,deactivated]"}, "start_date": {"type": "date", "label": "Start Date", "required": false, "help_text": "When this phase is scheduled to start", "validation": "permit_empty|valid_date"}, "end_date": {"type": "date", "label": "End Date", "required": false, "help_text": "When this phase is scheduled to end", "validation": "permit_empty|valid_date"}, "description": {"type": "textarea", "label": "Description", "required": false, "rows": 4, "placeholder": "Detailed description of this phase, its objectives, and key activities...", "help_text": "Optional detailed description of the phase", "validation": "permit_empty"}}, "input_styling": {"base_class": "form-input", "width": "100%", "padding": "var(--spacing-md)", "background": "var(--surface-card)", "border": "1px solid #D1D5DB", "border_radius": "var(--radius-md)", "color": "var(--text-primary)", "font_size": "0.875rem", "transition": "all 0.3s ease", "focus_state": {"outline": "none", "border_color": "var(--brand-primary)", "box_shadow": "0 0 0 3px rgba(59, 130, 246, 0.1)"}, "validation_states": {"invalid": {"border_color": "var(--brand-danger)"}, "valid": {"border_color": "var(--brand-secondary)"}}}, "label_styling": {"class": "form-label", "font_weight": "600", "color": "var(--text-primary)", "margin_bottom": "var(--spacing-sm)", "font_size": "0.875rem", "required_indicator": {"element": "span", "color": "var(--brand-danger)", "content": "*"}}, "help_text_styling": {"element": "small", "color": "var(--text-muted)", "font_size": "0.75rem", "margin_top": "var(--spacing-xs)"}, "form_actions": {"container": {"display": "flex", "gap": "var(--spacing-md)", "justify_content": "flex-end", "padding_top": "var(--spacing-lg)", "border_top": "1px solid var(--border-color)"}, "buttons": [{"type": "link", "text": "Cancel", "class": "btn btn-secondary", "href": "/admin/projects/{id}", "mobile_friendly": false}, {"type": "submit", "text": "✅ Create Phase", "class": "btn btn-primary", "mobile_friendly": false}]}, "button_styling": {"base_button": {"class": "btn", "display": "inline-block", "padding": "var(--spacing-md) var(--spacing-xl)", "border": "none", "border_radius": "var(--radius-md)", "font_size": "0.875rem", "font_weight": "600", "text_decoration": "none", "text_align": "center", "cursor": "pointer", "transition": "all 0.3s ease", "line_height": "1.5", "white_space": "nowrap", "overflow": "hidden", "min_height": "auto", "min_width": "auto"}, "mobile_enhanced_button": {"class": "btn-mobile", "display": "flex", "align_items": "center", "justify_content": "center", "gap": "var(--spacing-sm)", "padding": "var(--spacing-md) var(--spacing-xl)", "font_size": "0.875rem", "line_height": "1.5", "min_height": "44px", "min_width": "44px", "touch_action": "manipulation", "webkit_tap_highlight_color": "transparent", "desktop_behavior": "Original size and appearance", "mobile_behavior": "Enhanced touch targets (48px minimum)"}, "primary_button": {"class": "btn-primary", "background": "var(--gradient-primary)", "color": "var(--text-white)", "box_shadow": "var(--shadow-md)", "hover_transform": "translateY(-2px)", "hover_shadow": "var(--shadow-lg)"}, "secondary_button": {"class": "btn-secondary", "background": "var(--bg-tertiary)", "color": "var(--text-secondary)", "border": "1px solid #D1D5DB", "hover_background": "#E5E7EB", "hover_color": "var(--text-primary)"}, "button_structure": {"icon_text_separation": {"icon_element": "<span class='btn-icon'>✅</span>", "text_element": "<span class='btn-text'>Create Phase</span>", "benefits": ["Better icon-text spacing control", "Responsive text handling", "Easier styling for different screen sizes", "Improved accessibility"]}}, "resolved_issues": {"mobile_touch_target": "44px desktop, 48px mobile", "text_overflow": "Ellipsis handling implemented", "responsive_padding": "Adaptive padding for mobile", "icon_spacing": "Proper icon-text spacing with gap", "loading_state": "Loading indicators implemented"}}, "mobile_enhancements_implemented": {"progressive_enhancement_strategy": {"desktop_first": "Original sizes and styling maintained", "mobile_enhanced": "Larger touch targets only on mobile devices", "breakpoint": "768px and below for mobile enhancements"}, "button_enhancements": {"desktop_specs": {"min_height": "44px", "min_width": "44px", "padding": "var(--spacing-md) var(--spacing-xl)", "font_size": "0.875rem"}, "mobile_specs": {"min_height": "48px", "min_width": "48px", "padding": "var(--spacing-md) var(--spacing-lg)", "font_size": "0.875rem", "full_width": true}, "responsive_behavior": {"text_overflow": "ellipsis", "white_space": "nowrap", "icon_text_gap": "var(--spacing-sm)", "touch_optimization": "manipulation, no tap highlight"}}, "form_input_enhancements": {"desktop_specs": {"min_height": "44px", "padding": "var(--spacing-md)", "font_size": "0.875rem", "border": "1px solid #D1D5DB"}, "mobile_specs": {"min_height": "48px", "padding": "var(--spacing-md)", "font_size": "16px", "ios_zoom_prevention": true}, "layout_adjustments": {"single_column_mobile": true, "grid_collapse_breakpoint": "768px", "button_stacking": "vertical on mobile", "full_width_buttons": true}, "enhanced_elements": {"select_dropdown": {"custom_arrow": true, "min_height": "44px desktop, 48px mobile"}, "textarea": {"resize": "vertical only", "min_height": "100px desktop, 120px mobile"}, "focus_indicators": {"border_width": "2px", "box_shadow": "0 0 0 3px rgba(59, 130, 246, 0.1)"}}}}, "css_variables_used": {"spacing": {"--spacing-xs": "0.25rem", "--spacing-sm": "0.5rem", "--spacing-md": "1rem", "--spacing-lg": "1.5rem", "--spacing-xl": "2rem", "--spacing-2xl": "3rem"}, "colors": {"--text-primary": "Primary text color", "--text-secondary": "Secondary text color", "--text-muted": "Muted text color", "--text-white": "White text", "--bg-secondary": "Secondary background", "--bg-tertiary": "Tertiary background", "--surface-card": "Card surface color", "--surface-sidebar": "Sidebar surface", "--brand-primary": "Primary brand color", "--brand-secondary": "Secondary brand color", "--brand-danger": "Danger/error color", "--border-color": "Border color"}, "effects": {"--radius-md": "0.5rem", "--radius-lg": "0.75rem", "--shadow-md": "Medium shadow", "--shadow-lg": "Large shadow", "--gradient-primary": "Primary gradient"}}, "responsive_breakpoints": {"mobile": "max-width: 768px", "tablet": "769px to 1024px", "desktop": "min-width: 1025px"}, "accessibility_features": {"current": ["CSRF protection", "Form labels", "Required field indicators", "Help text", "Focus states"], "improvements_needed": ["ARIA labels", "Error announcements", "Loading states", "Keyboard navigation", "Screen reader support"]}, "implementation_guide": {"html_structure": {"button_markup": {"standard": "<button class='btn btn-primary btn-mobile'>Text</button>", "with_icon": "<button class='btn btn-primary btn-mobile'><span class='btn-icon'>✅</span><span class='btn-text'>Create Phase</span></button>", "link_button": "<a href='#' class='btn btn-secondary btn-mobile'><span class='btn-icon'>←</span><span class='btn-text'>Back</span></a>"}, "form_structure": {"container": "<div class='form-actions'>", "responsive_grid": "grid-template-columns: 1fr 1fr; @media (max-width: 768px) { grid-template-columns: 1fr !important; }"}}, "css_implementation": {"required_classes": [".btn-mobile", ".btn-icon", ".btn-text", ".form-actions"], "responsive_strategy": {"approach": "Progressive enhancement", "desktop_first": "Original PROMIS design maintained", "mobile_enhanced": "Touch-friendly improvements at 768px breakpoint", "very_small_screens": "Additional optimizations at 480px breakpoint"}, "key_css_rules": {"base_mobile_button": ".btn-mobile { min-height: 44px; display: flex; align-items: center; gap: var(--spacing-sm); }", "mobile_enhancement": "@media (max-width: 768px) { .btn-mobile { min-height: 48px; width: 100%; } }", "touch_optimization": "touch-action: manipulation; -webkit-tap-highlight-color: transparent;"}}, "consistency_standards": {"button_requirements": ["All buttons must use .btn-mobile class", "Icon-text structure with separate spans", "Minimum 44px height on desktop", "Minimum 48px height on mobile", "Full-width stacking on mobile"], "form_requirements": ["Single column layout on mobile", "44px minimum input height on desktop", "48px minimum input height on mobile", "16px font size on mobile to prevent zoom", "Consistent spacing using CSS variables"], "responsive_requirements": ["768px breakpoint for mobile enhancements", "480px breakpoint for very small screens", "Grid collapse to single column", "Button stacking and full-width behavior"]}, "reusable_components": {"button_component": {"file_location": "Should be added to promis_admin_template.php", "css_classes": "btn-mobile, btn-icon, btn-text", "usage": "Apply to all buttons across admin portal"}, "form_component": {"file_location": "Should be added to promis_admin_template.php", "css_classes": "form-actions, enhanced form inputs", "usage": "Apply to all forms across admin portal"}}, "rollout_strategy": {"phase_1": "Update promis_admin_template.php with base styles", "phase_2": "Update high-traffic pages (dashboard, project list)", "phase_3": "Update CRUD forms (create, edit pages)", "phase_4": "Update remaining admin pages", "testing": "Test on mobile devices at each phase"}}}}