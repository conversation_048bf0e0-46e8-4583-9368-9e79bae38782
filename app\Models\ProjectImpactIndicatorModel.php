<?php

namespace App\Models;

/**
 * Project Impact Indicator Model
 * 
 * Handles project impact indicators with baseline, target, and actual values.
 */
class ProjectImpactIndicatorModel extends BaseModel
{
    protected $table      = 'project_impact_indicators';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'indicator_text', 'baseline_value', 'baseline_date', 'target_value', 
        'target_date', 'actual_value', 'actual_date', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'      => 'required|integer',
        'indicator_text'  => 'required|max_length[255]',
        'baseline_value'  => 'permit_empty|max_length[15]',
        'target_value'    => 'permit_empty|max_length[15]',
        'actual_value'    => 'permit_empty|max_length[15]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'indicator_text' => [
            'required' => 'Indicator text is required'
        ]
    ];
    
    /**
     * Get indicators by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }
    

    
    /**
     * Update actual value
     */
    public function updateActualValue(int $indicatorId, string $actualValue, ?int $updatedBy = null): bool
    {
        return $this->update($indicatorId, [
            'actual_value' => $actualValue,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Get project impact summary
     */
    public function getProjectImpactSummary(int $projectId): array
    {
        $indicators = $this->getByProject($projectId);
        
        $summary = [
            'total_indicators' => count($indicators),
            'indicators_with_actual' => 0,
            'targets_achieved' => 0,
            'targets_exceeded' => 0,
            'targets_not_met' => 0,
            'average_achievement_rate' => 0
        ];
        
        $totalAchievementRate = 0;
        $indicatorsWithActual = 0;
        
        foreach ($indicators as $indicator) {
            if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== '') {
                $summary['indicators_with_actual']++;
                $indicatorsWithActual++;

                $targetValue = $indicator['target_value'];
                $actualValue = $indicator['actual_value'];

                // Only perform calculations if both values are numeric
                if (is_numeric($targetValue) && is_numeric($actualValue) && $targetValue > 0) {
                    $achievementRate = ((float)$actualValue / (float)$targetValue) * 100;
                    $totalAchievementRate += $achievementRate;

                    if ((float)$actualValue >= (float)$targetValue) {
                        if ((float)$actualValue > (float)$targetValue) {
                            $summary['targets_exceeded']++;
                        } else {
                            $summary['targets_achieved']++;
                        }
                    } else {
                        $summary['targets_not_met']++;
                    }
                }
            }
        }
        
        if ($indicatorsWithActual > 0) {
            $summary['average_achievement_rate'] = round($totalAchievementRate / $indicatorsWithActual, 2);
        }
        
        return $summary;
    }
    
    /**
     * Get indicator performance analysis
     */
    public function getIndicatorPerformance(int $projectId): array
    {
        $indicators = $this->getByProject($projectId);
        $performance = [];
        
        foreach ($indicators as $indicator) {
            $analysis = [
                'id' => $indicator['id'],
                'indicator_text' => $indicator['indicator_text'],
                'baseline_value' => $indicator['baseline_value'],
                'baseline_date' => $indicator['baseline_date'],
                'target_value' => $indicator['target_value'],
                'target_date' => $indicator['target_date'],
                'actual_value' => $indicator['actual_value'],
                'actual_date' => $indicator['actual_date'],
                'improvement_from_baseline' => null,
                'achievement_rate' => null,
                'status' => 'pending'
            ];
            
            if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== '') {
                // Calculate improvement from baseline (only if both values are numeric)
                $baselineValue = $indicator['baseline_value'];
                $actualValue = $indicator['actual_value'];

                if (is_numeric($baselineValue) && is_numeric($actualValue) && $baselineValue != 0) {
                    $analysis['improvement_from_baseline'] = round((((float)$actualValue - (float)$baselineValue) / (float)$baselineValue) * 100, 2);
                }

                // Calculate achievement rate (only if both values are numeric)
                $targetValue = $indicator['target_value'];
                if (is_numeric($targetValue) && is_numeric($actualValue) && $targetValue != 0) {
                    $analysis['achievement_rate'] = round(((float)$actualValue / (float)$targetValue) * 100, 2);

                    if ((float)$actualValue >= (float)$targetValue) {
                        $analysis['status'] = (float)$actualValue > (float)$targetValue ? 'exceeded' : 'achieved';
                    } else {
                        $analysis['status'] = 'not_met';
                    }
                } else if (!is_numeric($targetValue) || !is_numeric($actualValue)) {
                    // For text values, just mark as completed
                    $analysis['status'] = 'completed';
                }
            }
            
            $performance[] = $analysis;
        }
        
        return $performance;
    }
    
    /**
     * Get indicators statistics
     */
    public function getIndicatorStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total indicators
        $stats['total_indicators'] = $query->countAllResults();
        
        // Indicators with actual values
        $withActualQuery = $this->where('actual_value IS NOT NULL');
        if ($projectId) {
            $withActualQuery = $withActualQuery->where('project_id', $projectId);
        }
        $stats['with_actual_values'] = $withActualQuery->countAllResults();
        
        // Average values (only for numeric values)
        $avgResult = $query->select('AVG(CASE WHEN baseline_value REGEXP \'^[0-9]+(\.[0-9]+)?$\' THEN CAST(baseline_value AS DECIMAL(10,2)) END) as avg_baseline,
                                    AVG(CASE WHEN target_value REGEXP \'^[0-9]+(\.[0-9]+)?$\' THEN CAST(target_value AS DECIMAL(10,2)) END) as avg_target,
                                    AVG(CASE WHEN actual_value REGEXP \'^[0-9]+(\.[0-9]+)?$\' THEN CAST(actual_value AS DECIMAL(10,2)) END) as avg_actual')
                          ->first();

        $stats['averages'] = [
            'baseline' => round((float) ($avgResult['avg_baseline'] ?? 0), 2),
            'target' => round((float) ($avgResult['avg_target'] ?? 0), 2),
            'actual' => round((float) ($avgResult['avg_actual'] ?? 0), 2)
        ];
        

        
        return $stats;
    }
    
    /**
     * Get top performing indicators (only for numeric values)
     */
    public function getTopPerformingIndicators(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this->select('*, (CASE WHEN actual_value REGEXP \'^[0-9]+(\.[0-9]+)?$\' AND target_value REGEXP \'^[0-9]+(\.[0-9]+)?$\'
                                    THEN (CAST(actual_value AS DECIMAL(10,2)) / CAST(target_value AS DECIMAL(10,2)) * 100)
                                    ELSE NULL END) as achievement_rate')
                     ->where('actual_value IS NOT NULL')
                     ->where('actual_value !=', '')
                     ->where('target_value IS NOT NULL')
                     ->where('target_value !=', '')
                     ->where('actual_value REGEXP', '^[0-9]+(\.[0-9]+)?$')
                     ->where('target_value REGEXP', '^[0-9]+(\.[0-9]+)?$')
                     ->where('CAST(target_value AS DECIMAL(10,2)) >', 0);

        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }

        return $query->orderBy('achievement_rate', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
    

}
