<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * Unified Audit Log Model
 *
 * This model handles audit logs for all portals in the PROMIS application.
 * It provides methods to query audit logs by portal, user, table, etc.
 */
class AuditLogModel extends Model
{
    protected $table      = 'audit_logs';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'table_name', 'primary_key', 'action', 'old_data', 'new_data',
        'user_id', 'username', 'user_type', 'user_full_name',
        'organization_id', 'organization_name', 'organization_type',
        'project_id', 'project_title', 'portal', 'module',
        'ip_address', 'user_agent', 'session_id', 'request_url',
        'description', 'created_at',
    ];

    public $timestamps   = false; // we already store created_at manually

    /**
     * Get audit logs for a specific portal
     */
    public function getPortalLogs(string $portal, int $limit = 20): array
    {
        return $this->where('portal', $portal)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get audit logs for a specific table
     */
    public function getTableLogs(string $tableName, ?string $portal = null, int $limit = 20): array
    {
        $query = $this->where('table_name', $tableName);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs for a specific record
     */
    public function getRecordLogs(string $tableName, string $primaryKey, ?string $portal = null, int $limit = 20): array
    {
        $query = $this->where('table_name', $tableName)
                     ->where('primary_key', $primaryKey);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs by user
     */
    public function getUserLogs(int $userId, string $userType, ?string $portal = null, int $limit = 20): array
    {
        $query = $this->where('user_id', $userId)
                     ->where('user_type', $userType);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs by organization
     */
    public function getOrganizationLogs(int $organizationId, ?string $portal = null, int $limit = 50): array
    {
        $query = $this->where('organization_id', $organizationId);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit logs by project
     */
    public function getProjectLogs(int $projectId, ?string $portal = null, int $limit = 50): array
    {
        $query = $this->where('project_id', $projectId);

        if ($portal) {
            $query = $query->where('portal', $portal);
        }

        return $query->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get organization activity summary
     */
    public function getOrganizationActivitySummary(int $organizationId, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        $query = $this->select('portal, action, COUNT(*) as count')
                     ->where('organization_id', $organizationId);

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        return $query->groupBy('portal, action')
                    ->orderBy('portal, action')
                    ->findAll();
    }

    /**
     * Get user activity summary
     */
    public function getUserActivitySummary(int $userId, string $userType, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        $query = $this->select('portal, action, COUNT(*) as count')
                     ->where('user_id', $userId)
                     ->where('user_type', $userType);

        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        return $query->groupBy('portal, action')
                    ->orderBy('portal, action')
                    ->findAll();
    }

    /**
     * Get audit logs by session
     */
    public function getSessionLogs(string $sessionId, int $limit = 20): array
    {
        return $this->where('session_id', $sessionId)
                   ->orderBy('id', 'DESC')
                   ->findAll($limit);
    }

    /**
     * Get recent activity across all portals
     */
    public function getRecentActivity(int $limit = 50): array
    {
        return $this->orderBy('id', 'DESC')->findAll($limit);
    }

    /**
     * Get audit statistics for a specific portal
     */
    public function getPortalAuditStats(string $portal): array
    {
        $stats = [];

        // Total logs for this portal
        $stats['total_logs'] = $this->where('portal', $portal)->countAllResults();

        // Logs by action
        $stats['by_action'] = $this->select('action, COUNT(*) as count')
                                  ->where('portal', $portal)
                                  ->groupBy('action')
                                  ->findAll();

        // Most active users
        $stats['active_users'] = $this->select('username, user_type, COUNT(*) as count')
                                      ->where('portal', $portal)
                                      ->where('username IS NOT NULL')
                                      ->groupBy('username, user_type')
                                      ->orderBy('count', 'DESC')
                                      ->findAll(10);

        // Most modified tables
        $stats['active_tables'] = $this->select('table_name, COUNT(*) as count')
                                       ->where('portal', $portal)
                                       ->groupBy('table_name')
                                       ->orderBy('count', 'DESC')
                                       ->findAll(10);

        // Activity by module
        $stats['by_module'] = $this->select('module, COUNT(*) as count')
                                  ->where('portal', $portal)
                                  ->where('module IS NOT NULL')
                                  ->groupBy('module')
                                  ->orderBy('count', 'DESC')
                                  ->findAll();

        // Activity by organization
        $stats['by_organization'] = $this->select('organization_name, organization_type, COUNT(*) as count')
                                        ->where('portal', $portal)
                                        ->where('organization_id IS NOT NULL')
                                        ->groupBy('organization_name, organization_type')
                                        ->orderBy('count', 'DESC')
                                        ->findAll(10);

        // Activity by project (if applicable)
        $stats['by_project'] = $this->select('project_title, COUNT(*) as count')
                                   ->where('portal', $portal)
                                   ->where('project_id IS NOT NULL')
                                   ->groupBy('project_title')
                                   ->orderBy('count', 'DESC')
                                   ->findAll(10);

        return $stats;
    }

    /**
     * Get cross-portal audit statistics
     */
    public function getCrossPortalStats(): array
    {
        $stats = [];

        // Total logs across all portals
        $stats['total_logs'] = $this->countAllResults();

        // Logs by portal
        $stats['by_portal'] = $this->select('portal, COUNT(*) as count')
                                  ->groupBy('portal')
                                  ->orderBy('count', 'DESC')
                                  ->findAll();

        // Logs by action across all portals
        $stats['by_action'] = $this->select('action, COUNT(*) as count')
                                  ->groupBy('action')
                                  ->orderBy('count', 'DESC')
                                  ->findAll();

        // Most active users across all portals
        $stats['active_users'] = $this->select('username, user_full_name, user_type, portal, organization_name, COUNT(*) as count')
                                      ->where('username IS NOT NULL')
                                      ->groupBy('username, user_full_name, user_type, portal, organization_name')
                                      ->orderBy('count', 'DESC')
                                      ->findAll(15);

        // Most active organizations across all portals
        $stats['active_organizations'] = $this->select('organization_name, organization_type, COUNT(*) as count')
                                             ->where('organization_id IS NOT NULL')
                                             ->groupBy('organization_name, organization_type')
                                             ->orderBy('count', 'DESC')
                                             ->findAll(10);

        // Cross-portal project activity
        $stats['project_activity'] = $this->select('project_title, portal, COUNT(*) as count')
                                          ->where('project_id IS NOT NULL')
                                          ->groupBy('project_title, portal')
                                          ->orderBy('count', 'DESC')
                                          ->findAll(15);

        return $stats;
    }
}
