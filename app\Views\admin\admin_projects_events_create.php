<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/events') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Events
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-plus-circle me-2"></i>
            Create New Event
        </h1>
        <p class="text-muted mb-0">
            Add a new event for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Event Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-calendar-event me-2"></i>
            Event Information
        </h5>
    </div>

    <div class="card-body">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/events/create') ?>" class="event-create-form">
            <?= csrf_field() ?>

            <!-- Basic Information Section -->
            <div class="mb-5">
                <h5 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-info-circle me-2"></i>
                    Basic Information
                </h5>

                <div class="row g-4 mb-4">
                    <!-- Event Title -->
                    <div class="col-md-8">
                        <label for="title" class="form-label fw-semibold">
                            Event Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" id="title" name="title" class="form-control border-danger"
                               value="<?= old('title') ?>" placeholder="Brief title describing the event" required>
                        <div class="form-text">
                            Clear, concise title for the event (max 150 characters)
                        </div>
                    </div>

                    <!-- Event Date -->
                    <div class="col-md-4">
                        <label for="event_date" class="form-label fw-semibold">
                            Event Date <span class="text-danger">*</span>
                        </label>
                        <input type="date" id="event_date" name="event_date" class="form-control border-danger"
                               value="<?= old('event_date', date('Y-m-d')) ?>" required>
                        <div class="form-text">
                            When the event occurred
                        </div>
                    </div>
                </div>

                <!-- Event Description -->
                <div class="mb-4">
                    <label for="description" class="form-label fw-semibold">
                        Event Description <span class="text-danger">*</span>
                    </label>
                    <textarea id="description" name="description" class="form-control border-danger"
                              rows="4" placeholder="Detailed description of what happened..."
                              required><?= old('description') ?></textarea>
                    <div class="form-text">
                        Detailed description of the event, its circumstances, and immediate effects
                    </div>
                </div>
            </div>

            <!-- Classification Section -->
            <div class="mb-5">
                <h5 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-tags me-2"></i>
                    Event Classification
                </h5>

                <div class="row g-4 mb-4">
                    <!-- Event Type -->
                    <div class="col-md-6">
                        <label for="event_type" class="form-label fw-semibold">
                            Event Type <span class="text-danger">*</span>
                        </label>
                        <select id="event_type" name="event_type" class="form-select border-danger" required>
                            <option value="">Select Event Type</option>
                            <option value="delay" <?= old('event_type') === 'delay' ? 'selected' : '' ?>>🕐 Delay</option>
                            <option value="suspension" <?= old('event_type') === 'suspension' ? 'selected' : '' ?>>⏸️ Suspension</option>
                            <option value="resumption" <?= old('event_type') === 'resumption' ? 'selected' : '' ?>>▶️ Resumption</option>
                            <option value="incident" <?= old('event_type') === 'incident' ? 'selected' : '' ?>>⚠️ Incident</option>
                            <option value="natural_disaster" <?= old('event_type') === 'natural_disaster' ? 'selected' : '' ?>>🌪️ Natural Disaster</option>
                            <option value="funding_issue" <?= old('event_type') === 'funding_issue' ? 'selected' : '' ?>>💰 Funding Issue</option>
                            <option value="resource_issue" <?= old('event_type') === 'resource_issue' ? 'selected' : '' ?>>🔧 Resource Issue</option>
                            <option value="stakeholder_issue" <?= old('event_type') === 'stakeholder_issue' ? 'selected' : '' ?>>👥 Stakeholder Issue</option>
                            <option value="other" <?= old('event_type') === 'other' ? 'selected' : '' ?>>❓ Other</option>
                        </select>
                        <div class="form-text">
                            Category that best describes this event
                        </div>
                    </div>

                    <!-- Severity -->
                    <div class="col-md-6">
                        <label for="severity" class="form-label fw-semibold">
                            Severity Level <span class="text-danger">*</span>
                        </label>
                        <select id="severity" name="severity" class="form-select border-danger" required>
                            <option value="">Select Severity</option>
                            <option value="low" <?= old('severity') === 'low' ? 'selected' : '' ?>>🟢 Low - Minor impact</option>
                            <option value="medium" <?= old('severity') === 'medium' ? 'selected' : '' ?>>🟡 Medium - Moderate impact</option>
                            <option value="high" <?= old('severity') === 'high' ? 'selected' : '' ?>>🟠 High - Significant impact</option>
                            <option value="critical" <?= old('severity') === 'critical' ? 'selected' : '' ?>>🔴 Critical - Severe impact</option>
                        </select>
                        <div class="form-text">
                            How severe is the impact of this event
                        </div>
                    </div>
                </div>

                <!-- Associated Milestone -->
                <div class="mb-4">
                    <label for="milestone_id" class="form-label fw-semibold">
                        Associated Milestone
                    </label>
                    <select id="milestone_id" name="milestone_id" class="form-select border-success">
                        <option value="">No specific milestone</option>
                        <?php foreach ($milestones as $milestone): ?>
                            <option value="<?= $milestone['id'] ?>" <?= old('milestone_id') == $milestone['id'] ? 'selected' : '' ?>>
                                <?= esc($milestone['milestone_code']) ?> - <?= esc($milestone['title']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="form-text">
                        Optionally link this event to a specific project milestone
                    </div>
                </div>
            </div>

            <!-- Impact Assessment Section -->
            <div class="mb-5">
                <h5 class="fw-semibold text-primary mb-4 pb-2 border-bottom">
                    <i class="bi bi-graph-down me-2"></i>
                    Impact Assessment
                </h5>

                <div class="row g-4 mb-4">
                    <!-- Impact Days -->
                    <div class="col-md-6">
                        <label for="impact_days" class="form-label fw-semibold">
                            Impact Duration (Days)
                        </label>
                        <input type="number" id="impact_days" name="impact_days" class="form-control border-success"
                               value="<?= old('impact_days') ?>" placeholder="0" min="0" step="1">
                        <div class="form-text">
                            Number of days this event may delay the project (optional)
                        </div>
                    </div>
                </div>

                <!-- Impact Description -->
                <div class="mb-4">
                    <label for="impact_description" class="form-label fw-semibold">
                        Impact Description
                    </label>
                    <textarea id="impact_description" name="impact_description" class="form-control border-success"
                              rows="3" placeholder="Describe the impact this event has on the project..."><?= old('impact_description') ?></textarea>
                    <div class="form-text">
                        Detailed description of how this event affects the project timeline, budget, or deliverables (optional)
                    </div>
                </div>
            </div>

            <!-- Examples Section -->
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading">
                    <i class="bi bi-lightbulb me-2"></i>
                    Event Examples
                </h6>
                <div class="small">
                    <div class="row">
                        <div class="col-md-6">
                            <div><strong>Delays:</strong></div>
                            <ul class="mb-2">
                                <li>Weather delays construction</li>
                                <li>Equipment delivery postponed</li>
                                <li>Permit approval delayed</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <div><strong>Incidents:</strong></div>
                            <ul class="mb-2">
                                <li>Equipment malfunction</li>
                                <li>Safety incident on site</li>
                                <li>Quality control issue</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/events') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-check-circle me-2"></i>
                    Create Event
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>
