<?php /** @var array $organization */ /** @var array $images */ /** @var array $stats */ ?>

<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/edit') ?>" class="btn btn-secondary">
    ✏️ Edit Organization
</a>
<a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins') ?>" class="btn btn-secondary">
    👥 Manage Admins
</a>
<a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-primary">
    ← Back to Organizations
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Organization Header -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-lg);">
            <?php if ($organization['logo_path']): ?>
                <img src="<?= base_url($organization['logo_path']) ?>" alt="Organization Logo"
                     style="width: 80px; height: 80px; border-radius: var(--radius-lg); object-fit: cover;">
            <?php else: ?>
                <div style="width: 80px; height: 80px; border-radius: var(--radius-lg); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700;">
                    <?= strtoupper(substr($organization['name'], 0, 1)) ?>
                </div>
            <?php endif; ?>

            <div style="flex: 1;">
                <h1 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 2rem;"><?= esc($organization['name']) ?></h1>
                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-sm);">
                    <span style="font-family: var(--font-mono); font-weight: 600; color: var(--text-secondary);"><?= esc($organization['org_code']) ?></span>
                    <span class="status-badge status-<?= $organization['is_active'] ? 'active' : 'inactive' ?>">
                        <?= $stats['status'] ?>
                    </span>
                    <span class="status-badge status-<?= $organization['license_status'] ?>">
                        <?= $stats['license_status'] ?>
                    </span>
                </div>
                <?php if ($organization['description']): ?>
                    <p style="color: var(--text-secondary); margin: 0;"><?= esc($organization['description']) ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Organization Details -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
        <!-- Main Information -->
        <div class="card">
            <div class="card-header">Organization Information</div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                <div>
                    <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Contact Email</div>
                    <div style="color: var(--text-primary); font-weight: 500;">
                        <?= $organization['contact_email'] ? esc($organization['contact_email']) : 'Not provided' ?>
                    </div>
                </div>

                <div>
                    <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Contact Phone</div>
                    <div style="color: var(--text-primary); font-weight: 500;">
                        <?= $organization['contact_phone'] ? esc($organization['contact_phone']) : 'Not provided' ?>
                    </div>
                </div>

                <div>
                    <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Website</div>
                    <div style="color: var(--text-primary); font-weight: 500;">
                        <?php if ($organization['website_url']): ?>
                            <a href="<?= esc($organization['website_url']) ?>" target="_blank" style="color: #00D4FF; text-decoration: none;">
                                <?= esc($organization['website_url']) ?>
                            </a>
                        <?php else: ?>
                            Not provided
                        <?php endif; ?>
                    </div>
                </div>

                <div>
                    <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Address</div>
                    <div style="color: var(--text-primary); font-weight: 500;">
                        <?php if ($organization['address_line1']): ?>
                            <?= esc($organization['address_line1']) ?><br>
                            <?php if ($organization['address_line2']): ?>
                                <?= esc($organization['address_line2']) ?><br>
                            <?php endif; ?>
                            <?= esc($organization['city']) ?>, <?= esc($organization['state']) ?> <?= esc($organization['postal_code']) ?><br>
                            <?= esc($organization['country']) ?>
                        <?php else: ?>
                            Not provided
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card">
            <div class="card-header">Statistics</div>

            <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                <div>
                    <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Created Date</div>
                    <div style="color: var(--text-primary); font-weight: 500;"><?= date('F j, Y', strtotime($stats['created_date'])) ?></div>
                </div>

                <div>
                    <div style="font-size: 0.875rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Last Updated</div>
                    <div style="color: var(--text-primary); font-weight: 500;"><?= date('F j, Y', strtotime($stats['last_updated'])) ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organization Images -->
    <div class="card">
        <div class="card-header">Organization Images</div>

        <?php if (!empty($images)): ?>
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: var(--spacing-lg);">
                <?php foreach ($images as $img): ?>
                    <div style="border-radius: var(--radius-md); overflow: hidden; background: var(--glass-bg); border: 1px solid var(--glass-border);">
                        <img src="<?= base_url($img['image_path']) ?>" alt="Organization Image"
                             style="width: 100%; height: 150px; object-fit: cover;">
                        <?php if (!empty($img['caption'])): ?>
                            <div style="padding: var(--spacing-md); font-size: 0.875rem; color: var(--text-secondary);">
                                <?= esc($img['caption']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-tertiary);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🖼️</div>
                <div>No images uploaded for this organization</div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(6, 255, 165, 0.1);
    color: #06FFA5;
}

.status-inactive {
    background: rgba(124, 128, 145, 0.1);
    color: var(--text-muted);
}

.status-paid {
    background: rgba(0, 212, 255, 0.1);
    color: #00D4FF;
}

.status-unpaid {
    background: rgba(255, 0, 110, 0.1);
    color: #FF006E;
}
</style>
<?= $this->endSection() ?>