<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>Back to Documents
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-file-earmark-plus me-2"></i>Upload Project Document
        </h1>
        <p class="text-muted mb-0">
            Add a new document to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Upload Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <i class="bi bi-file-earmark-plus me-2"></i>Document Upload Form
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= esc(session()->getFlashdata('error')) ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" enctype="multipart/form-data">
                    <?= csrf_field() ?>

                    <!-- Document Category -->
                    <div class="mb-3">
                        <label for="category" class="form-label fw-semibold">Document Category</label>
                        <select name="category" id="category" class="form-select border-success">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $key => $label): ?>
                                <option value="<?= $key ?>" <?= (old('category') == $key) ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Choose the appropriate document category (optional)</div>
                    </div>

                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label fw-semibold">Description</label>
                        <textarea name="description" id="description" rows="3" maxlength="255"
                                  class="form-control border-success" style="resize: vertical;"><?= old('description') ?></textarea>
                        <div class="form-text">Optional description or notes about the document (max 255 characters)</div>
                    </div>

                    <!-- Document File -->
                    <div class="mb-4">
                        <label for="document_file" class="form-label fw-semibold">
                            Document File <span class="text-danger">*</span>
                        </label>
                        <input type="file" id="document_file" name="document_file" required
                               class="form-control border-danger">
                        <div class="form-text">
                            <strong>File Requirements:</strong>
                            <ul class="mt-2 mb-0">
                                <li>Maximum file size: 25MB</li>
                                <li>Allowed file types: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, ODT, ODS, ODP, JPG, JPEG, PNG, GIF, BMP, TIFF, SVG, ZIP, RAR, 7Z, TAR, GZ, CSV, XML, JSON</li>
                                <li>Executable files (.exe, .bat) and application files are not permitted</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="alert alert-info d-flex align-items-start">
                        <i class="bi bi-info-circle-fill me-2 mt-1"></i>
                        <div>
                            <strong>Important:</strong> Once uploaded, the document will be stored securely and can be downloaded by authorized users.
                            You can update the document details or replace the file later if needed.
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between gap-3">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-file-earmark-plus me-2"></i>Upload Document
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// File size validation and preview
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const maxSize = 25 * 1024 * 1024; // 25MB in bytes
        if (file.size > maxSize) {
            alert('File size exceeds 25MB limit. Please choose a smaller file.');
            e.target.value = '';
            return;
        }

        // Display file info
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.remove();
        }

        const info = document.createElement('div');
        info.id = 'fileInfo';
        info.className = 'alert alert-success mt-2';
        info.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-check-circle-fill me-2"></i>
                <div>
                    <strong>Selected file:</strong> ${file.name}<br>
                    <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                    <strong>Type:</strong> ${file.type || 'Unknown'}
                </div>
            </div>
        `;
        e.target.parentNode.appendChild(info);
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const file = document.getElementById('document_file').files[0];

    if (!file) {
        alert('Please select a file to upload.');
        e.preventDefault();
        return;
    }

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Uploading...';
});
</script>

<style>
/* Color-coded Form Input System */
.border-success {
    border-color: #198754 !important;
    border-width: 2px !important;
}

.border-success:focus {
    border-color: #146c43 !important;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25) !important;
}

.border-danger {
    border-color: #dc3545 !important;
    border-width: 2px !important;
}

.border-danger:focus {
    border-color: #b02a37 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
}

/* File input styling */
.form-control[type="file"] {
    cursor: pointer;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background-color: #0d6efd;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    margin-right: 0.75rem;
    cursor: pointer;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background-color: #0b5ed7;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .card {
        margin: 0.5rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 0.75rem;
    }

    .d-flex.justify-content-between .btn {
        width: 100%;
    }
}
</style>
<?= $this->endSection() ?>
