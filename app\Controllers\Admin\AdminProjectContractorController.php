<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectContractorModel;
use App\Models\OrganizationModel;

/**
 * Admin Project Contractor Controller
 * 
 * Handles contractor assignment and management for projects in the PROMIS Admin Portal.
 * Follows RESTful approach with separate methods for each operation.
 */
class AdminProjectContractorController extends BaseController
{
    protected $projectModel;
    protected $projectContractorModel;
    protected $organizationModel;
    
    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectContractorModel = new ProjectContractorModel();
        $this->organizationModel = new OrganizationModel();
    }

    /**
     * Display project contractors list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project contractors (including inactive for history)
        $contractors = $this->projectContractorModel->getByProject($projectId, false);

        // Get contractor statistics
        $stats = [
            'total_contractors' => count($contractors),
            'active_contractors' => count(array_filter($contractors, fn($c) => $c['is_active'] == 1)),
            'by_client_flag' => [
                'positive' => 0,
                'neutral' => 0,
                'negative' => 0
            ]
        ];

        foreach ($contractors as $contractor) {
            if ($contractor['is_active'] == 1 && isset($stats['by_client_flag'][$contractor['client_flag']])) {
                $stats['by_client_flag'][$contractor['client_flag']]++;
            }
        }

        $data = [
            'title' => 'Project Contractors - PROMIS Admin',
            'page_title' => 'Project Contractors',
            'project' => $project,
            'contractors' => $contractors,
            'stats' => $stats
        ];

        return view('admin/admin_projects_contractors_list', $data);
    }

    /**
     * Show assign contractor form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get available organizations that can be assigned as contractors
        $availableOrganizations = $this->organizationModel->where('is_active', 1)
                                                          ->where('deleted_at', null)
                                                          ->orderBy('name', 'ASC')
                                                          ->findAll();

        // Get current project contractors to exclude from dropdown
        $currentContractors = $this->projectContractorModel->getByProject($projectId, true);
        $currentContractorIds = array_column($currentContractors, 'contractor_id');

        // Filter out already assigned contractors
        $availableOrganizations = array_filter($availableOrganizations, function($org) use ($currentContractorIds) {
            return !in_array($org['id'], $currentContractorIds);
        });

        $data = [
            'title' => 'Assign Project Contractor - PROMIS Admin',
            'page_title' => 'Assign Project Contractor',
            'project' => $project,
            'availableOrganizations' => $availableOrganizations,
            'currentContractors' => $currentContractors
        ];

        return view('admin/admin_projects_contractors_create', $data);
    }

    /**
     * Process assign contractor form - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'contractor_id' => 'required|integer',
            'joined_at' => 'required|valid_date',
            'client_flag' => 'in_list[positive,neutral,negative]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $contractorId = $this->request->getPost('contractor_id');
        $joinedAt = $this->request->getPost('joined_at');
        $clientFlag = $this->request->getPost('client_flag') ?: 'neutral';

        // Verify the organization exists and is active
        $organization = $this->organizationModel->where('id', $contractorId)
                                               ->where('is_active', 1)
                                               ->where('deleted_at', null)
                                               ->first();

        if (!$organization) {
            return redirect()->back()->withInput()->with('error', 'Selected organization not found or inactive.');
        }

        // Check if contractor is already assigned
        if ($this->projectContractorModel->isContractorAssigned($projectId, $contractorId)) {
            return redirect()->back()->withInput()->with('error', 'This organization is already assigned as a contractor to this project.');
        }

        try {
            $success = $this->projectContractorModel->addContractor($projectId, $contractorId, $joinedAt, $adminUserId);
            
            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/contractors'))
                               ->with('success', 'Contractor assigned successfully!');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to assign contractor. They may already be assigned to this project.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error assigning contractor: ' . $e->getMessage());
        }
    }

    /**
     * Remove contractor from project - POST request
     */
    public function remove($projectId, $contractorId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validate removal reason
        $removalReason = $this->request->getPost('removal_reason');
        if (empty($removalReason)) {
            return redirect()->back()->with('error', 'Removal reason is required.');
        }

        try {
            $success = $this->projectContractorModel->removeContractor($projectId, $contractorId, $removalReason, $adminUserId);
            
            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/contractors'))
                               ->with('success', 'Contractor removed successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to remove contractor. They may not be assigned to this project.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error removing contractor: ' . $e->getMessage());
        }
    }
}
