<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary">
    ← Back to Indicators List
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Impact Indicator
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update impact metric for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Indicator Form -->
<div class="card">
    <div class="card-header">
        📊 Impact Indicator Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/indicators/' . $indicator['id'] . '/edit') ?>" class="indicator-edit-form">
            <?= csrf_field() ?>

            <!-- Indicator Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="indicator_text" class="form-label">
                    Indicator Description <span style="color: var(--brand-danger);">*</span>
                </label>
                <textarea id="indicator_text"
                          name="indicator_text"
                          class="form-input"
                          style="min-height: 120px; resize: vertical;"
                          placeholder="e.g., Number of beneficiaries served, Percentage increase in literacy rate, Reduction in travel time..."
                          required><?= old('indicator_text', $indicator['indicator_text']) ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Clear description of what will be measured for impact assessment (max 255 characters)
                </small>
                <?php if (isset($errors['indicator_text'])): ?>
                    <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                        <?= esc($errors['indicator_text']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column - Baseline Information -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        📈 Baseline Information
                    </h3>

                    <!-- Baseline Value -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="baseline_value" class="form-label">
                            Baseline Value
                        </label>
                        <input type="text"
                               id="baseline_value"
                               name="baseline_value"
                               class="form-input"
                               value="<?= old('baseline_value', $indicator['baseline_value']) ?>"
                               placeholder="e.g., 0, 50%, Low, None"
                               maxlength="15">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Starting value before project implementation - can be numbers or text (optional)
                        </small>
                        <?php if (isset($errors['baseline_value'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['baseline_value']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Baseline Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="baseline_date" class="form-label">
                            Baseline Date
                        </label>
                        <input type="date"
                               id="baseline_date"
                               name="baseline_date"
                               class="form-input"
                               value="<?= old('baseline_date', $indicator['baseline_date']) ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Date when baseline value was measured (optional)
                        </small>
                        <?php if (isset($errors['baseline_date'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['baseline_date']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Middle Column - Target Information -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        🎯 Target Information
                    </h3>

                    <!-- Target Value -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="target_value" class="form-label">
                            Target Value
                        </label>
                        <input type="text"
                               id="target_value"
                               name="target_value"
                               class="form-input"
                               value="<?= old('target_value', $indicator['target_value']) ?>"
                               placeholder="e.g., 100, 80%, High, Complete"
                               maxlength="15">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Expected value to be achieved by project completion - can be numbers or text (optional)
                        </small>
                        <?php if (isset($errors['target_value'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['target_value']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Target Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="target_date" class="form-label">
                            Target Date
                        </label>
                        <input type="date"
                               id="target_date"
                               name="target_date"
                               class="form-input"
                               value="<?= old('target_date', $indicator['target_date']) ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Expected date to achieve target value (optional)
                        </small>
                        <?php if (isset($errors['target_date'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['target_date']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column - Actual Information (Read-only) -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        ✅ Actual Information
                    </h3>

                    <!-- Actual Value (Read-only) -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label class="form-label">
                            Actual Value
                        </label>
                        <div style="background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-md); color: var(--text-secondary);">
                            <?= $indicator['actual_value'] !== null && $indicator['actual_value'] !== '' ?
                                (is_numeric($indicator['actual_value']) ? number_format($indicator['actual_value'], 2) : esc($indicator['actual_value'])) :
                                'Not recorded yet' ?>
                        </div>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Actual value recorded by M&E team (read-only)
                        </small>
                    </div>

                    <!-- Actual Date (Read-only) -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label class="form-label">
                            Actual Date
                        </label>
                        <div style="background: var(--bg-tertiary); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-md); color: var(--text-secondary);">
                            <?= $indicator['actual_date'] ? date('M j, Y', strtotime($indicator['actual_date'])) : 'Not recorded yet' ?>
                        </div>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Date when actual value was recorded (read-only)
                        </small>
                    </div>
                </div>
            </div>

            <!-- Important Note -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--brand-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    ℹ️ Important Note
                </h4>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                    <strong>Admin Portal:</strong> You can edit baseline and target values for monitoring framework adjustments.<br>
                    <strong>M&E Team:</strong> Actual values and dates are managed through the monitoring portal and cannot be edited here.
                </p>
            </div>

            <!-- Metadata -->
            <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📋 Indicator Metadata
                </h4>
                <div style="display: grid; grid-template-columns: auto 1fr auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.75rem;">
                    <span style="color: var(--text-muted); font-weight: 500;">Created:</span>
                    <span style="color: var(--text-secondary);"><?= date('M j, Y \a\t g:i A', strtotime($indicator['created_at'])) ?></span>
                    
                    <?php if ($indicator['updated_at'] && $indicator['updated_at'] !== $indicator['created_at']): ?>
                        <span style="color: var(--text-muted); font-weight: 500;">Last Updated:</span>
                        <span style="color: var(--text-secondary);"><?= date('M j, Y \a\t g:i A', strtotime($indicator['updated_at'])) ?></span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    💾 Update Impact Indicator
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* 
Color-coded Form Input System:
- RED OUTLINE: Required fields (must be filled)
- GREEN OUTLINE: Optional fields (can be left empty)
*/

/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

/* Form input styling - applies to all input types */
.form-input,
input.form-input,
textarea.form-input,
select.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
    border: 2px solid #10B981 !important; /* Default green for optional fields */
}

/* Required fields have red outline - applies to all input types */
.form-input[required],
input.form-input[required],
textarea.form-input[required],
select.form-input[required] {
    border: 2px solid #EF4444 !important; /* Red for required fields */
}

/* Focus states for optional fields (green) */
.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: #059669 !important; /* Darker green on focus */
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Focus states for required fields (red) */
.form-input[required]:focus,
input.form-input[required]:focus,
textarea.form-input[required]:focus,
select.form-input[required]:focus {
    outline: none;
    border-width: 2px;
    border-color: #DC2626 !important; /* Darker red on focus */
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Hover states for optional fields (green) */
.form-input:hover {
    border-color: #059669 !important; /* Slightly darker green on hover */
}

/* Hover states for required fields (red) */
.form-input[required]:hover,
input.form-input[required]:hover,
textarea.form-input[required]:hover,
select.form-input[required]:hover {
    border-color: #DC2626 !important; /* Slightly darker red on hover */
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 120px;
}

/* Enhanced date inputs */
input[type="date"].form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 2v3m8-3v3m-9 8h10l1-10H4l1 10z'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced text inputs */
input[type="text"].form-input {
    text-align: left;
}

/* Form group styling */
.form-group {
    margin-bottom: var(--spacing-lg);
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Button styling */
.btn {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    border-radius: var(--radius-sm);
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="grid-template-columns: 1fr 1fr 1fr"] {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    div[style*="display: flex"] {
        justify-content: stretch !important;
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
    
    div[style*="grid-template-columns: auto 1fr auto 1fr"] {
        grid-template-columns: auto 1fr !important;
    }
}
</style>

<?= $this->endSection() ?>
