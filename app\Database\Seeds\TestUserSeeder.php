<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TestUserSeeder extends Seeder
{
    public function run()
    {
        // Test Admin User (for admin portal)
        $adminData = [
            'organization_id' => 2,
            'user_code' => 'ADMIN001',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'name' => 'Admin User',
            'role' => 'admin',
            'password_hash' => password_hash('admin', PASSWORD_ARGON2ID),
            'is_activated' => 1,
            'created_by' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Test User (for monitoring portal)
        $userData = [
            'organization_id' => 2,
            'user_code' => 'USER001',
            'username' => 'user',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'role' => 'user',
            'password_hash' => password_hash('user', PASSWORD_ARGON2ID),
            'is_activated' => 1,
            'created_by' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $builder = $this->db->table('users');

        // Check and create admin user
        $existingAdmin = $builder->where('username', 'admin')->get()->getRow();
        if (!$existingAdmin) {
            $builder->insert($adminData);
            echo "✓ Admin user created: admin/admin (Admin Portal)\n";
        } else {
            echo "✓ Admin user already exists\n";
        }

        // Check and create test user
        $existingUser = $builder->where('username', 'user')->get()->getRow();
        if (!$existingUser) {
            $builder->insert($userData);
            echo "✓ Test user created: user/user (Monitoring Portal)\n";
        } else {
            echo "✓ Test user already exists\n";
        }

        echo "\n=== LOGIN TESTING CREDENTIALS ===\n";
        echo "Login URL: http://localhost/promis_two/auth/login\n\n";
        echo "Admin Portal: admin/admin\n";
        echo "Monitoring Portal: user/user\n";
        echo "Alternative Admin: testadmin/1234\n";
    }
}
