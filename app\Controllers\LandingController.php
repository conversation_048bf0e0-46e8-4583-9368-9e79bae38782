<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class Landing<PERSON>ontroller extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'PROMIS - Project Management Information Systems'
        ];
        
        return view('home/home_landing', $data);
    }
    
    public function contact()
    {
        // Handle contact form submission
        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'name' => 'required|min_length[2]|max_length[100]',
            'email' => 'required|valid_email',
            'company' => 'max_length[100]',
            'message' => 'required|min_length[10]|max_length[1000]'
        ]);
        
        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }
        
        $name = $this->request->getPost('name');
        $email = $this->request->getPost('email');
        $company = $this->request->getPost('company');
        $message = $this->request->getPost('message');
        
        // Here you can add logic to save to database or send email
        // For now, we'll just show a success message
        
        // You could save to a contacts table:
        /*
        $contactModel = new \App\Models\ContactModel();
        $contactModel->save([
            'name' => $name,
            'email' => $email,
            'company' => $company,
            'message' => $message,
            'created_at' => date('Y-m-d H:i:s')
        ]);
        */
        
        // Or send an email notification:
        /*
        $emailService = \Config\Services::email();
        $emailService->setTo('<EMAIL>');
        $emailService->setFrom($email, $name);
        $emailService->setSubject('New Contact Form Submission');
        $emailService->setMessage("
            Name: $name
            Email: $email
            Company: $company
            Message: $message
        ");
        $emailService->send();
        */
        
        return redirect()->to('/#contact')->with('success', 'Thank you for your message! We will get back to you soon.');
    }
}
