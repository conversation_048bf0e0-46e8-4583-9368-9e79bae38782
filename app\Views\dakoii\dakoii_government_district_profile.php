<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Districts
</a>
<?php if (isset($district)): ?>
<a href="<?= base_url('dakoii/government/districts/' . $district['id'] . '/edit') ?>" class="btn btn-primary">
    <i class="icon">✏️</i> Edit District
</a>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <?php if (isset($district)): ?>
        <!-- Page Header -->
        <div style="margin-bottom: var(--spacing-xl);">
            <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
                <span style="font-size: 2.5rem;">🏘️</span>
                <?= esc($district['name']) ?>
            </h1>
            <p style="color: var(--text-secondary); font-size: 1rem;">
                District profile and administrative information.
            </p>
        </div>

        <!-- District Information Card -->
        <div class="card">
            <div class="card-header">District Information</div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-xl);">
                <div>
                    <div style="margin-bottom: var(--spacing-lg);">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                            <span style="font-weight: 600; color: var(--text-secondary);">District Name:</span>
                            <span style="color: var(--text-primary); font-weight: 500;"><?= esc($district['name']) ?></span>
                        </div>
                        <?php if (isset($district['dist_code']) && !empty($district['dist_code'])): ?>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                            <span style="font-weight: 600; color: var(--text-secondary);">District Code:</span>
                            <span style="color: var(--text-primary); font-family: var(--font-mono);"><?= esc($district['dist_code']) ?></span>
                        </div>
                        <?php endif; ?>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                            <span style="font-weight: 600; color: var(--text-secondary);">Province:</span>
                            <span style="color: var(--text-primary);">
                                <a href="<?= base_url('dakoii/government/provinces/' . $district['province_id']) ?>" style="color: var(--accent-primary); text-decoration: none;">
                                    <?= esc($province['name']) ?>
                                </a>
                            </span>
                        </div>
                        <?php if (isset($district['created_at'])): ?>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                            <span style="font-weight: 600; color: var(--text-secondary);">Created:</span>
                            <span style="color: var(--text-primary);"><?= date('M j, Y', strtotime($district['created_at'])) ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if (isset($district['updated_at'])): ?>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0;">
                            <span style="font-weight: 600; color: var(--text-secondary);">Last Updated:</span>
                            <span style="color: var(--text-primary);"><?= date('M j, Y', strtotime($district['updated_at'])) ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="card">
            <div class="card-header">Actions</div>
            <div style="display: flex; gap: var(--spacing-md); flex-wrap: wrap;">
                <a href="<?= base_url('dakoii/government/districts/' . $district['id'] . '/edit') ?>" class="btn btn-primary">
                    ✏️ Edit District
                </a>
                <a href="<?= base_url('dakoii/government/llgs/by-district/' . $district['id']) ?>" class="btn btn-secondary">
                    🏛️ View LLGs
                </a>
                <form method="POST" action="<?= base_url('dakoii/government/districts/' . $district['id'] . '/delete') ?>" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn" style="background: #dc3545; color: white;"
                            onclick="return confirm('Are you sure you want to delete this district? This will also delete all associated LLGs.')"
                            title="Delete District">
                        🗑️ Delete District
                    </button>
                </form>
            </div>
        </div>

    <?php else: ?>
        <div class="card" style="text-align: center; padding: var(--spacing-2xl);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-md); opacity: 0.5;">🏘️</div>
            <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">District Not Found</h3>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">The requested district could not be found.</p>
            <a href="<?= base_url('dakoii/government/districts') ?>" class="btn btn-primary">
                <i class="icon">←</i> Back to Districts
            </a>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>