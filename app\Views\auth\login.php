<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'Login - PROMIS' ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/system_images/favicon.ico') ?>">
    <style>
        :root {
            /* Professional Light Theme Color Palette */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-tertiary: #F1F5F9;
            --bg-accent: #EFF6FF;
            
            /* Surface Colors */
            --surface-card: #FFFFFF;
            --surface-overlay: rgba(255, 255, 255, 0.95);
            
            /* Professional Gradients */
            --gradient-primary: linear-gradient(135deg, #3B82F6, #1D4ED8);
            --gradient-secondary: linear-gradient(135deg, #10B981, #059669);
            --gradient-hero: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);
            
            /* Text Colors */
            --text-primary: #1E293B;
            --text-secondary: #475569;
            --text-tertiary: #64748B;
            --text-muted: #94A3B8;
            --text-white: #FFFFFF;
            
            /* Brand Colors */
            --brand-primary: #3B82F6;
            --brand-secondary: #10B981;
            --brand-danger: #EF4444;
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            
            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background: var(--gradient-hero);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            background: var(--surface-card);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
        }

        .login-header {
            background: var(--gradient-primary);
            color: var(--text-white);
            padding: var(--spacing-2xl);
            text-align: center;
        }

        .login-logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
        }

        .login-subtitle {
            opacity: 0.9;
            font-size: 0.875rem;
        }

        .login-form {
            padding: var(--spacing-2xl);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            border: 1px solid #D1D5DB;
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: var(--bg-primary);
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .form-checkbox input {
            width: 16px;
            height: 16px;
            accent-color: var(--brand-primary);
        }

        .form-checkbox label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .btn {
            display: inline-block;
            width: 100%;
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-white);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .login-footer {
            padding: var(--spacing-lg) var(--spacing-2xl);
            background: var(--bg-secondary);
            text-align: center;
            border-top: 1px solid #E5E7EB;
        }

        .login-footer a {
            color: var(--brand-primary);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        /* Flash Messages */
        .flash-message {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border-left: 4px solid;
            font-size: 0.875rem;
        }

        .flash-success {
            background: #F0FDF4;
            border-color: var(--brand-secondary);
            color: #166534;
        }

        .flash-error {
            background: #FEF2F2;
            border-color: var(--brand-danger);
            color: #991B1B;
        }

        .flash-info {
            background: #EFF6FF;
            border-color: var(--brand-primary);
            color: #1E40AF;
        }

        .error-list {
            margin: 0;
            padding-left: var(--spacing-md);
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                margin: var(--spacing-md);
            }
            
            .login-header,
            .login-form {
                padding: var(--spacing-lg);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">PROMIS</div>
            <div class="login-subtitle">Project Management Information Systems</div>
        </div>

        <div class="login-form">
            <!-- Flash Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="flash-message flash-success">
                    <?= session()->getFlashdata('success') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="flash-message flash-error">
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('info')): ?>
                <div class="flash-message flash-info">
                    <?= session()->getFlashdata('info') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="flash-message flash-error">
                    <ul class="error-list">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form action="<?= base_url('auth/login') ?>" method="post">
                <?= csrf_field() ?>
                
                <div class="form-group">
                    <label for="identifier" class="form-label">Username or Email</label>
                    <input type="text" 
                           id="identifier" 
                           name="identifier" 
                           class="form-input" 
                           value="<?= old('identifier') ?>"
                           placeholder="Enter your username or email"
                           required 
                           autofocus>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           class="form-input" 
                           placeholder="Enter your password"
                           required>
                </div>

                <div class="form-checkbox">
                    <input type="checkbox" id="remember_me" name="remember_me" value="1">
                    <label for="remember_me">Remember me for 30 days</label>
                </div>

                <button type="submit" class="btn btn-primary">
                    Sign In
                </button>
            </form>
        </div>

        <div class="login-footer">
            <a href="<?= base_url('/') ?>">← Back to Homepage</a>
        </div>
    </div>

    <script>
        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html>
