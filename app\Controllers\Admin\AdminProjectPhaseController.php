<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectPhaseModel;

/**
 * Admin Project Phase Controller
 * 
 * Handles CRUD operations for project phases in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectPhaseController extends BaseController
{
    protected $projectModel;
    protected $projectPhaseModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectPhaseModel = new ProjectPhaseModel();
    }

    /**
     * Show create phase form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Create New Phase - PROMIS Admin',
            'page_title' => 'Create New Phase',
            'project' => $project
        ];

        return view('admin/admin_projects_phases_create', $data);
    }

    /**
     * Store new phase - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'phase_code' => 'required|max_length[20]',
            'title' => 'required|max_length[150]',
            'description' => 'permit_empty',
            'start_date' => 'permit_empty|valid_date',
            'end_date' => 'permit_empty|valid_date',
            'status' => 'required|in_list[active,deactivated]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for insertion
        $phaseData = [
            'project_id' => $projectId,
            'phase_code' => $this->request->getPost('phase_code'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description') ?: '',
            'start_date' => $this->request->getPost('start_date') ?: null,
            'end_date' => $this->request->getPost('end_date') ?: null,
            'status' => $this->request->getPost('status'),
            'sort_order' => $this->projectPhaseModel->getNextSortOrder($projectId),
            'created_by' => $adminUserId
        ];

        try {
            $phaseId = $this->projectPhaseModel->insert($phaseData);

            if ($phaseId) {
                return redirect()->to(base_url('admin/projects/' . $projectId))
                               ->with('success', 'Phase created successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectPhaseModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating phase: ' . $e->getMessage());
        }
    }

    /**
     * Show edit phase form - GET request
     */
    public function edit($projectId, $phaseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get phase
        $phase = $this->projectPhaseModel->where('id', $phaseId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$phase) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Phase not found.');
        }

        $data = [
            'title' => 'Edit Phase - PROMIS Admin',
            'page_title' => 'Edit Phase',
            'project' => $project,
            'phase' => $phase
        ];

        return view('admin/admin_projects_phases_edit', $data);
    }

    /**
     * Update phase - POST request
     */
    public function update($projectId, $phaseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get phase
        $phase = $this->projectPhaseModel->where('id', $phaseId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$phase) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Phase not found.');
        }

        // Validation rules
        $rules = [
            'phase_code' => 'required|max_length[20]',
            'title' => 'required|max_length[150]',
            'description' => 'permit_empty',
            'start_date' => 'permit_empty|valid_date',
            'end_date' => 'permit_empty|valid_date',
            'status' => 'required|in_list[active,deactivated]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for update
        $updateData = [
            'phase_code' => $this->request->getPost('phase_code'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description') ?: '',
            'start_date' => $this->request->getPost('start_date') ?: null,
            'end_date' => $this->request->getPost('end_date') ?: null,
            'status' => $this->request->getPost('status'),
            'updated_by' => $adminUserId
        ];

        try {
            $updated = $this->projectPhaseModel->update($phaseId, $updateData);

            if ($updated) {
                return redirect()->to(base_url('admin/projects/' . $projectId))
                               ->with('success', 'Phase updated successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectPhaseModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating phase: ' . $e->getMessage());
        }
    }

    /**
     * Delete phase - POST request
     */
    public function delete($projectId, $phaseId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get phase
        $phase = $this->projectPhaseModel->where('id', $phaseId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$phase) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Phase not found.');
        }

        // Check if phase can be deleted (no milestones)
        if (!$this->projectPhaseModel->canDelete($phaseId)) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Cannot delete phase. Please remove all milestones first.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectPhaseModel->update($phaseId, ['deleted_by' => $adminUserId]);
            $this->projectPhaseModel->delete($phaseId);

            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('success', 'Phase deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Error deleting phase: ' . $e->getMessage());
        }
    }
}
