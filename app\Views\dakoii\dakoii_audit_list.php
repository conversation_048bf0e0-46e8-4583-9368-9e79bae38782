<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>

<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>Audit Trail</span>
            <div style="display: flex; gap: var(--spacing-md);">
                <button type="button" class="btn btn-secondary" onclick="toggleFilters()">
                    🔍 Filters
                </button>
                <a href="<?= base_url('dakoii/audit/export?' . http_build_query($current_filters)) ?>" class="btn btn-secondary">
                    📥 Export CSV
                </a>
                <a href="<?= base_url('dakoii/audit/stats') ?>" class="btn btn-secondary">
                    📊 Statistics
                </a>
            </div>
        </div>
    </div>

    <!-- Filter Panel -->
    <div id="filterPanel" style="display: none; padding: var(--spacing-lg); border-bottom: 1px solid var(--glass-border); background: rgba(0, 0, 0, 0.2);">
        <form method="GET" action="<?= base_url('dakoii/audit') ?>">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                
                <div class="form-group">
                    <label class="form-label">Portal</label>
                    <select name="portal" class="form-input">
                        <option value="">All Portals</option>
                        <?php foreach ($filter_options['portals'] as $value => $label): ?>
                            <option value="<?= esc($value) ?>" <?= ($current_filters['portal'] === $value) ? 'selected' : '' ?>>
                                <?= esc($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Action</label>
                    <select name="action" class="form-input">
                        <option value="">All Actions</option>
                        <?php foreach ($filter_options['actions'] as $value => $label): ?>
                            <option value="<?= esc($value) ?>" <?= ($current_filters['action'] === $value) ? 'selected' : '' ?>>
                                <?= esc($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Table</label>
                    <select name="table_name" class="form-input">
                        <option value="">All Tables</option>
                        <?php foreach ($filter_options['tables'] as $table): ?>
                            <option value="<?= esc($table) ?>" <?= ($current_filters['table_name'] === $table) ? 'selected' : '' ?>>
                                <?= esc($table) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">User</label>
                    <select name="user_id" class="form-input">
                        <option value="">All Users</option>
                        <?php foreach ($filter_options['users'] as $user): ?>
                            <option value="<?= esc($user['user_id']) ?>" <?= ($current_filters['user_id'] == $user['user_id']) ? 'selected' : '' ?>>
                                <?= esc($user['username']) ?> (<?= esc($user['user_full_name']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Organization</label>
                    <select name="organization_id" class="form-input">
                        <option value="">All Organizations</option>
                        <?php foreach ($filter_options['organizations'] as $org): ?>
                            <option value="<?= esc($org['organization_id']) ?>" <?= ($current_filters['organization_id'] == $org['organization_id']) ? 'selected' : '' ?>>
                                <?= esc($org['organization_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Date From</label>
                    <input type="date" name="date_from" class="form-input" value="<?= esc($current_filters['date_from']) ?>">
                </div>

                <div class="form-group">
                    <label class="form-label">Date To</label>
                    <input type="date" name="date_to" class="form-input" value="<?= esc($current_filters['date_to']) ?>">
                </div>

                <div class="form-group">
                    <label class="form-label">Limit</label>
                    <select name="limit" class="form-input">
                        <option value="25" <?= ($current_filters['limit'] == 25) ? 'selected' : '' ?>>25</option>
                        <option value="50" <?= ($current_filters['limit'] == 50) ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= ($current_filters['limit'] == 100) ? 'selected' : '' ?>>100</option>
                        <option value="200" <?= ($current_filters['limit'] == 200) ? 'selected' : '' ?>>200</option>
                    </select>
                </div>

            </div>

            <div style="display: flex; gap: var(--spacing-md);">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-secondary">Clear Filters</a>
            </div>
        </form>
    </div>

    <!-- Audit Logs Table -->
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: rgba(0, 0, 0, 0.3); border-bottom: 1px solid var(--glass-border);">
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Date/Time</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Portal</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Action</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Table</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">User</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Organization</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Description</th>
                    <th style="padding: var(--spacing-md); text-align: left; color: var(--text-secondary); font-weight: 600;">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($audit_logs)): ?>
                    <tr>
                        <td colspan="8" style="padding: var(--spacing-xl); text-align: center; color: var(--text-tertiary);">
                            No audit logs found matching the current filters.
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($audit_logs as $log): ?>
                        <tr style="border-bottom: 1px solid var(--glass-border); transition: background-color 0.3s ease;" 
                            onmouseover="this.style.backgroundColor='rgba(255, 255, 255, 0.05)'" 
                            onmouseout="this.style.backgroundColor='transparent'">
                            
                            <td style="padding: var(--spacing-md); color: var(--text-primary); font-family: var(--font-mono); font-size: 0.875rem;">
                                <?= date('M j, Y H:i:s', strtotime($log['created_at'])) ?>
                            </td>
                            
                            <td style="padding: var(--spacing-md);">
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase; 
                                    <?php if ($log['portal'] === 'dakoii'): ?>
                                        background: rgba(255, 0, 110, 0.2); color: #FF006E;
                                    <?php elseif ($log['portal'] === 'admin'): ?>
                                        background: rgba(131, 56, 236, 0.2); color: #8338EC;
                                    <?php else: ?>
                                        background: rgba(0, 212, 255, 0.2); color: #00D4FF;
                                    <?php endif; ?>">
                                    <?= esc($log['portal']) ?>
                                </span>
                            </td>
                            
                            <td style="padding: var(--spacing-md);">
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;
                                    <?php if ($log['action'] === 'create'): ?>
                                        background: rgba(40, 167, 69, 0.2); color: #28a745;
                                    <?php elseif ($log['action'] === 'update'): ?>
                                        background: rgba(255, 183, 0, 0.2); color: #FFB700;
                                    <?php elseif ($log['action'] === 'delete'): ?>
                                        background: rgba(220, 53, 69, 0.2); color: #dc3545;
                                    <?php else: ?>
                                        background: rgba(108, 117, 125, 0.2); color: #6c757d;
                                    <?php endif; ?>">
                                    <?= esc($log['action']) ?>
                                </span>
                            </td>
                            
                            <td style="padding: var(--spacing-md); color: var(--text-secondary); font-family: var(--font-mono); font-size: 0.875rem;">
                                <?= esc($log['table_name']) ?>
                            </td>
                            
                            <td style="padding: var(--spacing-md); color: var(--text-primary);">
                                <?php if ($log['username']): ?>
                                    <div style="font-weight: 600;"><?= esc($log['username']) ?></div>
                                    <?php if ($log['user_full_name']): ?>
                                        <div style="font-size: 0.75rem; color: var(--text-tertiary);"><?= esc($log['user_full_name']) ?></div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span style="color: var(--text-tertiary); font-style: italic;">System</span>
                                <?php endif; ?>
                            </td>
                            
                            <td style="padding: var(--spacing-md); color: var(--text-secondary);">
                                <?= $log['organization_name'] ? esc($log['organization_name']) : '-' ?>
                            </td>
                            
                            <td style="padding: var(--spacing-md); color: var(--text-primary); max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                <?= esc($log['description']) ?>
                            </td>
                            
                            <td style="padding: var(--spacing-md);">
                                <a href="<?= base_url('dakoii/audit/' . $log['id']) ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    View Details
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if (isset($pager) && $pager->getPageCount() > 1): ?>
        <div style="padding: var(--spacing-lg); border-top: 1px solid var(--glass-border); text-align: center;">
            <div style="display: inline-flex; gap: var(--spacing-sm); align-items: center;">
                <?php if ($pager->hasPrevious()): ?>
                    <a href="<?= $pager->getPreviousPageURI() ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm);">
                        ← Previous
                    </a>
                <?php endif; ?>

                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                    Page <?= $pager->getCurrentPage() ?> of <?= $pager->getPageCount() ?>
                </span>

                <?php if ($pager->hasNext()): ?>
                    <a href="<?= $pager->getNextPageURI() ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm);">
                        Next →
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function toggleFilters() {
    const panel = document.getElementById('filterPanel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// Show filters if any are active
<?php if (array_filter($current_filters)): ?>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('filterPanel').style.display = 'block';
});
<?php endif; ?>
</script>

<?= $this->endSection() ?>
