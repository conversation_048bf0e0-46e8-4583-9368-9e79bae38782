# Admin Portal Implementation Task List for Phases 1-3

## Pre-Implementation Setup Tasks

### Task 1: Verify Database and Models
- Check existing tables: `users`, `organizations`, `projects`, `contractors`, `user_audit_log`
- Verify existing models: `UserModel`, `OrganizationModel`, `ProjectModel`, `ContractorModel`
- Confirm audit fields exist: `created_by`, `updated_by`, `deleted_by`, `created_at`, `updated_at`, `deleted_at`
- Ensure `users` table has fields: `is_supervisor`, `is_project_officer`, `is_mon_eval`, `role` (admin/moderator/editor/viewer)

### Task 2: Setup Directory Structure
- Create folder structure: `app/Views/admin/`
- Create folder structure: `app/Controllers/Admin/`
- Create folder structure: `public/uploads/admin/users/`
- Create folder structure: `public/uploads/admin/documents/`
- Set proper permissions for upload directories (755 for directories, 644 for files)

### Task 3: Create Base Template
- Create `app/Views/templates/admin_portal_template.php` extending Bootstrap 5 theme
- Include sidebar navigation placeholder
- Include header with user menu placeholder
- Include notification bell placeholder
- Include global search bar placeholder
- Include footer with feedback link placeholder
- Add session flash message display area
- Include CSRF meta tag

---

## PHASE 1: Foundation & Authentication (Weeks 1-2)

### Function 1: User Login

#### Task 1.1: Create Login Controller
- Create `app/Controllers/Admin/AdminAuthController.php`
- Add method `showLoginForm()` - GET request to display login page
- Add method `authenticateUser()` - POST request to process login
- Use existing `UserModel` for database queries
- Check `is_project_officer` flag for portal redirection
- Implement session management using CodeIgniter 4 session library
- Add audit logging using `user_audit_log` table

#### Task 1.2: Create Login View
- Create `app/Views/admin/admin_login.php`
- Use Bootstrap 5 form components
- Add CSRF token field using `<?= csrf_field() ?>`
- Include form validation error display using `<?= validation_list_errors() ?>`
- Add "Remember Me" checkbox
- Add link to organization login route
- Make form action POST to `/admin/login`

#### Task 1.3: Configure Routes
- Add to `app/Config/Routes.php`:
  - GET route: `$routes->get('admin/login', 'Admin\AdminAuthController::showLoginForm')`
  - POST route: `$routes->post('admin/login', 'Admin\AdminAuthController::authenticateUser')`
- Set up route group for admin with authentication filter

#### Task 1.4: Implement Authentication Logic
- In `authenticateUser()` method:
  - Validate input using CodeIgniter 4 validation library
  - Query `users` table using `UserModel` with email/username
  - Verify password using `password_verify()` with Argon2id hash
  - Set session variables: `admin_user_id`, `admin_user_role`, `admin_user_name`, `admin_organization_id`
  - Log authentication attempt in `user_audit_log` table
  - Redirect based on `is_project_officer` flag (0 = Admin Portal, 1 = Monitoring Portal)

### Function 2: Organization Login Route

#### Task 2.1: Extend Authentication Controller
- Add method `showOrganizationLoginForm()` - GET request
- Add method `authenticateOrganizationUser()` - POST request
- Reuse authentication logic from main login
- Add organization branding lookup using `OrganizationModel`

#### Task 2.2: Create Organization Login View
- Create `app/Views/admin/admin_organization_login.php`
- Include organization logo display area
- Use same form structure as main login
- Make form action POST to `/organization/login`

#### Task 2.3: Add Routes
- Add GET route: `$routes->get('organization/login', 'Admin\AdminAuthController::showOrganizationLoginForm')`
- Add POST route: `$routes->post('organization/login', 'Admin\AdminAuthController::authenticateOrganizationUser')`

### Function 3: Session Management

#### Task 3.1: Create Session Filter
- Create `app/Filters/AdminAuthFilter.php`
- Implement `before()` method to check session validity
- Check for `admin_user_id` in session
- Verify session timeout (30 minutes idle time)
- Update last activity timestamp
- Redirect to login if session invalid

#### Task 3.2: Configure Session Settings
- Update `app/Config/App.php`:
  - Set session expiration to 1800 seconds (30 minutes)
  - Configure session cookie settings
  - Enable session regeneration

#### Task 3.3: Register Filter
- Update `app/Config/Filters.php`
- Add `admin_auth` alias for `AdminAuthFilter`
- Apply filter to admin route group

### Function 4: Logout Function

#### Task 4.1: Add Logout Method
- In `AdminAuthController`, add method `logoutUser()` - POST request
- Destroy session using `session()->destroy()`
- Clear remember me cookie if exists
- Log logout event in `user_audit_log` table
- Redirect to `/admin/login` with success message

#### Task 4.2: Add Logout Route
- Add POST route: `$routes->post('admin/logout', 'Admin\AdminAuthController::logoutUser')`

### Function 5: Remember Me

#### Task 5.1: Implement Remember Me Logic
- In `authenticateUser()` method:
  - Check if remember me checkbox is checked
  - Generate secure token using `bin2hex(random_bytes(32))`
  - Store token hash in `users` table (add `remember_token` field note to admin)
  - Set secure cookie with 30-day expiration

#### Task 5.2: Auto-login Implementation
- In `AdminAuthFilter`:
  - Check for remember me cookie
  - Validate token against database
  - Auto-login if valid
  - Regenerate token for security

---

## PHASE 2: User Management System (Weeks 3-4)

### Function 6: Manage User Accounts

#### Task 6.1: Create User Management Controller
- Create `app/Controllers/Admin/AdminUserController.php`
- Add method `createUserStep1()` - GET request for account details form
- Add method `processUserStep1()` - POST request to process step 1
- Add method `createUserStep2()` - GET request for roles/permissions form
- Add method `processUserStep2()` - POST request to complete user creation
- Use existing `UserModel` for all database operations

#### Task 6.2: Create User Creation Views
- Create `app/Views/admin/admin_users_create_step1.php`
  - Extend `admin_portal_template.php`
  - Include form fields: username, email, name, phone, department
  - Add password generation option
  - Include CSRF protection
  
- Create `app/Views/admin/admin_users_create_step2.php`
  - Extend `admin_portal_template.php`
  - Role selection dropdown (admin/moderator/editor/viewer)
  - Checkboxes for: is_supervisor, is_project_officer, is_mon_eval
  - Organization assignment if applicable

#### Task 6.3: Configure Routes
- Add routes in admin group:
  - GET: `$routes->get('users/create', 'Admin\AdminUserController::createUserStep1')`
  - POST: `$routes->post('users/create', 'Admin\AdminUserController::processUserStep1')`
  - GET: `$routes->get('users/create/step2/(:num)', 'Admin\AdminUserController::createUserStep2/$1')`
  - POST: `$routes->post('users/create/step2/(:num)', 'Admin\AdminUserController::processUserStep2/$1')`

### Function 7: View User List

#### Task 7.1: Add List Method
- In `AdminUserController`, add method `listUsers()` - GET request
- Query `users` table with pagination using `UserModel`
- Apply filters: role, status, organization
- Join with `organizations` table for organization name
- Calculate last login from session data

#### Task 7.2: Create List View
- Create `app/Views/admin/admin_users_list.php`
- Extend `admin_portal_template.php`
- Create Bootstrap 5 table with DataTables integration
- Add role badges with colors (admin=primary, moderator=info, editor=warning, viewer=secondary)
- Include action buttons: edit, reset password, toggle status
- Add filters dropdown for role and status

#### Task 7.3: Add Route
- GET: `$routes->get('users', 'Admin\AdminUserController::listUsers')`

### Function 8: Password Reset Management

#### Task 8.1: Add Password Reset Methods
- In `AdminUserController`:
  - Add method `showResetPasswordModal()` - GET request for modal content
  - Add method `processPasswordReset()` - POST request to reset password
- Generate temporary password using `bin2hex(random_bytes(4))`
- Hash password using `password_hash()` with PASSWORD_ARGON2ID
- Update `users` table using `UserModel`
- Send email with temporary password

#### Task 8.2: Create Reset Modal View
- Create `app/Views/admin/admin_users_reset_password_modal.php`
- Bootstrap 5 modal structure
- Confirmation message
- Hidden user ID field
- CSRF protection

#### Task 8.3: Add Routes
- GET: `$routes->get('users/(:num)/reset-password-modal', 'Admin\AdminUserController::showResetPasswordModal/$1')`
- POST: `$routes->post('users/(:num)/reset-password', 'Admin\AdminUserController::processPasswordReset/$1')`

### Function 9: Manage User Sessions

#### Task 9.1: Create Session Management Methods
- In `AdminUserController`:
  - Add method `viewActiveSessions()` - GET request
  - Add method `terminateSession()` - POST request
- Query active sessions from session storage
- Display IP addresses, login times, user agents

#### Task 9.2: Create Sessions View
- Create `app/Views/admin/admin_users_sessions.php`
- Extend `admin_portal_template.php`
- Table showing active sessions
- Terminate button for each session
- Session duration calculation

#### Task 9.3: Add Routes
- GET: `$routes->get('users/sessions', 'Admin\AdminUserController::viewActiveSessions')`
- POST: `$routes->post('users/sessions/(:any)/terminate', 'Admin\AdminUserController::terminateSession/$1')`

### Function 10: User Audit Trail

#### Task 10.1: Create Audit Controller
- Create `app/Controllers/Admin/AdminAuditController.php`
- Add method `viewUserAudit()` - GET request
- Query `user_audit_log` table with filters
- Implement pagination
- Add export to CSV functionality

#### Task 10.2: Create Audit View
- Create `app/Views/admin/admin_audit_trail.php`
- Extend `admin_portal_template.php`
- Filterable table with date range, user, action type
- Export button
- Color-coded action types

#### Task 10.3: Add Route
- GET: `$routes->get('audit', 'Admin\AdminAuditController::viewUserAudit')`

---

## PHASE 3: Core Infrastructure (Weeks 5-6)

### Function 11: Dashboard Customization

#### Task 11.1: Create Dashboard Controller
- Create `app/Controllers/Admin/AdminDashboardController.php`
- Add method `index()` - GET request for main dashboard
- Add method `saveWidgetLayout()` - POST request to save layout
- Store widget preferences in user session or database

#### Task 11.2: Create Dashboard View
- Create `app/Views/admin/admin_dashboard.php`
- Extend `admin_portal_template.php`
- Implement grid system for widgets
- Add drag-and-drop containers using Bootstrap 5
- Include default widgets: statistics, recent activities, quick actions

#### Task 11.3: Add Routes
- GET: `$routes->get('dashboard', 'Admin\AdminDashboardController::index')`
- POST: `$routes->post('dashboard/save-layout', 'Admin\AdminDashboardController::saveWidgetLayout')`

### Function 12: Theme Switcher

#### Task 12.1: Add Theme Methods
- In `AdminDashboardController`:
  - Add method `switchTheme()` - POST request
- Store theme preference in `users` table
- Update session with theme choice

#### Task 12.2: Update Base Template
- Modify `admin_portal_template.php`:
  - Add theme class to body tag based on user preference
  - Include theme switcher in user menu
  - Load appropriate CSS based on theme

#### Task 12.3: Add Route
- POST: `$routes->post('theme/switch', 'Admin\AdminDashboardController::switchTheme')`

### Function 13: Global Search

#### Task 13.1: Create Search Controller
- Create `app/Controllers/Admin/AdminSearchController.php`
- Add method `search()` - GET request
- Search across projects, contractors, users tables
- Use LIKE queries for text search
- Return paginated results

#### Task 13.2: Create Search Results View
- Create `app/Views/admin/admin_search_results.php`
- Extend `admin_portal_template.php`
- Group results by module (projects, contractors, users)
- Add links to view full records
- Highlight search terms

#### Task 13.3: Add Route
- GET: `$routes->get('search', 'Admin\AdminSearchController::search')`

### Function 14: Advanced Filter Builder

#### Task 14.1: Create Filter Component
- In relevant controllers (UserController, ProjectController, etc.):
  - Add method `applyAdvancedFilters()` - helper method
- Build dynamic WHERE clauses based on filter criteria
- Support AND/OR logic

#### Task 14.2: Create Filter UI Component
- Create `app/Views/admin/components/admin_advanced_filter.php`
- Reusable filter builder interface
- Dropdown for field selection
- Operator selection (equals, contains, greater than, etc.)
- Value input fields

### Function 15: Saved Searches

#### Task 15.1: Add Saved Search Methods
- In `AdminSearchController`:
  - Add method `saveSearch()` - POST request
  - Add method `loadSavedSearch()` - GET request
  - Add method `deleteSavedSearch()` - DELETE request
- Store in database (note: may need saved_searches table)

#### Task 15.2: Create Saved Searches View
- Create `app/Views/admin/admin_saved_searches.php`
- List of saved searches
- Quick load buttons
- Delete options
- Share functionality

#### Task 15.3: Add Routes
- POST: `$routes->post('search/save', 'Admin\AdminSearchController::saveSearch')`
- GET: `$routes->get('search/saved/(:num)', 'Admin\AdminSearchController::loadSavedSearch/$1')`
- DELETE: `$routes->delete('search/saved/(:num)', 'Admin\AdminSearchController::deleteSavedSearch/$1')`

---

## General Implementation Notes:

1. **Every POST method must**:
   - Validate CSRF token
   - Log action in `user_audit_log`
   - Return to same page with success/error message
   - Use `return redirect()->back()->with('message', 'Success')`

2. **Every view file must**:
   - Extend `admin_portal_template.php`
   - Include `<?= $this->extend('templates/admin_portal_template') ?>`
   - Use `<?= $this->section('content') ?>` for content area
   - Follow naming convention: `admin_[module]_[action].php`

3. **Every controller must**:
   - Check user permissions using role from session
   - Use existing models only
   - Follow RESTful naming: GET = show/list, POST = process/create/update
   - Separate GET and POST methods

4. **File uploads must**:
   - Save to `public/uploads/admin/[module]/`
   - Store path in database with `public/` prefix
   - Validate file types and sizes
   - Generate unique filenames

5. **All forms must**:
   - Use standard CodeIgniter 4 form helpers
   - Include CSRF protection
   - Display validation errors
   - No AJAX submissions