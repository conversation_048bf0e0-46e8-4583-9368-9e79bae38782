<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Outcomes</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create Project Outcome
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Define a measurable deliverable for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Outcome Form -->
<div class="card">
    <div class="card-header">
        🎯 Outcome Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/create') ?>" class="outcome-create-form">
            <?= csrf_field() ?>

            <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Outcome Description -->
                    <div class="mb-4">
                        <label for="outcome_text" class="form-label fw-semibold">
                            Outcome Description <span class="text-danger">*</span>
                        </label>
                        <textarea id="outcome_text"
                                  name="outcome_text"
                                  class="form-control border-danger"
                                  rows="5"
                                  placeholder="e.g., Bridge construction, Double classroom building, Water well drilling..."
                                  required><?= old('outcome_text') ?></textarea>
                        <div class="form-text">
                            Detailed description of the measurable deliverable (max 255 characters)
                        </div>
                        <?php if (isset($errors['outcome_text'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['outcome_text']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Examples Section -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-lightbulb me-2"></i>
                            Examples of Project Outcomes
                        </h6>
                        <div class="small">
                            <div><strong>Infrastructure:</strong> "1 x bridge", "2 x double classroom", "1 x water well"</div>
                            <div><strong>Training:</strong> "50 x teachers trained", "100 x students graduated"</div>
                            <div><strong>Equipment:</strong> "10 x computers installed", "5 x vehicles delivered"</div>
                            <div><strong>Services:</strong> "1000 x people served", "500 x consultations provided"</div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Quantity -->
                    <div class="mb-4">
                        <label for="quantity" class="form-label fw-semibold">
                            Quantity <span class="text-danger">*</span>
                        </label>
                        <input type="number"
                               id="quantity"
                               name="quantity"
                               class="form-control border-danger"
                               value="<?= old('quantity', '1') ?>"
                               placeholder="1"
                               step="0.01"
                               min="0.01"
                               required>
                        <div class="form-text">
                            Measurable quantity of the deliverable
                        </div>
                        <?php if (isset($errors['quantity'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['quantity']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Unit -->
                    <div class="mb-4">
                        <label for="unit" class="form-label fw-semibold">
                            Unit of Measurement
                        </label>
                        <input type="text"
                               id="unit"
                               name="unit"
                               class="form-control border-success"
                               value="<?= old('unit') ?>"
                               placeholder="e.g., pieces, meters, people, classrooms, bridges..."
                               maxlength="50">
                        <div class="form-text">
                            Unit of measurement for the quantity (optional, max 50 characters)
                        </div>
                        <?php if (isset($errors['unit'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['unit']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Common Units -->
                    <div class="alert alert-secondary">
                        <h6 class="alert-heading">
                            <i class="bi bi-rulers me-2"></i>
                            Common Units
                        </h6>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" onclick="setUnit('pieces')" class="btn btn-outline-secondary btn-sm">pieces</button>
                            <button type="button" onclick="setUnit('meters')" class="unit-btn btn-mobile">meters</button>
                            <button type="button" onclick="setUnit('people')" class="unit-btn btn-mobile">people</button>
                            <button type="button" onclick="setUnit('classrooms')" class="unit-btn btn-mobile">classrooms</button>
                            <button type="button" onclick="setUnit('bridges')" class="unit-btn btn-mobile">bridges</button>
                            <button type="button" onclick="setUnit('wells')" class="unit-btn btn-mobile">wells</button>
                            <button type="button" onclick="setUnit('kilometers')" class="unit-btn btn-mobile">kilometers</button>
                            <button type="button" onclick="setUnit('buildings')" class="unit-btn btn-mobile">buildings</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">🎯</span>
                    <span class="btn-text">Create Outcome</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
    border: 2px solid var(--brand-success); /* Default green for optional fields */
}

/* Required fields have red outline */
.form-input[required] {
    border: 2px solid var(--brand-danger);
}

.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Unit buttons */
.unit-btn {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;
    min-width: 32px;
}

.unit-btn:hover {
    background: var(--brand-primary);
    color: white;
    border-color: var(--brand-primary);
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 120px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .unit-btn {
        min-height: 40px;
        padding: var(--spacing-sm);
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    .form-actions {
        gap: var(--spacing-sm);
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Set unit function
function setUnit(unit) {
    document.getElementById('unit').value = unit;
}

// Form validation
document.querySelector('.outcome-create-form').addEventListener('submit', function(e) {
    const outcomeText = document.getElementById('outcome_text').value.trim();
    const quantity = document.getElementById('quantity').value;

    if (!outcomeText || !quantity || parseFloat(quantity) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
