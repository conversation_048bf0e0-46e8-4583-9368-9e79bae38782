<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations/' . $organization['id']) ?>" class="btn btn-secondary">
    ← Back to Organization
</a>
<a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins/create') ?>" class="btn btn-primary">
    Create Administrator
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Organization Header -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-lg);">
            <?php if ($organization['logo_path']): ?>
                <img src="<?= base_url($organization['logo_path']) ?>" alt="Logo" style="width: 64px; height: 64px; border-radius: var(--radius-lg); object-fit: cover;">
            <?php else: ?>
                <div style="width: 64px; height: 64px; border-radius: var(--radius-lg); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 1.5rem;">
                    <?= strtoupper(substr($organization['name'], 0, 1)) ?>
                </div>
            <?php endif; ?>
            <div>
                <h2 style="margin: 0; color: var(--text-primary);"><?= esc($organization['name']) ?></h2>
                <div style="color: var(--text-secondary); margin-top: var(--spacing-xs);">
                    Organization Code: <span style="font-family: var(--font-mono); font-weight: 600;"><?= esc($organization['org_code']) ?></span>
                </div>
                <div style="color: var(--text-tertiary); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Administrators Management
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Statistics -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Total Admins</div>
            <div style="font-size: 2rem; font-weight: 700; color: var(--text-primary);"><?= count($admins) ?></div>
        </div>
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Active</div>
            <div style="font-size: 2rem; font-weight: 700; color: #06FFA5;">
                <?= count(array_filter($admins, function($admin) { return $admin['is_activated']; })) ?>
            </div>
        </div>
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Pending</div>
            <div style="font-size: 2rem; font-weight: 700; color: #FFB700;">
                <?= count(array_filter($admins, function($admin) { return !$admin['is_activated']; })) ?>
            </div>
        </div>
        <div class="card" style="padding: var(--spacing-lg);">
            <div style="font-size: 0.75rem; color: var(--text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--spacing-xs);">Roles</div>
            <div style="font-size: 2rem; font-weight: 700; color: #8338EC;">
                <?= count(array_unique(array_column($admins, 'role'))) ?>
            </div>
        </div>
    </div>

    <!-- Administrators List -->
    <div class="card">
        <div class="card-header">Administrators (<?= count($admins) ?> total)</div>
        
        <?php if (empty($admins)): ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-tertiary);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">👥</div>
                <div style="margin-bottom: var(--spacing-lg);">No administrators found for this organization</div>
                <a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins/create') ?>" class="btn btn-primary">
                    Create First Administrator
                </a>
            </div>
        <?php else: ?>
            <!-- Admin Cards Grid -->
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: var(--spacing-lg); padding: var(--spacing-lg);">
                <?php foreach ($admins as $admin): ?>
                    <div class="admin-card">
                        <div style="display: flex; align-items: center; margin-bottom: var(--spacing-md);">
                            <?php if ($admin['profile_photo_path']): ?>
                                <img src="<?= base_url($admin['profile_photo_path']) ?>" alt="Photo" style="width: 48px; height: 48px; border-radius: 50%; object-fit: cover; margin-right: var(--spacing-md);">
                            <?php else: ?>
                                <div style="width: 48px; height: 48px; border-radius: 50%; background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-right: var(--spacing-md);">
                                    <?= strtoupper(substr($admin['name'], 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc($admin['name']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-tertiary);">
                                    <?= esc($admin['username']) ?>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <span class="status-badge status-<?= $admin['is_activated'] ? 'active' : 'pending' ?>">
                                    <?= $admin['is_activated'] ? 'Active' : 'Pending' ?>
                                </span>
                            </div>
                        </div>

                        <div style="margin-bottom: var(--spacing-md);">
                            <div style="font-size: 0.75rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Email</div>
                            <div style="color: var(--text-secondary); font-size: 0.875rem;"><?= esc($admin['email']) ?></div>
                        </div>

                        <div style="margin-bottom: var(--spacing-md);">
                            <div style="font-size: 0.75rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Role</div>
                            <span class="role-badge role-<?= $admin['role'] ?>">
                                <?= ucfirst($admin['role']) ?>
                            </span>
                        </div>

                        <div style="margin-bottom: var(--spacing-md);">
                            <div style="font-size: 0.75rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Admin Code</div>
                            <div style="font-family: var(--font-mono); font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">
                                <?= esc($admin['user_code']) ?>
                            </div>
                        </div>

                        <div style="margin-bottom: var(--spacing-lg);">
                            <div style="font-size: 0.75rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Last Login</div>
                            <div style="color: var(--text-secondary); font-size: 0.875rem;">
                                <?= $admin['last_login_at'] ? date('M j, Y g:i A', strtotime($admin['last_login_at'])) : 'Never' ?>
                            </div>
                        </div>

                        <div style="display: flex; gap: var(--spacing-xs); justify-content: flex-end;">
                            <a href="<?= base_url('dakoii/organizations/admins/' . $admin['id']) ?>" class="btn-icon" title="View Profile">👁️</a>
                            <button onclick="editAdmin(<?= $admin['id'] ?>)" class="btn-icon" title="Edit">✏️</button>
                            <button onclick="toggleAdminStatus(<?= $admin['id'] ?>, '<?= $admin['is_activated'] ? 'deactivate' : 'activate' ?>')" class="btn-icon" title="<?= $admin['is_activated'] ? 'Deactivate' : 'Activate' ?>">
                                <?= $admin['is_activated'] ? '⏸️' : '▶️' ?>
                            </button>
                            <button onclick="resetPassword(<?= $admin['id'] ?>)" class="btn-icon" title="Reset Password">🔑</button>
                            <button onclick="deleteAdmin(<?= $admin['id'] ?>, '<?= esc($admin['name']) ?>')" class="btn-icon" title="Delete">🗑️</button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.admin-card {
    background: var(--surface-card);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(6, 255, 165, 0.1);
    color: #06FFA5;
}

.status-pending {
    background: rgba(255, 183, 0, 0.1);
    color: #FFB700;
}

.role-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-admin {
    background: rgba(255, 0, 110, 0.1);
    color: #FF006E;
}

.role-moderator {
    background: rgba(131, 56, 236, 0.1);
    color: #8338EC;
}

.role-user {
    background: rgba(0, 212, 255, 0.1);
    color: #00D4FF;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--glass-bg);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 14px;
}

.btn-icon:hover {
    background: var(--glass-border);
    transform: translateY(-1px);
}
</style>

<script>
function editAdmin(adminId) {
    // Implement edit modal or redirect to edit page
    window.location.href = '<?= base_url('dakoii/organizations/admins/') ?>' + adminId + '/edit';
}

function toggleAdminStatus(adminId, action) {
    if (confirm('Are you sure you want to ' + action + ' this administrator?')) {
        fetch('<?= base_url('dakoii/organizations/admins/') ?>' + adminId + '/toggle-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function resetPassword(adminId) {
    if (confirm('Are you sure you want to reset the password for this administrator? A new temporary password will be sent to their email.')) {
        fetch('<?= base_url('dakoii/organizations/admins/') ?>' + adminId + '/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Password reset successfully. New temporary password sent to administrator\'s email.');
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function deleteAdmin(adminId, name) {
    if (confirm('Are you sure you want to delete administrator "' + name + '"? This action cannot be undone.')) {
        fetch('<?= base_url('dakoii/organizations/admins/') ?>' + adminId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}
</script>
<?= $this->endSection() ?>
