<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/events') ?>" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Events
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/events/' . $event['id'] . '/edit') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-pencil me-2"></i>
    Edit Event
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-calendar-event me-2"></i>
            Event Details
        </h1>
        <p class="text-muted mb-0">
            Complete information for event: <strong><?= esc($event['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Event Overview Card -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="d-flex align-items-center gap-3">
            <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                 style="width: 48px; height: 48px; background: var(--promis-gradient-primary);">
                <i class="bi bi-calendar-event"></i>
            </div>
            <div class="flex-grow-1">
                <h5 class="fw-semibold text-primary mb-1">
                    <?= esc($event['title']) ?>
                </h5>
                <div class="text-muted small">
                    Event Date: <?= date('M j, Y', strtotime($event['event_date'])) ?>
                </div>
            </div>
            <div>
                <?php
                $statusClasses = [
                    'active' => 'bg-warning',
                    'resolved' => 'bg-success',
                    'monitoring' => 'bg-info'
                ];
                $statusClass = $statusClasses[$event['status']] ?? 'bg-secondary';
                ?>
                <span class="badge <?= $statusClass ?> text-uppercase px-3 py-2">
                    <?= esc($event['status']) ?>
                </span>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Basic Information Grid -->
        <div class="row g-4 mb-4">
            
            <!-- Left Column -->
            <div class="col-md-6">
                <!-- Event Description -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-2 text-uppercase">
                        Event Description
                    </h6>
                    <p class="text-secondary mb-0">
                        <?= esc($event['description']) ?>
                    </p>
                </div>

                <!-- Event Classification -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                        Classification
                    </h6>
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="small text-muted">Event Type</div>
                            <div class="fw-semibold">
                                <?php
                                $typeIcons = [
                                    'delay' => 'clock',
                                    'suspension' => 'pause-circle',
                                    'resumption' => 'play-circle',
                                    'incident' => 'exclamation-triangle',
                                    'natural_disaster' => 'cloud-lightning',
                                    'funding_issue' => 'currency-dollar',
                                    'resource_issue' => 'tools',
                                    'stakeholder_issue' => 'people',
                                    'other' => 'question-circle'
                                ];
                                $icon = $typeIcons[$event['event_type']] ?? 'question-circle';
                                ?>
                                <i class="bi bi-<?= $icon ?> me-1"></i>
                                <?= esc(ucfirst(str_replace('_', ' ', $event['event_type']))) ?>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="small text-muted">Severity</div>
                            <div class="fw-semibold">
                                <?php
                                $severityColors = [
                                    'low' => 'text-success',
                                    'medium' => 'text-warning',
                                    'high' => 'text-danger',
                                    'critical' => 'text-dark'
                                ];
                                $severityColor = $severityColors[$event['severity']] ?? 'text-secondary';
                                ?>
                                <span class="<?= $severityColor ?>">
                                    <?= esc(ucfirst($event['severity'])) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Associated Milestone -->
                <?php if ($event['milestone_title']): ?>
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-2 text-uppercase">
                        Associated Milestone
                    </h6>
                    <div class="bg-light rounded p-3">
                        <div class="fw-semibold"><?= esc($event['milestone_code']) ?></div>
                        <div class="text-muted small"><?= esc($event['milestone_title']) ?></div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Right Column -->
            <div class="col-md-6">
                <!-- Timeline Information -->
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                        Timeline
                    </h6>
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="small text-muted">Event Date</div>
                            <div class="fw-semibold">
                                <?= date('M j, Y', strtotime($event['event_date'])) ?>
                            </div>
                        </div>
                        <?php if ($event['resolution_date']): ?>
                        <div class="col-12">
                            <div class="small text-muted">Resolution Date</div>
                            <div class="fw-semibold text-success">
                                <?= date('M j, Y', strtotime($event['resolution_date'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-12">
                            <div class="small text-muted">Created</div>
                            <div class="fw-semibold">
                                <?= date('M j, Y', strtotime($event['created_at'])) ?>
                                <?php if ($event['created_by_name']): ?>
                                    <div class="small text-muted">by <?= esc($event['created_by_name']) ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Impact Assessment -->
                <?php if ($event['impact_days'] || $event['impact_description']): ?>
                <div class="mb-4">
                    <h6 class="fw-semibold text-primary mb-3 text-uppercase">
                        Impact Assessment
                    </h6>
                    <?php if ($event['impact_days']): ?>
                    <div class="mb-2">
                        <div class="small text-muted">Impact Duration</div>
                        <div class="fw-semibold text-danger">
                            <?= $event['impact_days'] ?> days
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php if ($event['impact_description']): ?>
                    <div>
                        <div class="small text-muted">Impact Description</div>
                        <div class="text-secondary">
                            <?= esc($event['impact_description']) ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Resolution Information -->
        <?php if ($event['status'] === 'resolved' && $event['resolution_description']): ?>
        <div class="border-top pt-4">
            <h6 class="fw-semibold text-success mb-3 text-uppercase">
                <i class="bi bi-check-circle me-2"></i>
                Resolution Details
            </h6>
            <div class="bg-success bg-opacity-10 rounded p-3">
                <div class="text-secondary">
                    <?= esc($event['resolution_description']) ?>
                </div>
                <?php if ($event['resolution_date']): ?>
                <div class="small text-muted mt-2">
                    Resolved on <?= date('M j, Y', strtotime($event['resolution_date'])) ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Event Files Section -->
<?php if (!empty($eventFiles)): ?>
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-paperclip me-2"></i>
            Event Files
        </h5>
    </div>

    <div class="card-body">
        <div class="row g-3">
            <?php foreach ($eventFiles as $file): ?>
            <div class="col-md-6 col-lg-4">
                <div class="border rounded p-3">
                    <div class="d-flex align-items-center gap-2 mb-2">
                        <?php
                        $fileIcons = [
                            'image' => 'image',
                            'document' => 'file-text',
                            'video' => 'play-circle',
                            'audio' => 'music-note',
                            'other' => 'file'
                        ];
                        $icon = $fileIcons[$file['file_type']] ?? 'file';
                        ?>
                        <i class="bi bi-<?= $icon ?> text-primary"></i>
                        <div class="fw-semibold small"><?= esc($file['title']) ?></div>
                    </div>
                    <?php if ($file['description']): ?>
                    <div class="text-muted small mb-2">
                        <?= esc($file['description']) ?>
                    </div>
                    <?php endif; ?>
                    <div class="small text-muted">
                        <?= date('M j, Y', strtotime($file['created_at'])) ?>
                        <?php if ($file['file_size']): ?>
                            • <?= number_format($file['file_size'] / 1024, 1) ?> KB
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="card">
    <div class="card-body">
        <div class="d-flex gap-3 justify-content-center">
            <?php if ($event['status'] !== 'resolved'): ?>
            <button onclick="showResolveModal()" class="btn btn-success">
                <i class="bi bi-check-circle me-2"></i>
                Resolve Event
            </button>
            <?php endif; ?>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/events/' . $event['id'] . '/edit') ?>" class="btn btn-primary">
                <i class="bi bi-pencil me-2"></i>
                Edit Event
            </a>
            <button onclick="showDeleteModal()" class="btn btn-outline-danger">
                <i class="bi bi-trash me-2"></i>
                Delete Event
            </button>
        </div>
    </div>
</div>

<!-- Resolve Event Modal -->
<div class="modal fade" id="resolveModal" tabindex="-1" aria-labelledby="resolveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resolveModalLabel">Resolve Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/events/' . $event['id'] . '/resolve') ?>">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <p>Are you sure you want to resolve this event?</p>
                    <div class="mb-3">
                        <label for="resolution_description" class="form-label fw-semibold">Resolution Description <span class="text-danger">*</span></label>
                        <textarea name="resolution_description" id="resolution_description" class="form-control border-danger" rows="3" required placeholder="Describe how this event was resolved..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Resolve Event</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Event Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this event?</p>
                <p class="text-danger small">This action cannot be undone. All event data will be permanently removed.</p>
            </div>
            <div class="modal-footer">
                <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/events/' . $event['id'] . '/delete') ?>">
                    <?= csrf_field() ?>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Event</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function showResolveModal() {
    new bootstrap.Modal(document.getElementById('resolveModal')).show();
}

function showDeleteModal() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
