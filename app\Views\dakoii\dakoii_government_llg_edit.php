<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to LLGs
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">✏️</span>
            Edit Local Level Government
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Update the LLG information in the government structure hierarchy.
        </p>
    </div>

    <!-- Validation Errors -->
    <?php if (isset($validation) && !empty($validation)): ?>
        <div style="margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: var(--radius-md);">
            <h4 style="margin: 0 0 var(--spacing-sm) 0; color: #dc3545;">Validation Errors</h4>
            <ul style="margin: 0; padding-left: var(--spacing-md); color: #dc3545;">
                <?php foreach ($validation as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Edit LLG Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">LLG Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Update the Local Level Government information below.</p>

            <form method="POST" action="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/edit') ?>" class="llg-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="district_id" class="form-label">District *</label>
                        <select id="district_id" name="district_id" class="form-input" required
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select District</option>
                            <?php if (isset($districts) && !empty($districts)): ?>
                                <?php foreach ($districts as $district): ?>
                                    <option value="<?= $district['id'] ?>" <?= ($llg['district_id'] == $district['id']) ? 'selected' : '' ?>
                                            style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">
                                        <?= esc($district['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Select the district this LLG belongs to
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="name" class="form-label">LLG Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               value="<?= esc($llg['name']) ?>" required
                               placeholder="Enter the full LLG name">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Official name of the Local Level Government as it should appear in the system
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="llg_code" class="form-label">LLG Code *</label>
                        <input type="text" id="llg_code" name="llg_code" class="form-input"
                               value="<?= esc($llg['llg_code']) ?>" required
                               placeholder="e.g., DARU, KIWAI, PMNE"
                               style="text-transform: uppercase;">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Short code or abbreviation for the LLG
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="llg_type" class="form-label">LLG Type</label>
                        <select id="llg_type" name="llg_type" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select LLG Type (Optional)</option>
                            <option value="urban" <?= (($llg['llg_type'] ?? '') == 'urban') ? 'selected' : '' ?>
                                    style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">Urban LLG</option>
                            <option value="rural" <?= (($llg['llg_type'] ?? '') == 'rural') ? 'selected' : '' ?>
                                    style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">Rural LLG</option>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Classification of the Local Level Government (Urban or Rural)
                        </small>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>

                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <select id="geojson_id" name="geojson_id" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select LLG Boundary (Optional)</option>
                            <?php
                            // Load LLG options from JSON
                            $jsonPath = FCPATH . 'map_jsons/png_llg_boundaries_2011.json';
                            if (file_exists($jsonPath)) {
                                $jsonData = json_decode(file_get_contents($jsonPath), true);
                                if (isset($jsonData['features'])) {
                                    $llgsOptions = [];
                                    foreach ($jsonData['features'] as $feature) {
                                        if (isset($feature['properties']['GEOCODE']) && isset($feature['properties']['LLGNAME'])) {
                                            $llgsOptions[] = [
                                                'id' => $feature['properties']['GEOCODE'],
                                                'name' => $feature['properties']['LLGNAME']
                                            ];
                                        }
                                    }
                                    // Sort by name
                                    usort($llgsOptions, function($a, $b) {
                                        return strcmp($a['name'], $b['name']);
                                    });

                                    foreach ($llgsOptions as $llgOption) {
                                        $selected = (($llg['geojson_id'] ?? '') == $llgOption['id']) ? 'selected' : '';
                                        echo '<option value="' . esc($llgOption['id']) . '" ' . $selected . ' style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">' . esc($llgOption['name']) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Select the corresponding boundary from map data
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input"
                                   value="<?= esc($llg['map_centre_gps'] ?? '') ?>"
                                   placeholder="e.g., -6.314993,143.95555">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Latitude, Longitude coordinates
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input"
                                   value="<?= esc($llg['map_zoom'] ?? '12') ?>" min="1" max="20">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Default zoom level for map display (1-20)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Update LLG
                    </button>
                    <a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Help Information -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>LLG Update Guide</h3>
            </div>
            <div class="info-content">
                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Required Fields</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);">District: Parent district</li>
                        <li style="margin-bottom: var(--spacing-xs);">LLG Name: Official name</li>
                        <li>LLG Code: Short abbreviation</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">LLG Types</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);"><strong>Urban:</strong> City or town councils</li>
                        <li><strong>Rural:</strong> Rural area governments</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">LLG Code Examples</h4>
                    <div style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                        <div>Daru Urban LLG: DARU</div>
                        <div>Kiwai Rural LLG: KIWAI</div>
                        <div>Port Moresby North East: PMNE</div>
                        <div>Mount Hagen Urban: MHU</div>
                    </div>
                </div>

                <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                    <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                        <div style="font-size: 1.25rem;">💡</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">Tip</h4>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                                Make sure to update all related information when changing the parent district of an LLG.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Form styling consistency */
.form-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.info-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.info-header {
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.info-content {
    padding: var(--spacing-lg);
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<?= $this->endSection() ?> 