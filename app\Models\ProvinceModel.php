<?php

namespace App\Models;

/**
 * Model for the 'provinces' table.
 * Represents provinces/states with country reference, code, name, map defaults, and audit columns.
 */
class ProvinceModel extends BaseModel
{
    protected $table = 'provinces';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'country_id', 'prov_code', 'name', 'geojson_id',
        'map_centre_gps', 'map_zoom',
        'created_by', 'updated_by', 'deleted_by',
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    /**
     * Only essential validation rules: required for country_id, prov_code, and name.
     */
    protected $validationRules = [
        'country_id' => 'required',
        'prov_code'  => 'required',
        'name'       => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
} 