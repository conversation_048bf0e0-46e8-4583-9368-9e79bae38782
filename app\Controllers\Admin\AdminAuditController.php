<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;

/**
 * Admin Audit Controller
 * 
 * Handles audit trail viewing and management for the PROMIS Admin Portal including:
 * - Audit log viewing with filters
 * - Export functionality
 * - Audit statistics
 */
class AdminAuditController extends BaseController
{
    protected $db;
    
    public function __construct()
    {
        $this->db = \Config\Database::connect();
    }

    /**
     * View user audit trail - GET request
     */
    public function viewUserAudit()
    {
        // Get filters from query parameters
        $filters = [
            'user' => $this->request->getGet('user'),
            'action' => $this->request->getGet('action'),
            'module' => $this->request->getGet('module'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'search' => $this->request->getGet('search'),
            'per_page' => $this->request->getGet('per_page') ?: 50
        ];

        // Get audit logs with filters
        $auditLogs = $this->getAuditLogsWithFilters($filters);
        
        // Get audit statistics
        $stats = $this->getAuditStatistics();
        
        // Get unique users and modules for filters
        $users = $this->getUniqueUsers();
        $modules = $this->getUniqueModules();

        $data = [
            'title' => 'Audit Trail - PROMIS Admin',
            'page_title' => 'Audit Trail',
            'audit_logs' => $auditLogs,
            'stats' => $stats,
            'users' => $users,
            'modules' => $modules,
            'filters' => $filters
        ];

        return view('admin/admin_audit_trail', $data);
    }

    /**
     * Export audit logs to CSV - GET request
     */
    public function exportAuditLogs()
    {
        // Get filters from query parameters
        $filters = [
            'user' => $this->request->getGet('user'),
            'action' => $this->request->getGet('action'),
            'module' => $this->request->getGet('module'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'search' => $this->request->getGet('search')
        ];

        // Get audit logs for export (no pagination)
        $auditLogs = $this->getAuditLogsWithFilters($filters, false);

        // Generate CSV content
        $csvContent = $this->generateCSV($auditLogs);

        // Set headers for download
        $filename = 'audit_trail_' . date('Y-m-d_H-i-s') . '.csv';
        
        return $this->response
                    ->setHeader('Content-Type', 'text/csv')
                    ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
                    ->setBody($csvContent);
    }

    /**
     * Get audit logs with filters applied
     */
    private function getAuditLogsWithFilters($filters, $paginate = true)
    {
        $builder = $this->db->table('audit_logs')
                           ->select('*')
                           ->where('portal', 'admin')
                           ->orderBy('created_at', 'DESC');

        // Apply filters
        if (!empty($filters['user'])) {
            $builder->where('user_id', $filters['user']);
        }

        if (!empty($filters['action'])) {
            $builder->where('action', $filters['action']);
        }

        if (!empty($filters['module'])) {
            $builder->where('module', $filters['module']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('created_at >=', $filters['date_from'] . ' 00:00:00');
        }

        if (!empty($filters['date_to'])) {
            $builder->where('created_at <=', $filters['date_to'] . ' 23:59:59');
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('description', $filters['search'])
                   ->orLike('username', $filters['search'])
                   ->orLike('table_name', $filters['search'])
                   ->groupEnd();
        }

        if ($paginate) {
            $perPage = (int)$filters['per_page'];
            $page = (int)($this->request->getGet('page') ?: 1);
            $offset = ($page - 1) * $perPage;
            
            $builder->limit($perPage, $offset);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get audit statistics
     */
    private function getAuditStatistics()
    {
        $stats = [];

        // Total audit logs
        $stats['total_logs'] = $this->db->table('audit_logs')
                                       ->where('portal', 'admin')
                                       ->countAllResults();

        // Logs today
        $stats['logs_today'] = $this->db->table('audit_logs')
                                       ->where('portal', 'admin')
                                       ->where('DATE(created_at)', date('Y-m-d'))
                                       ->countAllResults();

        // Logs this week
        $stats['logs_this_week'] = $this->db->table('audit_logs')
                                           ->where('portal', 'admin')
                                           ->where('created_at >=', date('Y-m-d', strtotime('-7 days')))
                                           ->countAllResults();

        // Most active user
        $activeUser = $this->db->table('audit_logs')
                              ->select('username, COUNT(*) as log_count')
                              ->where('portal', 'admin')
                              ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
                              ->groupBy('username')
                              ->orderBy('log_count', 'DESC')
                              ->limit(1)
                              ->get()
                              ->getRowArray();

        $stats['most_active_user'] = $activeUser ? $activeUser['username'] . ' (' . $activeUser['log_count'] . ' actions)' : 'No activity';

        return $stats;
    }

    /**
     * Get unique users for filter dropdown
     */
    private function getUniqueUsers()
    {
        return $this->db->table('audit_logs')
                       ->select('DISTINCT user_id, username, user_full_name')
                       ->where('portal', 'admin')
                       ->where('user_id IS NOT NULL')
                       ->orderBy('username')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get unique modules for filter dropdown
     */
    private function getUniqueModules()
    {
        return $this->db->table('audit_logs')
                       ->select('DISTINCT module')
                       ->where('portal', 'admin')
                       ->where('module IS NOT NULL')
                       ->orderBy('module')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Generate CSV content from audit logs
     */
    private function generateCSV($auditLogs)
    {
        $csv = [];
        
        // CSV headers
        $csv[] = [
            'Date/Time',
            'User',
            'Action',
            'Module',
            'Table',
            'Record ID',
            'Description',
            'IP Address',
            'User Agent'
        ];

        // CSV data
        foreach ($auditLogs as $log) {
            $csv[] = [
                $log['created_at'],
                $log['username'] ?: 'System',
                $log['action'],
                $log['module'] ?: 'N/A',
                $log['table_name'],
                $log['primary_key'],
                $log['description'],
                $log['ip_address'],
                $log['user_agent']
            ];
        }

        // Convert to CSV string
        $output = '';
        foreach ($csv as $row) {
            $output .= '"' . implode('","', array_map('str_replace', ['"'], ['""'], $row)) . '"' . "\n";
        }

        return $output;
    }

    /**
     * Log audit event for this controller
     */
    private function logAuditEvent($action, $description)
    {
        $auditData = [
            'table_name' => 'audit_logs',
            'primary_key' => '0',
            'action' => $action,
            'new_data' => json_encode(['description' => $description]),
            'user_id' => session()->get('admin_user_id'),
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'audit_management',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'session_id' => session()->session_id,
            'request_url' => current_url(),
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->db->table('audit_logs')->insert($auditData);
    }
}
