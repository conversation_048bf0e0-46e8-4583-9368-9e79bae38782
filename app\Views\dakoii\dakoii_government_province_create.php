<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/provinces') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Provinces
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🏞️</span>
            Add New Province
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Create a new province entry in the government structure hierarchy.
        </p>
    </div>

    <!-- Create Province Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">Province Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Enter the basic information for the new province.</p>

            <?php if (isset($validation) && !empty($validation)): ?>
                <div class="alert alert-danger" style="margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: var(--radius-md); color: #dc3545;">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Please fix the following errors:</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md);">
                        <?php foreach ($validation as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?= base_url('dakoii/government/provinces/create') ?>" class="province-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="country_id" class="form-label">Country *</label>
                        <select id="country_id" name="country_id" class="form-input" required
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select Country</option>
                            <?php if (isset($countries) && !empty($countries)): ?>
                                <?php foreach ($countries as $country): ?>
                                    <option value="<?= $country['id'] ?>" <?= (old('country_id', isset($old_data['country_id']) ? $old_data['country_id'] : '') == $country['id']) ? 'selected' : '' ?>
                                            style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">
                                        <?= esc($country['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Select the country this province belongs to
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="name" class="form-label">Province Name *</label>
                        <input type="text" id="name" name="name" class="form-input"
                               value="<?= old('name', isset($old_data['name']) ? $old_data['name'] : '') ?>" required
                               placeholder="Enter the full province name">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Official name of the province as it should appear in the system
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="prov_code" class="form-label">Province Code *</label>
                        <input type="text" id="prov_code" name="prov_code" class="form-input"
                               value="<?= old('prov_code', isset($old_data['prov_code']) ? $old_data['prov_code'] : '') ?>" required
                               placeholder="e.g., WP, NCD, MP"
                               style="text-transform: uppercase;">
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Short code or abbreviation for the province
                        </small>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>

                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <select id="geojson_id" name="geojson_id" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select Province Boundary (Optional)</option>
                            <?php
                            // Load province options from JSON
                            $jsonPath = FCPATH . 'map_jsons/png_prov_boundaries_2011.json';
                            if (file_exists($jsonPath)) {
                                $jsonData = json_decode(file_get_contents($jsonPath), true);
                                if (isset($jsonData['features'])) {
                                    $provinces = [];
                                    foreach ($jsonData['features'] as $feature) {
                                        if (isset($feature['properties']['PROVID']) && isset($feature['properties']['PROVNAME'])) {
                                            $provinces[] = [
                                                'id' => $feature['properties']['PROVID'],
                                                'name' => $feature['properties']['PROVNAME']
                                            ];
                                        }
                                    }
                                    // Sort by name
                                    usort($provinces, function($a, $b) {
                                        return strcmp($a['name'], $b['name']);
                                    });

                                    foreach ($provinces as $province) {
                                        $selected = (old('geojson_id') == $province['id']) ? 'selected' : '';
                                        echo '<option value="' . esc($province['id']) . '" ' . $selected . ' style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">' . esc($province['name']) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Select the corresponding boundary from map data
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input"
                                   value="<?= old('map_centre_gps') ?>"
                                   placeholder="e.g., -6.314993,143.95555">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Latitude, Longitude coordinates
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input"
                                   value="<?= old('map_zoom', '8') ?>" min="1" max="20">
                            <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                                Default zoom level for map display (1-20)
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon">💾</i> Create Province
                    </button>
                    <a href="<?= base_url('dakoii/government/provinces') ?>" class="btn btn-secondary">
                        <i class="icon">✖️</i> Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Help Information -->
        <div class="info-card glass-effect">
            <div class="info-header">
                <h3>Province Creation Guide</h3>
            </div>
            <div class="info-content">
                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Required Fields</h4>
                    <ul style="margin: 0; padding-left: var(--spacing-md); color: var(--text-secondary);">
                        <li style="margin-bottom: var(--spacing-xs);">Country: Parent country</li>
                        <li style="margin-bottom: var(--spacing-xs);">Province Name: Official name</li>
                        <li>Province Code: Short abbreviation</li>
                    </ul>
                </div>

                <div style="margin-bottom: var(--spacing-md);">
                    <h4 style="margin: 0 0 var(--spacing-sm) 0; color: var(--text-primary); font-size: 1rem;">Province Code Examples</h4>
                    <div style="font-family: var(--font-mono); font-size: 0.875rem; color: var(--text-secondary);">
                        <div>Western Province: WP</div>
                        <div>National Capital District: NCD</div>
                        <div>Morobe Province: MP</div>
                        <div>Eastern Highlands: EHP</div>
                    </div>
                </div>

                <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                    <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                        <div style="font-size: 1.25rem;">💡</div>
                        <div>
                            <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">Tip</h4>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                                After creating a province, you can add districts and LLGs to build the complete administrative hierarchy.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Force dropdown option styling */
#country_id option, #geojson_id option {
    background: #1a1a2e !important;
    color: #ffffff !important;
    padding: 8px 12px !important;
}

#country_id option:hover, #geojson_id option:hover {
    background: #16213e !important;
    color: #ffffff !important;
}

#country_id option:selected, #geojson_id option:selected,
#country_id option:checked, #geojson_id option:checked {
    background: #00D4FF !important;
    color: #1a1a2e !important;
    font-weight: 600 !important;
}

/* Ensure select elements have proper styling */
#country_id, #geojson_id {
    background: rgba(26, 26, 46, 0.8) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}
</style>

<script>
// Auto-uppercase province code
document.getElementById('prov_code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});

// Form validation
document.querySelector('.province-form').addEventListener('submit', function(e) {
    const countryId = document.getElementById('country_id').value;
    const name = document.getElementById('name').value.trim();
    const provCode = document.getElementById('prov_code').value.trim();

    if (!countryId) {
        e.preventDefault();
        alert('Please select a country.');
        return false;
    }

    if (name.length < 2) {
        e.preventDefault();
        alert('Province name must be at least 2 characters long.');
        return false;
    }

    if (provCode.length < 2) {
        e.preventDefault();
        alert('Province code must be at least 2 characters long.');
        return false;
    }
});
</script>
<?= $this->endSection() ?>