<?php

namespace App\Models;

/**
 * Project Milestone File Model
 * 
 * Handles milestone evidence files with verification tracking.
 * Manages file uploads, verification status, and metadata.
 */
class ProjectMilestoneFileModel extends BaseModel
{
    protected $table      = 'project_milestone_files';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'file_path', 'file_type', 'file_size',
        'title', 'description', 'is_verified', 'verified_by', 'verified_at',
        'verification_notes', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'milestone_id'   => 'required|integer',
        'file_path'      => 'required|max_length[255]',
        'file_type'      => 'in_list[image,document,video,audio,other]',
        'title'          => 'required|max_length[150]',
        'file_size'      => 'permit_empty|integer',
        'is_verified'    => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'milestone_id' => [
            'required' => 'Milestone ID is required'
        ],
        'file_path' => [
            'required' => 'File path is required'
        ],
        'title' => [
            'required' => 'File title is required'
        ]
    ];
    
    /**
     * Get files by milestone
     */
    public function getByMilestone(int $milestoneId, ?bool $verifiedOnly = null): array
    {
        $query = $this->where('milestone_id', $milestoneId);
        
        if ($verifiedOnly !== null) {
            $query = $query->where('is_verified', $verifiedOnly ? 1 : 0);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get files by project
     */
    public function getByProject(int $projectId, ?string $fileType = null): array
    {
        $query = $this->select('project_milestone_files.*, project_milestones.title as milestone_title, project_milestones.milestone_code')
                     ->join('project_milestones', 'project_milestones.id = project_milestone_files.milestone_id')
                     ->where('project_milestone_files.project_id', $projectId);
        
        if ($fileType) {
            $query = $query->where('project_milestone_files.file_type', $fileType);
        }
        
        return $query->orderBy('project_milestone_files.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get files by type
     */
    public function getByType(string $fileType): array
    {
        return $this->select('project_milestone_files.*, project_milestones.title as milestone_title, projects.title as project_title, projects.pro_code')
                   ->join('project_milestones', 'project_milestones.id = project_milestone_files.milestone_id')
                   ->join('projects', 'projects.id = project_milestone_files.project_id')
                   ->where('project_milestone_files.file_type', $fileType)
                   ->orderBy('project_milestone_files.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get unverified files
     */
    public function getUnverifiedFiles(?int $projectId = null): array
    {
        $query = $this->select('project_milestone_files.*, project_milestones.title as milestone_title, projects.title as project_title, projects.pro_code')
                     ->join('project_milestones', 'project_milestones.id = project_milestone_files.milestone_id')
                     ->join('projects', 'projects.id = project_milestone_files.project_id')
                     ->where('project_milestone_files.is_verified', 0);
        
        if ($projectId) {
            $query = $query->where('project_milestone_files.project_id', $projectId);
        }
        
        return $query->orderBy('project_milestone_files.created_at', 'ASC')->findAll();
    }
    
    /**
     * Upload milestone file
     */
    public function uploadFile(array $fileData): bool
    {
        $fileData['created_by'] = session()->get('admin_user_id') ?? session()->get('user_id');
        $fileData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->insert($fileData) !== false;
    }
    
    /**
     * Verify file
     */
    public function verifyFile(int $fileId, bool $isVerified, ?string $notes = null, ?int $verifiedBy = null): bool
    {
        $data = [
            'is_verified' => $isVerified ? 1 : 0,
            'verified_by' => $verifiedBy,
            'verified_at' => $isVerified ? date('Y-m-d H:i:s') : null,
            'verification_notes' => $notes,
            'updated_by' => $verifiedBy
        ];
        
        return $this->update($fileId, $data);
    }
    
    /**
     * Delete file
     */
    public function deleteFile(int $fileId, ?int $deletedBy = null): bool
    {
        $file = $this->find($fileId);
        
        if (!$file) {
            return false;
        }
        
        // Soft delete the record
        $result = $this->update($fileId, [
            'deleted_by' => $deletedBy,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            // Delete physical file
            $fullPath = FCPATH . $file['file_path'];
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
        }
        
        return $result;
    }
    
    /**
     * Get file statistics
     */
    public function getFileStatistics(?int $projectId = null, ?int $milestoneId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        if ($milestoneId) {
            $query = $query->where('milestone_id', $milestoneId);
        }
        
        // Total files
        $stats['total_files'] = $query->countAllResults(false);
        
        // Verified files
        $stats['verified_files'] = $query->where('is_verified', 1)->countAllResults(false);
        
        // Unverified files
        $stats['unverified_files'] = $stats['total_files'] - $stats['verified_files'];
        
        // Files by type
        $typeCounts = $this->select('file_type, COUNT(*) as count')
                          ->groupBy('file_type');
        
        if ($projectId) {
            $typeCounts = $typeCounts->where('project_id', $projectId);
        }
        if ($milestoneId) {
            $typeCounts = $typeCounts->where('milestone_id', $milestoneId);
        }
        
        $typeResults = $typeCounts->findAll();
        $stats['by_type'] = array_column($typeResults, 'count', 'file_type');
        
        // Total file size (in KB)
        $sizeQuery = $this->selectSum('file_size', 'total_size');
        if ($projectId) {
            $sizeQuery = $sizeQuery->where('project_id', $projectId);
        }
        if ($milestoneId) {
            $sizeQuery = $sizeQuery->where('milestone_id', $milestoneId);
        }
        
        $sizeResult = $sizeQuery->first();
        $stats['total_size_kb'] = $sizeResult['total_size'] ?? 0;
        $stats['total_size_mb'] = round($stats['total_size_kb'] / 1024, 2);
        
        return $stats;
    }
    
    /**
     * Get milestone evidence count
     */
    public function getMilestoneEvidenceCount(int $milestoneId): int
    {
        return $this->where('milestone_id', $milestoneId)
                   ->where('is_verified', 1)
                   ->countAllResults();
    }
    
    /**
     * Get files with milestone and project info
     */
    public function getFilesWithDetails(?int $projectId = null): array
    {
        $query = $this->select('project_milestone_files.*, project_milestones.title as milestone_title, project_milestones.milestone_code, projects.title as project_title, projects.pro_code, users.name as uploaded_by_name')
                     ->join('project_milestones', 'project_milestones.id = project_milestone_files.milestone_id')
                     ->join('projects', 'projects.id = project_milestone_files.project_id')
                     ->join('users', 'users.id = project_milestone_files.created_by', 'left');
        
        if ($projectId) {
            $query = $query->where('project_milestone_files.project_id', $projectId);
        }
        
        return $query->orderBy('project_milestone_files.created_at', 'DESC')->findAll();
    }
}
