<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectEventModel;
use App\Models\ProjectEventFileModel;
use App\Models\ProjectMilestoneModel;

/**
 * Admin Project Event Controller
 * 
 * Handles project event management for the PROMIS Admin Portal including:
 * - Listing project events
 * - Creating new events (delays, incidents, etc.)
 * - Editing event details
 * - Resolving events
 * - Managing event files
 */
class AdminProjectEventController extends BaseController
{
    protected $projectModel;
    protected $projectEventModel;
    protected $projectEventFileModel;
    protected $projectMilestoneModel;
    
    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectEventModel = new ProjectEventModel();
        $this->projectEventFileModel = new ProjectEventFileModel();
        $this->projectMilestoneModel = new ProjectMilestoneModel();
    }

    /**
     * List project events - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project events
        $events = $this->projectEventModel->getEventsWithDetails($projectId);

        // Get event statistics
        $eventStats = [
            'total' => count($events),
            'active' => count(array_filter($events, fn($e) => $e['status'] === 'active')),
            'resolved' => count(array_filter($events, fn($e) => $e['status'] === 'resolved')),
            'monitoring' => count(array_filter($events, fn($e) => $e['status'] === 'monitoring')),
            'critical' => count(array_filter($events, fn($e) => $e['severity'] === 'critical')),
            'high' => count(array_filter($events, fn($e) => $e['severity'] === 'high'))
        ];

        $data = [
            'title' => 'Project Events - ' . $project['title'],
            'project' => $project,
            'events' => $events,
            'eventStats' => $eventStats,
            'admin_organization_name' => session()->get('admin_organization_name')
        ];

        return view('admin/admin_projects_events_list', $data);
    }

    /**
     * Show create event form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project milestones for optional association
        $milestones = $this->projectMilestoneModel->where('project_id', $projectId)
                                                 ->where('deleted_at', null)
                                                 ->orderBy('target_date', 'ASC')
                                                 ->findAll();

        $data = [
            'title' => 'Create Project Event - ' . $project['title'],
            'project' => $project,
            'milestones' => $milestones,
            'admin_organization_name' => session()->get('admin_organization_name')
        ];

        return view('admin/admin_projects_events_create', $data);
    }

    /**
     * Store new event - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Prepare event data
        $eventData = [
            'project_id' => $projectId,
            'milestone_id' => $this->request->getPost('milestone_id') ?: null,
            'event_type' => $this->request->getPost('event_type'),
            'severity' => $this->request->getPost('severity'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'event_date' => $this->request->getPost('event_date'),
            'impact_days' => $this->request->getPost('impact_days') ?: null,
            'impact_description' => $this->request->getPost('impact_description') ?: null,
            'status' => 'active',
            'created_by' => session()->get('admin_user_id')
        ];

        try {
            $eventId = $this->projectEventModel->insert($eventData);

            if ($eventId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                               ->with('success', 'Project event created successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectEventModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating event: ' . $e->getMessage());
        }
    }

    /**
     * Show event details - GET request
     */
    public function show($projectId, $eventId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get event with details
        $event = $this->projectEventModel->select('project_events.*, project_milestones.title as milestone_title, project_milestones.milestone_code, users.name as created_by_name')
                                        ->join('project_milestones', 'project_milestones.id = project_events.milestone_id', 'left')
                                        ->join('users', 'users.id = project_events.created_by', 'left')
                                        ->where('project_events.id', $eventId)
                                        ->where('project_events.project_id', $projectId)
                                        ->where('project_events.deleted_at', null)
                                        ->first();

        if (!$event) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                           ->with('error', 'Event not found.');
        }

        // Get event files
        $eventFiles = $this->projectEventFileModel->getByEvent($eventId);

        $data = [
            'title' => 'Event Details - ' . $event['title'],
            'project' => $project,
            'event' => $event,
            'eventFiles' => $eventFiles,
            'admin_organization_name' => session()->get('admin_organization_name')
        ];

        return view('admin/admin_projects_events_show', $data);
    }

    /**
     * Show edit event form - GET request
     */
    public function edit($projectId, $eventId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get event
        $event = $this->projectEventModel->where('id', $eventId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$event) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                           ->with('error', 'Event not found.');
        }

        // Get project milestones for optional association
        $milestones = $this->projectMilestoneModel->where('project_id', $projectId)
                                                 ->where('deleted_at', null)
                                                 ->orderBy('target_date', 'ASC')
                                                 ->findAll();

        $data = [
            'title' => 'Edit Event - ' . $event['title'],
            'project' => $project,
            'event' => $event,
            'milestones' => $milestones,
            'admin_organization_name' => session()->get('admin_organization_name')
        ];

        return view('admin/admin_projects_events_edit', $data);
    }

    /**
     * Update event - POST request
     */
    public function update($projectId, $eventId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get event
        $event = $this->projectEventModel->where('id', $eventId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$event) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                           ->with('error', 'Event not found.');
        }

        // Prepare update data
        $updateData = [
            'milestone_id' => $this->request->getPost('milestone_id') ?: null,
            'event_type' => $this->request->getPost('event_type'),
            'severity' => $this->request->getPost('severity'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'event_date' => $this->request->getPost('event_date'),
            'impact_days' => $this->request->getPost('impact_days') ?: null,
            'impact_description' => $this->request->getPost('impact_description') ?: null,
            'resolution_description' => $this->request->getPost('resolution_description') ?: null,
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('admin_user_id')
        ];

        // If status is being changed to resolved, set resolution date
        if ($updateData['status'] === 'resolved' && $event['status'] !== 'resolved') {
            $updateData['resolution_date'] = date('Y-m-d');
        }

        try {
            $success = $this->projectEventModel->update($eventId, $updateData);

            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                               ->with('success', 'Event updated successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectEventModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating event: ' . $e->getMessage());
        }
    }

    /**
     * Delete event - POST request
     */
    public function delete($projectId, $eventId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get event
        $event = $this->projectEventModel->where('id', $eventId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$event) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                           ->with('error', 'Event not found.');
        }

        try {
            // Soft delete the event
            $success = $this->projectEventModel->update($eventId, [
                'deleted_by' => session()->get('admin_user_id'),
                'deleted_at' => date('Y-m-d H:i:s')
            ]);

            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                               ->with('success', 'Event deleted successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to delete event.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting event: ' . $e->getMessage());
        }
    }

    /**
     * Resolve event - POST request
     */
    public function resolve($projectId, $eventId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $resolutionDescription = $this->request->getPost('resolution_description');

        try {
            $success = $this->projectEventModel->resolveEvent($eventId, $resolutionDescription, session()->get('admin_user_id'));

            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/events'))
                               ->with('success', 'Event resolved successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to resolve event.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error resolving event: ' . $e->getMessage());
        }
    }
}
