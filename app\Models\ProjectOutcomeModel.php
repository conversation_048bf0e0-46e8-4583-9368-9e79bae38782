<?php

namespace App\Models;

/**
 * Project Outcome Model
 * 
 * Handles project outcomes with quantity and unit tracking.
 */
class ProjectOutcomeModel extends BaseModel
{
    protected $table      = 'project_outcomes';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'outcome_text', 'quantity', 'unit',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'   => 'required|integer',
        'outcome_text' => 'required|max_length[255]',
        'quantity'     => 'required|decimal'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'outcome_text' => [
            'required' => 'Outcome text is required'
        ],
        'quantity' => [
            'required' => 'Quantity is required'
        ]
    ];
    
    /**
     * Get outcomes by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get outcomes by unit
     */
    public function getByUnit(string $unit): array
    {
        return $this->where('unit', $unit)
                   ->orderBy('quantity', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get project outcome summary
     */
    public function getProjectOutcomeSummary(int $projectId): array
    {
        $outcomes = $this->getByProject($projectId);
        
        $summary = [
            'total_outcomes' => count($outcomes),
            'total_quantity' => 0,
            'by_unit' => []
        ];
        
        foreach ($outcomes as $outcome) {
            $summary['total_quantity'] += $outcome['quantity'];
            
            $unit = $outcome['unit'] ?: 'No Unit';
            if (!isset($summary['by_unit'][$unit])) {
                $summary['by_unit'][$unit] = [
                    'count' => 0,
                    'total_quantity' => 0
                ];
            }
            
            $summary['by_unit'][$unit]['count']++;
            $summary['by_unit'][$unit]['total_quantity'] += $outcome['quantity'];
        }
        
        return $summary;
    }
    
    /**
     * Get outcome statistics
     */
    public function getOutcomeStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total outcomes
        $stats['total_outcomes'] = $query->countAllResults();
        
        // Total quantity
        $totalResult = $query->selectSum('quantity')->first();
        $stats['total_quantity'] = (float) ($totalResult['quantity'] ?? 0);
        
        // Average quantity
        $stats['average_quantity'] = $stats['total_outcomes'] > 0 
            ? $stats['total_quantity'] / $stats['total_outcomes'] 
            : 0;
        
        // Outcomes by unit
        $unitStats = $this->select('unit, COUNT(*) as count, SUM(quantity) as total_quantity')
                         ->groupBy('unit')
                         ->orderBy('count', 'DESC')
                         ->findAll();
        
        $stats['by_unit'] = $unitStats;
        
        return $stats;
    }
    
    /**
     * Get top outcomes by quantity
     */
    public function getTopOutcomes(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this;
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('quantity', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
    
    /**
     * Search outcomes by text
     */
    public function searchOutcomes(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->like('outcome_text', $searchTerm);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get unique units
     */
    public function getUniqueUnits(): array
    {
        return $this->select('unit')
                   ->where('unit IS NOT NULL')
                   ->where('unit !=', '')
                   ->distinct()
                   ->orderBy('unit', 'ASC')
                   ->findAll();
    }
}
