<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Indicators</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create Impact Indicator
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Define a measurable impact metric for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Indicator Form -->
<div class="card">
    <div class="card-header">
        📊 Impact Indicator Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="indicator-create-form">
            <?= csrf_field() ?>

            <!-- Indicator Description -->
            <div class="form-group" style="margin-bottom: var(--spacing-xl);">
                <label for="indicator_text" class="form-label">
                    Indicator Description <span style="color: var(--brand-danger);">*</span>
                </label>
                <textarea id="indicator_text"
                          name="indicator_text"
                          class="form-input"
                          style="min-height: 120px; resize: vertical;"
                          placeholder="e.g., Number of beneficiaries served, Percentage increase in literacy rate, Reduction in travel time..."
                          required><?= old('indicator_text') ?></textarea>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Clear description of what will be measured for impact assessment (max 255 characters)
                </small>
                <?php if (isset($errors['indicator_text'])): ?>
                    <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                        <?= esc($errors['indicator_text']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column - Baseline Information -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        📈 Baseline Information
                    </h3>

                    <!-- Baseline Value -->
                    <div class="mb-4">
                        <label for="baseline_value" class="form-label fw-semibold">
                            Baseline Value
                        </label>
                        <input type="text"
                               id="baseline_value"
                               name="baseline_value"
                               class="form-control border-success"
                               value="<?= old('baseline_value') ?>"
                               placeholder="e.g., 0, 50%, Low, None"
                               maxlength="15">
                        <div class="form-text">
                            Starting value before project implementation - can be numbers or text (optional)
                        </div>
                        <?php if (isset($errors['baseline_value'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['baseline_value']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Baseline Date -->
                    <div class="mb-4">
                        <label for="baseline_date" class="form-label fw-semibold">
                            Baseline Date
                        </label>
                        <input type="date"
                               id="baseline_date"
                               name="baseline_date"
                               class="form-control border-success"
                               value="<?= old('baseline_date') ?>">
                        <div class="form-text">
                            Date when baseline value was measured (optional)
                        </div>
                        <?php if (isset($errors['baseline_date'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['baseline_date']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column - Target Information -->
                <div>
                    <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin-bottom: var(--spacing-lg); padding-bottom: var(--spacing-sm); border-bottom: 1px solid var(--border-color);">
                        🎯 Target Information
                    </h3>

                    <!-- Target Value -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="target_value" class="form-label">
                            Target Value
                        </label>
                        <input type="text"
                               id="target_value"
                               name="target_value"
                               class="form-input"
                               value="<?= old('target_value') ?>"
                               placeholder="e.g., 100, 80%, High, Complete"
                               maxlength="15">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Expected value to be achieved by project completion - can be numbers or text (optional)
                        </small>
                        <?php if (isset($errors['target_value'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['target_value']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Target Date -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="target_date" class="form-label">
                            Target Date
                        </label>
                        <input type="date"
                               id="target_date"
                               name="target_date"
                               class="form-input"
                               value="<?= old('target_date') ?>">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Expected date to achieve target value (optional)
                        </small>
                        <?php if (isset($errors['target_date'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['target_date']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Examples Section -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    💡 Examples of Impact Indicators
                </h4>
                <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                    <div><strong>Access:</strong> "Number of people with improved access to clean water" (Baseline: 0, Target: 500)</div>
                    <div><strong>Quality:</strong> "Percentage improvement in education quality scores" (Baseline: 60%, Target: 85%)</div>
                    <div><strong>Time:</strong> "Reduction in travel time to health facility (minutes)" (Baseline: 120, Target: 30)</div>
                    <div><strong>Income:</strong> "Average household income increase (USD)" (Baseline: 200, Target: 350)</div>
                    <div><strong>Health:</strong> "Reduction in child mortality rate per 1000" (Baseline: 50, Target: 25)</div>
                </div>
            </div>

            <!-- Important Note -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
                <h4 style="color: var(--brand-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    ℹ️ Important Note
                </h4>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                    <strong>Admin Portal:</strong> Define baseline and target values for monitoring framework setup.<br>
                    <strong>M&E Team:</strong> Will add actual values and dates during project implementation through the monitoring portal.
                </p>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators') ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">Create Indicator</span>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* 
Color-coded Form Input System:
- RED OUTLINE: Required fields (must be filled)
- GREEN OUTLINE: Optional fields (can be left empty)
*/

/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

/* Form input styling - applies to all input types */
.form-input,
input.form-input,
textarea.form-input,
select.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
    border: 2px solid #10B981 !important; /* Default green for optional fields */
}

/* Required fields have red outline - applies to all input types */
.form-input[required],
input.form-input[required],
textarea.form-input[required],
select.form-input[required] {
    border: 2px solid #EF4444 !important; /* Red for required fields */
}

/* Focus states for optional fields (green) */
.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: #059669 !important; /* Darker green on focus */
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Focus states for required fields (red) */
.form-input[required]:focus,
input.form-input[required]:focus,
textarea.form-input[required]:focus,
select.form-input[required]:focus {
    outline: none;
    border-width: 2px;
    border-color: #DC2626 !important; /* Darker red on focus */
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Hover states for optional fields (green) */
.form-input:hover {
    border-color: #059669 !important; /* Slightly darker green on hover */
}

/* Hover states for required fields (red) */
.form-input[required]:hover,
input.form-input[required]:hover,
textarea.form-input[required]:hover,
select.form-input[required]:hover {
    border-color: #DC2626 !important; /* Slightly darker red on hover */
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 120px;
}

/* Enhanced date inputs */
input[type="date"].form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 2v3m8-3v3m-9 8h10l1-10H4l1 10z'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced number inputs */
input[type="number"].form-input {
    text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    .form-actions {
        gap: var(--spacing-sm);
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}
</style>

<?= $this->endSection() ?>
