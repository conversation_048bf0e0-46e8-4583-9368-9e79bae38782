<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Overview
</a>
<a href="<?= base_url('dakoii/government/llgs/create') ?>" class="btn btn-primary">
    <i class="icon">➕</i> Add Local
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🏛️</span>
            Local Level Governments
            <?php if (isset($filtered) && $filtered && isset($district)): ?>
                <span style="font-size: 1rem; color: var(--text-secondary);">in <?= esc($district['name']) ?></span>
            <?php endif; ?>
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            <?php if (isset($filtered) && $filtered && isset($district)): ?>
                Local Level Governments in <?= esc($district['name']) ?> District.
            <?php else: ?>
                Manage LLGs and their administrative information.
            <?php endif; ?>
        </p>
        <?php if (isset($filtered) && $filtered && isset($district)): ?>
        <div style="margin-top: var(--spacing-md);">
            <a href="<?= base_url('dakoii/government/districts/' . $district['id']) ?>" class="btn btn-secondary" style="font-size: 0.875rem;">
                ← Back to <?= esc($district['name']) ?>
            </a>
            <a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary" style="font-size: 0.875rem;">
                View All LLGs
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- Statistics Card -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-xl);">
            <div>
                <div style="font-size: 2.5rem; font-weight: 700; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    <?= isset($llgs) ? count($llgs) : 0 ?>
                </div>
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Local</div>
            </div>
            <div style="flex: 1; height: 1px; background: var(--glass-border);"></div>
            <div style="text-align: right;">
                <div style="color: var(--text-secondary); font-size: 0.875rem;">Last Updated</div>
                <div style="color: var(--text-primary); font-weight: 600;"><?= date('M j, Y') ?></div>
            </div>
        </div>
    </div>

    <!-- Local List -->
    <?php if (!empty($llgs)): ?>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: var(--spacing-lg);">
            <?php foreach ($llgs as $item): ?>
                <div class="card">
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md); padding-bottom: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <div style="width: 60px; height: 40px; background: var(--gradient-primary); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; font-size: 0.875rem;">
                            🏛️
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 1.25rem;"><?= esc($item['name']) ?></h3>
                            <?php if (isset($item['code'])): ?>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">Code: <?= esc($item['code']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div style="display: flex; gap: var(--spacing-xs); flex-wrap: wrap;">
                        <a href="<?= base_url('dakoii/government/llgs/' . $item['id']) ?>" class="btn btn-primary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            👁️ View
                        </a>
                        <a href="<?= base_url('dakoii/government/llgs/' . $item['id'] . '/edit') ?>" class="btn btn-secondary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            ✏️ Edit
                        </a>
                        <form method="POST" action="<?= base_url('dakoii/government/llgs/' . $item['id'] . '/delete') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn" style="background: #dc3545; color: white; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);"
                                    onclick="return confirm('Are you sure you want to delete this llg?')"
                                    title="Delete llg">
                                🗑️ Delete
                            </button>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="card" style="text-align: center; padding: var(--spacing-2xl);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-md); opacity: 0.5;">🏛️</div>
            <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">No Local Found</h3>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Start by adding your first llg to the government structure.</p>
            <a href="<?= base_url('dakoii/government/llgs/create') ?>" class="btn btn-primary">
                <i class="icon">➕</i> Add First Local
            </a>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?>