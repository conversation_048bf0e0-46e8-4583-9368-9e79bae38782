<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('content') ?>

<div style="display: flex; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
    <a href="<?= base_url('dakoii/audit') ?>" class="btn btn-secondary">
        ← Back to Audit Trail
    </a>
</div>

<div class="card">
    <div class="card-header">
        Audit Log Details - ID: <?= esc($audit_log['id']) ?>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl);">
        
        <!-- Basic Information -->
        <div>
            <h3 style="margin: 0 0 var(--spacing-lg) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>
            
            <div style="display: grid; gap: var(--spacing-md);">
                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Date & Time</label>
                    <div style="color: var(--text-primary); font-family: var(--font-mono);">
                        <?= date('F j, Y \a\t g:i:s A', strtotime($audit_log['created_at'])) ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Portal</label>
                    <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.875rem; font-weight: 600; text-transform: uppercase; 
                        <?php if ($audit_log['portal'] === 'dakoii'): ?>
                            background: rgba(255, 0, 110, 0.2); color: #FF006E;
                        <?php elseif ($audit_log['portal'] === 'admin'): ?>
                            background: rgba(131, 56, 236, 0.2); color: #8338EC;
                        <?php else: ?>
                            background: rgba(0, 212, 255, 0.2); color: #00D4FF;
                        <?php endif; ?>">
                        <?= esc($audit_log['portal']) ?>
                    </span>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Module</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['module'] ? esc($audit_log['module']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Action</label>
                    <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.875rem; font-weight: 600; text-transform: uppercase;
                        <?php if ($audit_log['action'] === 'create'): ?>
                            background: rgba(40, 167, 69, 0.2); color: #28a745;
                        <?php elseif ($audit_log['action'] === 'update'): ?>
                            background: rgba(255, 183, 0, 0.2); color: #FFB700;
                        <?php elseif ($audit_log['action'] === 'delete'): ?>
                            background: rgba(220, 53, 69, 0.2); color: #dc3545;
                        <?php else: ?>
                            background: rgba(108, 117, 125, 0.2); color: #6c757d;
                        <?php endif; ?>">
                        <?= esc($audit_log['action']) ?>
                    </span>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Table</label>
                    <div style="color: var(--text-primary); font-family: var(--font-mono);">
                        <?= esc($audit_log['table_name']) ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Record ID</label>
                    <div style="color: var(--text-primary); font-family: var(--font-mono);">
                        <?= esc($audit_log['primary_key']) ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Description</label>
                    <div style="color: var(--text-primary);">
                        <?= esc($audit_log['description']) ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- User & Context Information -->
        <div>
            <h3 style="margin: 0 0 var(--spacing-lg) 0; color: var(--text-primary); font-size: 1.2rem;">User & Context</h3>
            
            <div style="display: grid; gap: var(--spacing-md);">
                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">User ID</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['user_id'] ? esc($audit_log['user_id']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Username</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['username'] ? esc($audit_log['username']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Full Name</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['user_full_name'] ? esc($audit_log['user_full_name']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">User Type</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['user_type'] ? esc($audit_log['user_type']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Organization</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['organization_name'] ? esc($audit_log['organization_name']) : '-' ?>
                        <?php if ($audit_log['organization_type']): ?>
                            <span style="color: var(--text-tertiary); font-size: 0.875rem;">(<?= esc($audit_log['organization_type']) ?>)</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Project</label>
                    <div style="color: var(--text-primary);">
                        <?= $audit_log['project_title'] ? esc($audit_log['project_title']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">IP Address</label>
                    <div style="color: var(--text-primary); font-family: var(--font-mono);">
                        <?= $audit_log['ip_address'] ? esc($audit_log['ip_address']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Request URL</label>
                    <div style="color: var(--text-primary); font-family: var(--font-mono); font-size: 0.875rem; word-break: break-all;">
                        <?= $audit_log['request_url'] ? esc($audit_log['request_url']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">Session ID</label>
                    <div style="color: var(--text-primary); font-family: var(--font-mono); font-size: 0.875rem; word-break: break-all;">
                        <?= $audit_log['session_id'] ? esc($audit_log['session_id']) : '-' ?>
                    </div>
                </div>

                <div>
                    <label style="display: block; font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">User Agent</label>
                    <div style="color: var(--text-primary); font-size: 0.875rem; word-break: break-all;">
                        <?= $audit_log['user_agent'] ? esc($audit_log['user_agent']) : '-' ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Changes -->
<?php if ($audit_log['old_data'] || $audit_log['new_data']): ?>
<div class="card">
    <div class="card-header">
        Data Changes
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl);">
        
        <!-- Old Data -->
        <div>
            <h3 style="margin: 0 0 var(--spacing-lg) 0; color: var(--text-primary); font-size: 1.2rem;">Before (Old Data)</h3>
            <?php if ($audit_log['old_data_decoded']): ?>
                <pre style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: var(--radius-md); padding: var(--spacing-md); color: var(--text-primary); font-family: var(--font-mono); font-size: 0.875rem; overflow-x: auto; white-space: pre-wrap;"><?= esc(json_encode($audit_log['old_data_decoded'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
            <?php else: ?>
                <div style="color: var(--text-tertiary); font-style: italic; padding: var(--spacing-md);">No previous data</div>
            <?php endif; ?>
        </div>

        <!-- New Data -->
        <div>
            <h3 style="margin: 0 0 var(--spacing-lg) 0; color: var(--text-primary); font-size: 1.2rem;">After (New Data)</h3>
            <?php if ($audit_log['new_data_decoded']): ?>
                <pre style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: var(--radius-md); padding: var(--spacing-md); color: var(--text-primary); font-family: var(--font-mono); font-size: 0.875rem; overflow-x: auto; white-space: pre-wrap;"><?= esc(json_encode($audit_log['new_data_decoded'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
            <?php else: ?>
                <div style="color: var(--text-tertiary); font-style: italic; padding: var(--spacing-md);">No new data</div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?= $this->endSection() ?>
