<?php

namespace App\Models;

/**
 * Project Event File Model
 * 
 * Handles file attachments for project events including evidence and documentation.
 * Manages file uploads, metadata, and organization.
 */
class ProjectEventFileModel extends BaseModel
{
    protected $table      = 'project_event_files';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'event_id', 'file_path', 'file_type', 'file_size',
        'title', 'description', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'event_id'       => 'required|integer',
        'file_path'      => 'required|max_length[255]',
        'file_type'      => 'in_list[image,document,video,audio,other]',
        'title'          => 'required|max_length[150]',
        'file_size'      => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'event_id' => [
            'required' => 'Event ID is required'
        ],
        'file_path' => [
            'required' => 'File path is required'
        ],
        'title' => [
            'required' => 'File title is required'
        ]
    ];
    
    /**
     * Get files by event
     */
    public function getByEvent(int $eventId): array
    {
        return $this->where('event_id', $eventId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get files by project
     */
    public function getByProject(int $projectId, ?string $fileType = null): array
    {
        $query = $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, project_events.severity')
                     ->join('project_events', 'project_events.id = project_event_files.event_id')
                     ->where('project_event_files.project_id', $projectId);
        
        if ($fileType) {
            $query = $query->where('project_event_files.file_type', $fileType);
        }
        
        return $query->orderBy('project_event_files.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get files by type
     */
    public function getByType(string $fileType): array
    {
        return $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, projects.title as project_title, projects.pro_code')
                   ->join('project_events', 'project_events.id = project_event_files.event_id')
                   ->join('projects', 'projects.id = project_event_files.project_id')
                   ->where('project_event_files.file_type', $fileType)
                   ->orderBy('project_event_files.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get files by event type
     */
    public function getByEventType(string $eventType): array
    {
        return $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, project_events.severity, projects.title as project_title, projects.pro_code')
                   ->join('project_events', 'project_events.id = project_event_files.event_id')
                   ->join('projects', 'projects.id = project_event_files.project_id')
                   ->where('project_events.event_type', $eventType)
                   ->orderBy('project_event_files.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get files by event severity
     */
    public function getByEventSeverity(string $severity): array
    {
        return $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, project_events.severity, projects.title as project_title, projects.pro_code')
                   ->join('project_events', 'project_events.id = project_event_files.event_id')
                   ->join('projects', 'projects.id = project_event_files.project_id')
                   ->where('project_events.severity', $severity)
                   ->orderBy('project_event_files.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Upload event file
     */
    public function uploadFile(array $fileData): bool
    {
        $fileData['created_by'] = session()->get('admin_user_id') ?? session()->get('user_id');
        $fileData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->insert($fileData) !== false;
    }
    
    /**
     * Delete file
     */
    public function deleteFile(int $fileId, ?int $deletedBy = null): bool
    {
        $file = $this->find($fileId);
        
        if (!$file) {
            return false;
        }
        
        // Soft delete the record
        $result = $this->update($fileId, [
            'deleted_by' => $deletedBy,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            // Delete physical file
            $fullPath = FCPATH . $file['file_path'];
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
        }
        
        return $result;
    }
    
    /**
     * Get file statistics
     */
    public function getFileStatistics(?int $projectId = null, ?int $eventId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        if ($eventId) {
            $query = $query->where('event_id', $eventId);
        }
        
        // Total files
        $stats['total_files'] = $query->countAllResults(false);
        
        // Files by type
        $typeCounts = $this->select('file_type, COUNT(*) as count')
                          ->groupBy('file_type');
        
        if ($projectId) {
            $typeCounts = $typeCounts->where('project_id', $projectId);
        }
        if ($eventId) {
            $typeCounts = $typeCounts->where('event_id', $eventId);
        }
        
        $typeResults = $typeCounts->findAll();
        $stats['by_type'] = array_column($typeResults, 'count', 'file_type');
        
        // Total file size (in KB)
        $sizeQuery = $this->selectSum('file_size', 'total_size');
        if ($projectId) {
            $sizeQuery = $sizeQuery->where('project_id', $projectId);
        }
        if ($eventId) {
            $sizeQuery = $sizeQuery->where('event_id', $eventId);
        }
        
        $sizeResult = $sizeQuery->first();
        $stats['total_size_kb'] = $sizeResult['total_size'] ?? 0;
        $stats['total_size_mb'] = round($stats['total_size_kb'] / 1024, 2);
        
        // Files by event type (if not filtering by specific event)
        if (!$eventId) {
            $eventTypeCounts = $this->select('project_events.event_type, COUNT(*) as count')
                                   ->join('project_events', 'project_events.id = project_event_files.event_id')
                                   ->groupBy('project_events.event_type');
            
            if ($projectId) {
                $eventTypeCounts = $eventTypeCounts->where('project_event_files.project_id', $projectId);
            }
            
            $eventTypeResults = $eventTypeCounts->findAll();
            $stats['by_event_type'] = array_column($eventTypeResults, 'count', 'event_type');
        }
        
        // Files by event severity (if not filtering by specific event)
        if (!$eventId) {
            $severityCounts = $this->select('project_events.severity, COUNT(*) as count')
                                  ->join('project_events', 'project_events.id = project_event_files.event_id')
                                  ->groupBy('project_events.severity');
            
            if ($projectId) {
                $severityCounts = $severityCounts->where('project_event_files.project_id', $projectId);
            }
            
            $severityResults = $severityCounts->findAll();
            $stats['by_severity'] = array_column($severityResults, 'count', 'severity');
        }
        
        return $stats;
    }
    
    /**
     * Get event file count
     */
    public function getEventFileCount(int $eventId): int
    {
        return $this->where('event_id', $eventId)->countAllResults();
    }
    
    /**
     * Get files with event and project info
     */
    public function getFilesWithDetails(?int $projectId = null): array
    {
        $query = $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, project_events.severity, project_events.event_date, projects.title as project_title, projects.pro_code, users.name as uploaded_by_name')
                     ->join('project_events', 'project_events.id = project_event_files.event_id')
                     ->join('projects', 'projects.id = project_event_files.project_id')
                     ->join('users', 'users.id = project_event_files.created_by', 'left');
        
        if ($projectId) {
            $query = $query->where('project_event_files.project_id', $projectId);
        }
        
        return $query->orderBy('project_event_files.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get recent files
     */
    public function getRecentFiles(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, project_events.severity, projects.title as project_title, projects.pro_code')
                     ->join('project_events', 'project_events.id = project_event_files.event_id')
                     ->join('projects', 'projects.id = project_event_files.project_id');
        
        if ($projectId) {
            $query = $query->where('project_event_files.project_id', $projectId);
        }
        
        return $query->orderBy('project_event_files.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
    
    /**
     * Get files by date range
     */
    public function getFilesByDateRange(string $startDate, string $endDate, ?int $projectId = null): array
    {
        $query = $this->select('project_event_files.*, project_events.title as event_title, project_events.event_type, project_events.severity, projects.title as project_title, projects.pro_code')
                     ->join('project_events', 'project_events.id = project_event_files.event_id')
                     ->join('projects', 'projects.id = project_event_files.project_id')
                     ->where('project_event_files.created_at >=', $startDate . ' 00:00:00')
                     ->where('project_event_files.created_at <=', $endDate . ' 23:59:59');
        
        if ($projectId) {
            $query = $query->where('project_event_files.project_id', $projectId);
        }
        
        return $query->orderBy('project_event_files.created_at', 'DESC')->findAll();
    }
}
