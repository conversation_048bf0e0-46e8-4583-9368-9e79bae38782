<?php

namespace App\Models;

/**
 * Project Issues Addressed Model
 * 
 * Handles issues that projects address with quantity and unit tracking.
 */
class ProjectIssuesAddressedModel extends BaseModel
{
    protected $table      = 'project_issues_addressed';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'issue_type', 'description',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'  => 'required|integer',
        'issue_type'  => 'required|in_list[direct,indirect]',
        'description' => 'required|max_length[500]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'issue_type' => [
            'required' => 'Issue type is required',
            'in_list' => 'Issue type must be either direct or indirect'
        ],
        'description' => [
            'required' => 'Issue description is required'
        ]
    ];
    
    /**
     * Get issues by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('created_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get issues by type
     */
    public function getByType(string $issueType): array
    {
        return $this->where('issue_type', $issueType)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get project issues summary
     */
    public function getProjectIssuesSummary(int $projectId): array
    {
        $issues = $this->getByProject($projectId);

        $summary = [
            'total_issues' => count($issues),
            'by_type' => [
                'direct' => 0,
                'indirect' => 0
            ]
        ];

        foreach ($issues as $issue) {
            $summary['by_type'][$issue['issue_type']]++;
        }

        return $summary;
    }
    
    /**
     * Get issues statistics
     */
    public function getIssuesStatistics(?int $projectId = null): array
    {
        $stats = [];

        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }

        // Total issues
        $stats['total_issues'] = $query->countAllResults();

        // Issues by type
        $typeStats = $this->select('issue_type, COUNT(*) as count')
                         ->groupBy('issue_type')
                         ->orderBy('count', 'DESC');

        if ($projectId) {
            $typeStats = $typeStats->where('project_id', $projectId);
        }

        $typeResults = $typeStats->findAll();
        $stats['by_type'] = [
            'direct' => 0,
            'indirect' => 0
        ];

        foreach ($typeResults as $result) {
            $stats['by_type'][$result['issue_type']] = (int) $result['count'];
        }

        return $stats;
    }
    
    /**
     * Get recent issues
     */
    public function getRecentIssues(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this;

        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }

        return $query->orderBy('created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
    
    /**
     * Search issues by text
     */
    public function searchIssues(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->like('description', $searchTerm);

        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }

        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get unique units
     */
    public function getUniqueUnits(): array
    {
        return $this->select('unit')
                   ->where('unit IS NOT NULL')
                   ->where('unit !=', '')
                   ->distinct()
                   ->orderBy('unit', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get common issues across projects
     */
    public function getCommonIssues(int $limit = 10): array
    {
        return $this->select('issue_text, COUNT(*) as project_count, SUM(quantity) as total_quantity')
                   ->groupBy('issue_text')
                   ->having('project_count >', 1)
                   ->orderBy('project_count', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
}
