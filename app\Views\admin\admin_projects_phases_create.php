<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-phases" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-diagram-3 me-2"></i>
            Create New Phase
        </h1>
        <p class="text-muted mb-0">
            Add a new phase to project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Phase Form -->
<div class="card">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-diagram-3 me-2"></i>
            Phase Information
        </h5>
    </div>

    <div class="card-body">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/phases/create') ?>" class="phase-create-form">
            <?= csrf_field() ?>

            <div class="row g-4 mb-4">

                <!-- Left Column -->
                <div class="col-md-6">
                    <!-- Phase Code -->
                    <div class="mb-4">
                        <label for="phase_code" class="form-label fw-semibold">
                            Phase Code <span class="text-danger">*</span>
                        </label>
                        <input type="text" id="phase_code" name="phase_code" class="form-control border-danger"
                               value="<?= old('phase_code') ?>" placeholder="e.g., PH001, INIT, PLAN" required>
                        <div class="form-text">
                            Unique identifier for this phase (max 20 characters)
                        </div>
                    </div>

                    <!-- Phase Title -->
                    <div class="mb-4">
                        <label for="title" class="form-label fw-semibold">
                            Phase Title <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               id="title"
                               name="title"
                               class="form-control border-danger"
                               value="<?= old('title') ?>"
                               placeholder="e.g., Planning Phase, Implementation Phase"
                               required>
                        <div class="form-text">
                            Descriptive name for this phase (max 150 characters)
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <label for="status" class="form-label fw-semibold">
                            Status <span class="text-danger">*</span>
                        </label>
                        <select id="status"
                                name="status"
                                class="form-select border-danger"
                                required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="deactivated" <?= old('status') === 'deactivated' ? 'selected' : '' ?>>Deactivated</option>
                        </select>
                        <div class="form-text">
                            Current status of this phase
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Start Date -->
                    <div class="mb-4">
                        <label for="start_date" class="form-label fw-semibold">
                            Start Date
                        </label>
                        <input type="date"
                               id="start_date"
                               name="start_date"
                               class="form-control border-success"
                               value="<?= old('start_date') ?>">
                        <div class="form-text">
                            When this phase is scheduled to start (optional)
                        </div>
                    </div>

                    <!-- End Date -->
                    <div class="mb-4">
                        <label for="end_date" class="form-label fw-semibold">
                            End Date
                        </label>
                        <input type="date"
                               id="end_date"
                               name="end_date"
                               class="form-control border-success"
                               value="<?= old('end_date') ?>">
                        <div class="form-text">
                            When this phase is scheduled to end (optional)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="mb-4">
                <label for="description" class="form-label fw-semibold">
                    Description
                </label>
                <textarea id="description"
                          name="description"
                          class="form-control border-success"
                          rows="4"
                          placeholder="Detailed description of this phase, its objectives, and key activities..."><?= old('description') ?></textarea>
                <div class="form-text">
                    Optional detailed description of the phase
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary promis-btn-gradient">
                    <i class="bi bi-check-circle me-2"></i>
                    Create Phase
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Form validation styling - matches project edit form */
.phase-create-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.phase-create-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.phase-create-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Enhanced form inputs for mobile */
.phase-create-form .form-input {
    min-height: 44px;
    font-size: 0.875rem; /* Original size */
    padding: var(--spacing-md);
}

.phase-create-form select.form-input {
    min-height: 44px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.phase-create-form textarea.form-input {
    resize: vertical;
    min-height: 100px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .phase-create-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 0.875rem;
        min-height: 48px; /* Larger touch target on mobile only */
    }

    /* Larger touch targets on mobile */
    .phase-create-form .form-input {
        padding: var(--spacing-md);
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px; /* Larger touch target on mobile only */
    }

    /* Better spacing on mobile */
    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    /* Stack form actions vertically on very small screens */
    .form-actions {
        gap: var(--spacing-sm);
    }

    /* Adjust card padding for small screens */
    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

.phase-create-form .form-input:focus {
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}
</style>

<?= $this->endSection() ?>
