<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectImpactIndicatorModel;

/**
 * Admin Project Impact Indicator Controller
 * 
 * Handles CRUD operations for project impact indicators in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectIndicatorController extends BaseController
{
    protected $projectModel;
    protected $projectImpactIndicatorModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectImpactIndicatorModel = new ProjectImpactIndicatorModel();
    }

    /**
     * Show indicators list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get indicators for this project
        $indicators = $this->projectImpactIndicatorModel->getByProject($projectId);

        // Get indicator statistics
        $indicatorStats = $this->projectImpactIndicatorModel->getIndicatorStatistics($projectId);

        // Get indicator performance analysis
        $indicatorPerformance = $this->projectImpactIndicatorModel->getIndicatorPerformance($projectId);

        $data = [
            'title' => 'Impact Indicators - PROMIS Admin',
            'page_title' => 'Impact Indicators',
            'project' => $project,
            'indicators' => $indicators,
            'indicatorStats' => $indicatorStats,
            'indicatorPerformance' => $indicatorPerformance
        ];

        return view('admin/admin_projects_indicators_list', $data);
    }

    /**
     * Show create indicator form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Create Impact Indicator - PROMIS Admin',
            'page_title' => 'Create Impact Indicator',
            'project' => $project
        ];

        return view('admin/admin_projects_indicators_create', $data);
    }

    /**
     * Store indicator - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'indicator_text' => 'required|max_length[255]',
            'baseline_value' => 'permit_empty|max_length[15]',
            'baseline_date' => 'permit_empty|valid_date',
            'target_value' => 'permit_empty|max_length[15]',
            'target_date' => 'permit_empty|valid_date'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data - handle 0 as valid value
        $baselineValue = $this->request->getPost('baseline_value');
        $targetValue = $this->request->getPost('target_value');

        $indicatorData = [
            'project_id' => $projectId,
            'indicator_text' => $this->request->getPost('indicator_text'),
            'baseline_value' => ($baselineValue !== '' && $baselineValue !== null) ? $baselineValue : null,
            'baseline_date' => $this->request->getPost('baseline_date') ?: null,
            'target_value' => ($targetValue !== '' && $targetValue !== null) ? $targetValue : null,
            'target_date' => $this->request->getPost('target_date') ?: null,
            'created_by' => $adminUserId
        ];

        try {
            $indicatorId = $this->projectImpactIndicatorModel->insert($indicatorData);

            if ($indicatorId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                               ->with('success', 'Impact indicator created successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectImpactIndicatorModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating impact indicator: ' . $e->getMessage());
        }
    }

    /**
     * Show edit indicator form - GET request
     */
    public function edit($projectId, $indicatorId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get indicator
        $indicator = $this->projectImpactIndicatorModel->where('id', $indicatorId)
                                                      ->where('project_id', $projectId)
                                                      ->where('deleted_at', null)
                                                      ->first();

        if (!$indicator) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                           ->with('error', 'Impact indicator not found.');
        }

        $data = [
            'title' => 'Edit Impact Indicator - PROMIS Admin',
            'page_title' => 'Edit Impact Indicator',
            'project' => $project,
            'indicator' => $indicator
        ];

        return view('admin/admin_projects_indicators_edit', $data);
    }

    /**
     * Update indicator - POST request
     */
    public function update($projectId, $indicatorId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get indicator
        $indicator = $this->projectImpactIndicatorModel->where('id', $indicatorId)
                                                      ->where('project_id', $projectId)
                                                      ->where('deleted_at', null)
                                                      ->first();

        if (!$indicator) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                           ->with('error', 'Impact indicator not found.');
        }

        // Validation rules
        $rules = [
            'indicator_text' => 'required|max_length[255]',
            'baseline_value' => 'permit_empty|max_length[15]',
            'baseline_date' => 'permit_empty|valid_date',
            'target_value' => 'permit_empty|max_length[15]',
            'target_date' => 'permit_empty|valid_date'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data - handle 0 as valid value
        $baselineValue = $this->request->getPost('baseline_value');
        $targetValue = $this->request->getPost('target_value');

        $indicatorData = [
            'indicator_text' => $this->request->getPost('indicator_text'),
            'baseline_value' => ($baselineValue !== '' && $baselineValue !== null) ? $baselineValue : null,
            'baseline_date' => $this->request->getPost('baseline_date') ?: null,
            'target_value' => ($targetValue !== '' && $targetValue !== null) ? $targetValue : null,
            'target_date' => $this->request->getPost('target_date') ?: null,
            'updated_by' => $adminUserId
        ];

        try {
            $result = $this->projectImpactIndicatorModel->update($indicatorId, $indicatorData);

            if ($result) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                               ->with('success', 'Impact indicator updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectImpactIndicatorModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating impact indicator: ' . $e->getMessage());
        }
    }

    /**
     * Delete indicator - POST request
     */
    public function delete($projectId, $indicatorId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get indicator
        $indicator = $this->projectImpactIndicatorModel->where('id', $indicatorId)
                                                      ->where('project_id', $projectId)
                                                      ->where('deleted_at', null)
                                                      ->first();

        if (!$indicator) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                           ->with('error', 'Impact indicator not found.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectImpactIndicatorModel->update($indicatorId, ['deleted_by' => $adminUserId]);
            $this->projectImpactIndicatorModel->delete($indicatorId);

            return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                           ->with('success', 'Impact indicator deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/indicators'))
                           ->with('error', 'Error deleting impact indicator: ' . $e->getMessage());
        }
    }

    /**
     * Get indicators data for project show view
     * This method is called by AdminProjectController to get indicators data for the project profile
     */
    public function getIndicatorsForProjectShow($projectId)
    {
        // Get indicators for this project
        $indicators = $this->projectImpactIndicatorModel->getByProject($projectId);

        // Get indicator statistics
        $indicatorStats = $this->projectImpactIndicatorModel->getIndicatorStatistics($projectId);

        // Get indicator summary
        $indicatorSummary = $this->projectImpactIndicatorModel->getProjectImpactSummary($projectId);

        return [
            'indicators' => $indicators,
            'indicatorStats' => $indicatorStats,
            'indicatorSummary' => $indicatorSummary
        ];
    }
}
