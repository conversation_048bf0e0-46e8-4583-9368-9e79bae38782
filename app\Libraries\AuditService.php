<?php

namespace App\Libraries;

use App\Models\AuditLogModel;
use Config\Services;

/**
 * Unified Audit Service
 *
 * This service handles manual audit logging for all portals in the PROMIS application.
 * It automatically detects the portal context and logs activities accordingly.
 */
class AuditService
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    /**
     * Log authentication events manually
     */
    public function logAuthentication(string $action, string $description, ?int $userId = null): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $this->detectPortalContext();

        if (!$portalContext) {
            return false; // No valid portal context
        }

        $userInfo = $this->getUserInfo($portalContext, $session);
        $projectInfo = $this->getProjectContext($session);

        $data = [
            'table_name'        => $portalContext['portal'] . '_users',
            'primary_key'       => (string) ($userId ?? $userInfo['user_id'] ?? 'unknown'),
            'action'            => $action,
            'old_data'          => null,
            'new_data'          => json_encode([
                'description' => $description,
                'event_type' => 'authentication'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'           => $userId ?? $userInfo['user_id'],
            'username'          => $userInfo['username'],
            'user_type'         => $userInfo['user_type'],
            'user_full_name'    => $userInfo['user_full_name'],
            'organization_id'   => $userInfo['organization_id'],
            'organization_name' => $userInfo['organization_name'],
            'organization_type' => $userInfo['organization_type'],
            'project_id'        => $projectInfo['project_id'],
            'project_title'     => $projectInfo['project_title'],
            'portal'            => $portalContext['portal'],
            'module'            => 'authentication',
            'ip_address'        => $request->getIPAddress(),
            'user_agent'        => is_cli() ? 'CLI' : $request->getUserAgent()->getAgentString(),
            'session_id'        => $session->session_id ?? null,
            'request_url'       => current_url(),
            'description'       => $description,
            'created_at'        => date('Y-m-d H:i:s'),
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log system events manually
     */
    public function logSystemEvent(string $tableName, string $action, string $description, ?string $portal = null): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $portal ? ['portal' => $portal, 'module' => 'system'] : $this->detectPortalContext();

        if (!$portalContext) {
            $portalContext = ['portal' => 'system', 'module' => 'system'];
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        $data = [
            'table_name'  => $tableName,
            'primary_key' => 'system',
            'action'      => $action,
            'old_data'    => null,
            'new_data'    => json_encode([
                'description' => $description,
                'event_type' => 'system'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'     => $userInfo['user_id'],
            'username'    => $userInfo['username'],
            'user_type'   => $userInfo['user_type'],
            'portal'      => $portalContext['portal'],
            'module'      => $portalContext['module'],
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => is_cli() ? 'CLI' : $request->getUserAgent()->getAgentString(),
            'session_id'  => $session->session_id ?? null,
            'request_url' => current_url(),
            'description' => $description,
            'created_at'  => date('Y-m-d H:i:s'),
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log portal access events
     */
    public function logPortalAccess(string $page, string $action = 'access'): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $this->detectPortalContext();

        if (!$portalContext) {
            return false; // No valid portal context
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        $data = [
            'table_name'  => $portalContext['portal'] . '_portal_access',
            'primary_key' => $page,
            'action'      => $action,
            'old_data'    => null,
            'new_data'    => json_encode([
                'page' => $page,
                'url' => current_url(),
                'event_type' => 'portal_access'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'     => $userInfo['user_id'],
            'username'    => $userInfo['username'],
            'user_type'   => $userInfo['user_type'],
            'portal'      => $portalContext['portal'],
            'module'      => $portalContext['module'] ?? 'access',
            'ip_address'  => $request->getIPAddress(),
            'user_agent'  => is_cli() ? 'CLI' : $request->getUserAgent()->getAgentString(),
            'session_id'  => $session->session_id ?? null,
            'request_url' => current_url(),
            'description' => "User accessed {$page} page",
            'created_at'  => date('Y-m-d H:i:s'),
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log organization-specific events
     */
    public function logOrganizationEvent(int $organizationId, string $action, string $description, ?string $tableName = null): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $this->detectPortalContext();

        if (!$portalContext) {
            $portalContext = ['portal' => 'system', 'module' => 'organization'];
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        // Get organization details
        $organizationModel = new \App\Models\OrganizationModel();
        $organization = $organizationModel->find($organizationId);

        $data = [
            'table_name'        => $tableName ?? 'organizations',
            'primary_key'       => (string) $organizationId,
            'action'            => $action,
            'old_data'          => null,
            'new_data'          => json_encode([
                'description' => $description,
                'event_type' => 'organization'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'           => $userInfo['user_id'],
            'username'          => $userInfo['username'],
            'user_type'         => $userInfo['user_type'],
            'user_full_name'    => $userInfo['user_full_name'],
            'organization_id'   => $organizationId,
            'organization_name' => $organization['name'] ?? 'Unknown Organization',
            'organization_type' => $organization['type'] ?? null,
            'project_id'        => null,
            'project_title'     => null,
            'portal'            => $portalContext['portal'],
            'module'            => $portalContext['module'] ?? 'organization',
            'ip_address'        => $request->getIPAddress(),
            'user_agent'        => is_cli() ? 'CLI' : $request->getUserAgent()->getAgentString(),
            'session_id'        => $session->session_id ?? null,
            'request_url'       => current_url(),
            'description'       => $description,
            'created_at'        => date('Y-m-d H:i:s'),
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Log project-specific events
     */
    public function logProjectEvent(int $projectId, string $action, string $description, ?string $tableName = null): bool
    {
        $request = Services::request();
        $session = session();
        $portalContext = $this->detectPortalContext();

        if (!$portalContext) {
            $portalContext = ['portal' => 'system', 'module' => 'project'];
        }

        $userInfo = $this->getUserInfo($portalContext, $session);

        // Get project details
        $projectModel = new \App\Models\ProjectModel();
        $project = $projectModel->find($projectId);

        $data = [
            'table_name'        => $tableName ?? 'projects',
            'primary_key'       => (string) $projectId,
            'action'            => $action,
            'old_data'          => null,
            'new_data'          => json_encode([
                'description' => $description,
                'event_type' => 'project'
            ], JSON_UNESCAPED_UNICODE),
            'user_id'           => $userInfo['user_id'],
            'username'          => $userInfo['username'],
            'user_type'         => $userInfo['user_type'],
            'user_full_name'    => $userInfo['user_full_name'],
            'organization_id'   => $userInfo['organization_id'],
            'organization_name' => $userInfo['organization_name'],
            'organization_type' => $userInfo['organization_type'],
            'project_id'        => $projectId,
            'project_title'     => $project['title'] ?? 'Unknown Project',
            'portal'            => $portalContext['portal'],
            'module'            => $portalContext['module'] ?? 'project',
            'ip_address'        => $request->getIPAddress(),
            'user_agent'        => is_cli() ? 'CLI' : $request->getUserAgent()->getAgentString(),
            'session_id'        => $session->session_id ?? null,
            'request_url'       => current_url(),
            'description'       => $description,
            'created_at'        => date('Y-m-d H:i:s'),
        ];

        return $this->auditLogModel->insert($data);
    }

    /**
     * Detect portal context (same as in Auditable trait)
     */
    protected function detectPortalContext(): ?array
    {
        $session = session();
        $request = Services::request();
        $uri = $request->getUri()->getPath();

        // Check for Dakoii portal
        if ($session->get('dakoii_user_id') || strpos($uri, '/dakoii/') !== false) {
            return [
                'portal' => 'dakoii',
                'module' => $this->extractModuleFromUrl($uri, 'dakoii')
            ];
        }

        // Check for Admin portal
        if ($session->get('admin_user_id') || strpos($uri, '/admin/') !== false) {
            return [
                'portal' => 'admin',
                'module' => $this->extractModuleFromUrl($uri, 'admin')
            ];
        }

        // Check for Monitor portal
        if ($session->get('monitor_user_id') || strpos($uri, '/monitor/') !== false) {
            return [
                'portal' => 'monitor',
                'module' => $this->extractModuleFromUrl($uri, 'monitor')
            ];
        }

        return null;
    }

    /**
     * Get user information based on portal context (same as in Auditable trait)
     */
    protected function getUserInfo(array $portalContext, $session): array
    {
        switch ($portalContext['portal']) {
            case 'dakoii':
                return [
                    'user_id' => $session->get('dakoii_user_id'),
                    'username' => $session->get('dakoii_username') ?? 'system',
                    'user_type' => 'dakoii_user',
                    'user_full_name' => $session->get('dakoii_user_full_name'),
                    'organization_id' => null, // Dakoii users are system admins, not tied to specific organizations
                    'organization_name' => 'Dakoii System Administration',
                    'organization_type' => 'System',
                ];
            case 'admin':
                return [
                    'user_id' => $session->get('admin_user_id'),
                    'username' => $session->get('admin_username') ?? 'system',
                    'user_type' => 'admin_user',
                    'user_full_name' => $session->get('admin_user_full_name'),
                    'organization_id' => $session->get('admin_organization_id'),
                    'organization_name' => $session->get('admin_organization_name'),
                    'organization_type' => $session->get('admin_organization_type'),
                ];
            case 'monitor':
                return [
                    'user_id' => $session->get('monitor_user_id'),
                    'username' => $session->get('monitor_username') ?? 'system',
                    'user_type' => 'project_officer',
                    'user_full_name' => $session->get('monitor_user_full_name'),
                    'organization_id' => $session->get('monitor_organization_id'),
                    'organization_name' => $session->get('monitor_organization_name'),
                    'organization_type' => $session->get('monitor_organization_type'),
                ];
            default:
                return [
                    'user_id' => null,
                    'username' => 'system',
                    'user_type' => 'system',
                    'user_full_name' => null,
                    'organization_id' => null,
                    'organization_name' => null,
                    'organization_type' => null,
                ];
        }
    }

    /**
     * Get project context if available
     */
    protected function getProjectContext($session): array
    {
        return [
            'project_id' => $session->get('current_project_id'),
            'project_title' => $session->get('current_project_title'),
        ];
    }

    /**
     * Extract module name from URL
     */
    protected function extractModuleFromUrl(string $uri, string $portal): ?string
    {
        $pattern = "/{$portal}\/([^\/]+)/";
        if (preg_match($pattern, $uri, $matches)) {
            return $matches[1];
        }
        return null;
    }
}
