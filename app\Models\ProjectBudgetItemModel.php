<?php

namespace App\Models;

/**
 * Project Budget Item Model
 * 
 * Handles project budget items with planned amounts and status tracking.
 */
class ProjectBudgetItemModel extends BaseModel
{
    protected $table      = 'project_budget_items';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'item_code', 'description', 'amount_planned', 'status',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'item_code'      => 'required|max_length[30]',
        'description'    => 'required|max_length[255]',
        'amount_planned' => 'required|decimal',
        'status'         => 'in_list[active,removed]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'item_code' => [
            'required' => 'Item code is required'
        ],
        'description' => [
            'required' => 'Description is required'
        ],
        'amount_planned' => [
            'required' => 'Planned amount is required'
        ]
    ];
    
    /**
     * Get budget items by project
     */
    public function getByProject(int $projectId, bool $activeOnly = true): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($activeOnly) {
            $query = $query->where('status', 'active');
        }
        
        return $query->orderBy('item_code', 'ASC')->findAll();
    }
    
    /**
     * Get active budget items
     */
    public function getActiveBudgetItems(): array
    {
        return $this->where('status', 'active')
                   ->orderBy('project_id', 'ASC')
                   ->orderBy('item_code', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get project total budget
     */
    public function getProjectTotalBudget(int $projectId): float
    {
        $result = $this->selectSum('amount_planned')
                      ->where('project_id', $projectId)
                      ->where('status', 'active')
                      ->first();
        
        return (float) ($result['amount_planned'] ?? 0);
    }
    
    /**
     * Update budget item status
     */
    public function updateStatus(int $itemId, string $status, ?int $updatedBy = null): bool
    {
        return $this->update($itemId, [
            'status' => $status,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Get budget statistics
     */
    public function getBudgetStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total planned amount
        $totalResult = $query->selectSum('amount_planned')
                            ->where('status', 'active')
                            ->first();
        
        $stats['total_planned'] = (float) ($totalResult['amount_planned'] ?? 0);
        
        // Count by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Total items
        $totalQuery = $this;
        if ($projectId) {
            $totalQuery = $totalQuery->where('project_id', $projectId);
        }
        $stats['total_items'] = $totalQuery->countAllResults();
        
        return $stats;
    }
    
    /**
     * Check if item code exists for project
     */
    public function itemCodeExists(int $projectId, string $itemCode, ?int $excludeId = null): bool
    {
        $query = $this->where('project_id', $projectId)
                     ->where('item_code', $itemCode);
        
        if ($excludeId) {
            $query = $query->where('id !=', $excludeId);
        }
        
        return $query->countAllResults() > 0;
    }
}
