<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectRiskModel;
use App\Models\ProjectMilestoneModel;

/**
 * Admin Project Risks Controller
 * 
 * Handles CRUD operations for project risks in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectRisksController extends BaseController
{
    protected $projectModel;
    protected $projectRiskModel;
    protected $projectMilestoneModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectRiskModel = new ProjectRiskModel();
        $this->projectMilestoneModel = new ProjectMilestoneModel();
    }

    /**
     * List project risks - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project risks
        $risks = $this->projectRiskModel->getByProject($projectId);

        $data = [
            'title' => 'Project Risks - PROMIS Admin',
            'page_title' => 'Project Risks',
            'project' => $project,
            'risks' => $risks
        ];

        return view('admin/admin_projects_risks_list', $data);
    }

    /**
     * Show create risk form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project milestones for dropdown
        $milestones = $this->projectMilestoneModel->where('project_id', $projectId)
                                                 ->where('deleted_at', null)
                                                 ->orderBy('title', 'ASC')
                                                 ->findAll();

        $data = [
            'title' => 'Create Risk - PROMIS Admin',
            'page_title' => 'Create Risk',
            'project' => $project,
            'milestones' => $milestones
        ];

        return view('admin/admin_projects_risks_create', $data);
    }

    /**
     * Store new risk - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'description' => 'required',
            'risk_type' => 'required|in_list[proposed,foreseen,witnessed]',
            'risk_level' => 'required|in_list[low,medium,high,critical]',
            'mitigation' => 'permit_empty',
            'milestone_id' => 'permit_empty|integer',
            'approval_status' => 'required|in_list[pending,approved,rejected]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for insertion
        $riskData = [
            'project_id' => $projectId,
            'description' => $this->request->getPost('description'),
            'risk_type' => $this->request->getPost('risk_type'),
            'risk_level' => $this->request->getPost('risk_level'),
            'mitigation' => $this->request->getPost('mitigation'),
            'milestone_id' => $this->request->getPost('milestone_id') ?: null,
            'approval_status' => $this->request->getPost('approval_status'),
            'created_by' => $adminUserId,
            'updated_by' => $adminUserId
        ];

        try {
            $riskId = $this->projectRiskModel->insert($riskData);

            if ($riskId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/risks'))
                               ->with('success', 'Risk created successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectRiskModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating risk: ' . $e->getMessage());
        }
    }

    /**
     * Show edit risk form - GET request
     */
    public function edit($projectId, $riskId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get risk
        $risk = $this->projectRiskModel->where('id', $riskId)
                                      ->where('project_id', $projectId)
                                      ->where('deleted_at', null)
                                      ->first();

        if (!$risk) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/risks'))
                           ->with('error', 'Risk not found.');
        }

        // Get project milestones for dropdown
        $milestones = $this->projectMilestoneModel->where('project_id', $projectId)
                                                 ->where('deleted_at', null)
                                                 ->orderBy('title', 'ASC')
                                                 ->findAll();

        $data = [
            'title' => 'Edit Risk - PROMIS Admin',
            'page_title' => 'Edit Risk',
            'project' => $project,
            'risk' => $risk,
            'milestones' => $milestones
        ];

        return view('admin/admin_projects_risks_edit', $data);
    }

    /**
     * Update risk - POST request
     */
    public function update($projectId, $riskId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get risk
        $risk = $this->projectRiskModel->where('id', $riskId)
                                      ->where('project_id', $projectId)
                                      ->where('deleted_at', null)
                                      ->first();

        if (!$risk) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/risks'))
                           ->with('error', 'Risk not found.');
        }

        // Validation rules
        $rules = [
            'description' => 'required',
            'risk_type' => 'required|in_list[proposed,foreseen,witnessed]',
            'risk_level' => 'required|in_list[low,medium,high,critical]',
            'mitigation' => 'permit_empty',
            'milestone_id' => 'permit_empty|integer',
            'approval_status' => 'required|in_list[pending,approved,rejected]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data for update
        $riskData = [
            'description' => $this->request->getPost('description'),
            'risk_type' => $this->request->getPost('risk_type'),
            'risk_level' => $this->request->getPost('risk_level'),
            'mitigation' => $this->request->getPost('mitigation'),
            'milestone_id' => $this->request->getPost('milestone_id') ?: null,
            'approval_status' => $this->request->getPost('approval_status'),
            'updated_by' => $adminUserId
        ];

        try {
            $updated = $this->projectRiskModel->update($riskId, $riskData);

            if ($updated) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/risks'))
                               ->with('success', 'Risk updated successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectRiskModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating risk: ' . $e->getMessage());
        }
    }

    /**
     * Delete risk - POST request
     */
    public function delete($projectId, $riskId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get risk
        $risk = $this->projectRiskModel->where('id', $riskId)
                                      ->where('project_id', $projectId)
                                      ->where('deleted_at', null)
                                      ->first();

        if (!$risk) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/risks'))
                           ->with('error', 'Risk not found.');
        }

        try {
            // Soft delete the risk
            $deleted = $this->projectRiskModel->update($riskId, [
                'deleted_by' => $adminUserId,
                'deleted_at' => date('Y-m-d H:i:s')
            ]);

            if ($deleted) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/risks'))
                               ->with('success', 'Risk deleted successfully!');
            } else {
                return redirect()->back()->with('error', 'Error deleting risk.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting risk: ' . $e->getMessage());
        }
    }
}
