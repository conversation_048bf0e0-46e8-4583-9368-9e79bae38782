<?php

namespace App\Models;

/**
 * Contractor Compliance Model
 * 
 * Manages contractor compliance tracking including
 * compliance types, status monitoring, and check scheduling.
 */
class ContractorComplianceModel extends BaseModel
{
    protected $table      = 'contractor_compliance';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'contractor_id', 'compliance_type', 'status', 'last_check_date',
        'next_check_date', 'notes', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'contractor_id'     => 'required|integer',
        'compliance_type'   => 'required|max_length[100]',
        'status'            => 'in_list[compliant,warning,non-compliant]',
        'last_check_date'   => 'required|valid_date',
        'next_check_date'   => 'valid_date'
    ];
    
    protected $validationMessages = [
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'compliance_type' => [
            'required' => 'Compliance type is required'
        ],
        'last_check_date' => [
            'required' => 'Last check date is required'
        ]
    ];
    
    /**
     * Get compliance records by contractor
     */
    public function getByContractor(int $contractorId): array
    {
        return $this->where('contractor_id', $contractorId)
                   ->orderBy('status', 'ASC') // non-compliant first
                   ->orderBy('next_check_date', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get compliance records by status
     */
    public function getByStatus(string $status, ?int $orgId = null): array
    {
        $query = $this->select('contractor_compliance.*, contractors.name as contractor_name, contractors.contractor_code')
                     ->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                     ->where('contractor_compliance.status', $status);
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractor_compliance.next_check_date', 'ASC')
                    ->orderBy('contractors.name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Get overdue compliance checks
     */
    public function getOverdueChecks(?int $orgId = null): array
    {
        $query = $this->select('contractor_compliance.*, contractors.name as contractor_name, contractors.contractor_code')
                     ->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                     ->where('contractor_compliance.next_check_date IS NOT NULL')
                     ->where('contractor_compliance.next_check_date <', date('Y-m-d'));
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractor_compliance.next_check_date', 'ASC')
                    ->orderBy('contractors.name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Get upcoming compliance checks
     */
    public function getUpcomingChecks(int $daysAhead = 30, ?int $orgId = null): array
    {
        $futureDate = date('Y-m-d', strtotime("+{$daysAhead} days"));
        
        $query = $this->select('contractor_compliance.*, contractors.name as contractor_name, contractors.contractor_code')
                     ->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                     ->where('contractor_compliance.next_check_date IS NOT NULL')
                     ->where('contractor_compliance.next_check_date >=', date('Y-m-d'))
                     ->where('contractor_compliance.next_check_date <=', $futureDate);
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractor_compliance.next_check_date', 'ASC')
                    ->orderBy('contractors.name', 'ASC')
                    ->findAll();
    }
    
    /**
     * Update compliance status
     */
    public function updateCompliance(int $id, string $status, ?string $notes = null, ?string $nextCheckDate = null): bool
    {
        $data = [
            'status' => $status,
            'last_check_date' => date('Y-m-d'),
            'notes' => $notes
        ];
        
        if ($nextCheckDate) {
            $data['next_check_date'] = $nextCheckDate;
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * Get compliance statistics by organization
     */
    public function getComplianceStatistics(int $orgId): array
    {
        $stats = [];
        
        // Total compliance records
        $stats['total'] = $this->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->countAllResults();
        
        // By status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                            ->where('contractors.org_id', $orgId)
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Overdue checks
        $stats['overdue'] = count($this->getOverdueChecks($orgId));
        
        // Upcoming checks (next 30 days)
        $stats['upcoming'] = count($this->getUpcomingChecks(30, $orgId));
        
        // By compliance type
        $typeCounts = $this->select('compliance_type, COUNT(*) as count')
                          ->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                          ->where('contractors.org_id', $orgId)
                          ->groupBy('compliance_type')
                          ->orderBy('count', 'DESC')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'compliance_type');
        
        return $stats;
    }
    
    /**
     * Get compliance types for organization
     */
    public function getComplianceTypes(int $orgId): array
    {
        return $this->select('DISTINCT compliance_type')
                   ->join('contractors', 'contractors.id = contractor_compliance.contractor_id')
                   ->where('contractors.org_id', $orgId)
                   ->orderBy('compliance_type', 'ASC')
                   ->findAll();
    }
    
    /**
     * Check contractor compliance status
     */
    public function getContractorComplianceStatus(int $contractorId): array
    {
        $records = $this->getByContractor($contractorId);
        
        $status = [
            'overall_status' => 'compliant',
            'total_checks' => count($records),
            'compliant' => 0,
            'warning' => 0,
            'non_compliant' => 0,
            'overdue_checks' => 0,
            'details' => $records
        ];
        
        foreach ($records as $record) {
            $status[$record['status']]++;
            
            // Check for overdue
            if ($record['next_check_date'] && $record['next_check_date'] < date('Y-m-d')) {
                $status['overdue_checks']++;
            }
            
            // Determine overall status (worst case)
            if ($record['status'] === 'non-compliant') {
                $status['overall_status'] = 'non-compliant';
            } elseif ($record['status'] === 'warning' && $status['overall_status'] !== 'non-compliant') {
                $status['overall_status'] = 'warning';
            }
        }
        
        return $status;
    }
    
    /**
     * Schedule next compliance check
     */
    public function scheduleNextCheck(int $contractorId, string $complianceType, string $nextCheckDate): bool
    {
        $existing = $this->where('contractor_id', $contractorId)
                        ->where('compliance_type', $complianceType)
                        ->first();
        
        if ($existing) {
            return $this->update($existing['id'], ['next_check_date' => $nextCheckDate]);
        } else {
            $data = [
                'contractor_id' => $contractorId,
                'compliance_type' => $complianceType,
                'status' => 'compliant',
                'last_check_date' => date('Y-m-d'),
                'next_check_date' => $nextCheckDate
            ];
            
            return $this->insert($data) !== false;
        }
    }
    
    /**
     * Get compliance dashboard data
     */
    public function getComplianceDashboard(int $orgId): array
    {
        return [
            'statistics' => $this->getComplianceStatistics($orgId),
            'overdue' => $this->getOverdueChecks($orgId),
            'upcoming' => $this->getUpcomingChecks(7, $orgId), // Next 7 days
            'non_compliant' => $this->getByStatus('non-compliant', $orgId),
            'warnings' => $this->getByStatus('warning', $orgId)
        ];
    }
}
