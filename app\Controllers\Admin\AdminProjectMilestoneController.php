<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectPhaseModel;
use App\Models\ProjectMilestoneModel;

/**
 * Admin Project Milestone Controller
 * 
 * Handles CRUD operations for project milestones in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectMilestoneController extends BaseController
{
    protected $projectModel;
    protected $projectPhaseModel;
    protected $projectMilestoneModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectPhaseModel = new ProjectPhaseModel();
        $this->projectMilestoneModel = new ProjectMilestoneModel();
    }

    /**
     * Show create milestone form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get active phases for the project
        $phases = $this->projectPhaseModel->getByProject($projectId, true);

        if (empty($phases)) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'No active phases found. Please create a phase first.');
        }

        $data = [
            'title' => 'Create New Milestone - PROMIS Admin',
            'page_title' => 'Create New Milestone',
            'project' => $project,
            'phases' => $phases
        ];

        return view('admin/admin_projects_milestones_create', $data);
    }

    /**
     * Store new milestone - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'phase_id' => 'required|integer',
            'milestone_code' => 'required|max_length[20]',
            'title' => 'required|max_length[200]',
            'description' => 'permit_empty',
            'target_date' => 'permit_empty|valid_date',
            'status' => 'required|in_list[pending,in-progress,completed,approved]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Verify phase belongs to project
        $phaseId = $this->request->getPost('phase_id');
        $phase = $this->projectPhaseModel->where('id', $phaseId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$phase) {
            return redirect()->back()->withInput()->with('error', 'Invalid phase selected.');
        }

        // Prepare data for insertion
        $milestoneData = [
            'project_id' => $projectId,
            'phase_id' => $phaseId,
            'milestone_code' => $this->request->getPost('milestone_code'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description') ?: '',
            'target_date' => $this->request->getPost('target_date') ?: null,
            'status' => $this->request->getPost('status'),
            'evidence_count' => 0,
            'created_by' => $adminUserId
        ];

        try {
            $milestoneId = $this->projectMilestoneModel->insert($milestoneData);

            if ($milestoneId) {
                return redirect()->to(base_url('admin/projects/' . $projectId))
                               ->with('success', 'Milestone created successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectMilestoneModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating milestone: ' . $e->getMessage());
        }
    }

    /**
     * Show edit milestone form - GET request
     */
    public function edit($projectId, $milestoneId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get milestone
        $milestone = $this->projectMilestoneModel->where('id', $milestoneId)
                                                ->where('project_id', $projectId)
                                                ->where('deleted_at', null)
                                                ->first();

        if (!$milestone) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Milestone not found.');
        }

        // Get active phases for the project
        $phases = $this->projectPhaseModel->getByProject($projectId, true);

        $data = [
            'title' => 'Edit Milestone - PROMIS Admin',
            'page_title' => 'Edit Milestone',
            'project' => $project,
            'milestone' => $milestone,
            'phases' => $phases
        ];

        return view('admin/admin_projects_milestones_edit', $data);
    }

    /**
     * Update milestone - POST request
     */
    public function update($projectId, $milestoneId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get milestone
        $milestone = $this->projectMilestoneModel->where('id', $milestoneId)
                                                ->where('project_id', $projectId)
                                                ->where('deleted_at', null)
                                                ->first();

        if (!$milestone) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Milestone not found.');
        }

        // Validation rules
        $rules = [
            'phase_id' => 'required|integer',
            'milestone_code' => 'required|max_length[20]',
            'title' => 'required|max_length[200]',
            'description' => 'permit_empty',
            'target_date' => 'permit_empty|valid_date',
            'status' => 'required|in_list[pending,in-progress,completed,approved]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Verify phase belongs to project
        $phaseId = $this->request->getPost('phase_id');
        $phase = $this->projectPhaseModel->where('id', $phaseId)
                                        ->where('project_id', $projectId)
                                        ->where('deleted_at', null)
                                        ->first();

        if (!$phase) {
            return redirect()->back()->withInput()->with('error', 'Invalid phase selected.');
        }

        // Prepare data for update
        $updateData = [
            'phase_id' => $phaseId,
            'milestone_code' => $this->request->getPost('milestone_code'),
            'title' => $this->request->getPost('title'),
            'description' => $this->request->getPost('description') ?: '',
            'target_date' => $this->request->getPost('target_date') ?: null,
            'status' => $this->request->getPost('status'),
            'updated_by' => $adminUserId
        ];

        // Set completion date if status is completed or approved
        $status = $this->request->getPost('status');
        if (in_array($status, ['completed', 'approved']) && !in_array($milestone['status'], ['completed', 'approved'])) {
            $updateData['completion_date'] = date('Y-m-d');
        } elseif (!in_array($status, ['completed', 'approved'])) {
            $updateData['completion_date'] = null;
        }

        try {
            $updated = $this->projectMilestoneModel->update($milestoneId, $updateData);

            if ($updated) {
                return redirect()->to(base_url('admin/projects/' . $projectId))
                               ->with('success', 'Milestone updated successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectMilestoneModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating milestone: ' . $e->getMessage());
        }
    }

    /**
     * Delete milestone - POST request
     */
    public function delete($projectId, $milestoneId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get milestone
        $milestone = $this->projectMilestoneModel->where('id', $milestoneId)
                                                ->where('project_id', $projectId)
                                                ->where('deleted_at', null)
                                                ->first();

        if (!$milestone) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Milestone not found.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectMilestoneModel->update($milestoneId, ['deleted_by' => $adminUserId]);
            $this->projectMilestoneModel->delete($milestoneId);

            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('success', 'Milestone deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Error deleting milestone: ' . $e->getMessage());
        }
    }
}
