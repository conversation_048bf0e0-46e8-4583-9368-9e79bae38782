<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectDocumentModel;

/**
 * Admin Project Documents Controller
 * 
 * Handles CRUD operations for project documents in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectDocumentsController extends BaseController
{
    protected $projectModel;
    protected $projectDocumentModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectDocumentModel = new ProjectDocumentModel();
    }

    /**
     * Show documents list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get filters from query parameters
        $filters = [
            'category' => $this->request->getGet('category'),
            'search' => $this->request->getGet('search')
        ];

        // Get documents for this project
        $documents = $this->getDocumentsWithFilters($projectId, $filters);
        
        // Get document statistics
        $documentStats = $this->projectDocumentModel->getDocumentStatistics($projectId);

        $data = [
            'title' => 'Project Documents - PROMIS Admin',
            'page_title' => 'Project Documents',
            'project' => $project,
            'documents' => $documents,
            'documentStats' => $documentStats,
            'filters' => $filters,
            'categories' => $this->getDocumentCategories()
        ];

        return view('admin/project_documents_list', $data);
    }

    /**
     * Show create document form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Upload Project Document - PROMIS Admin',
            'page_title' => 'Upload Project Document',
            'project' => $project,
            'categories' => $this->getDocumentCategories()
        ];

        return view('admin/project_documents_create', $data);
    }

    /**
     * Store new document - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'description' => 'permit_empty|max_length[255]',
            'category' => 'permit_empty|max_length[50]',
            'document_file' => 'uploaded[document_file]|max_size[document_file,25600]' // 25MB
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $file = $this->request->getFile('document_file');
        
        if (!$file->isValid()) {
            return redirect()->back()->withInput()->with('error', 'File upload failed: ' . $file->getErrorString());
        }

        // Validate file type (exclude dangerous files)
        $allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg', 'zip', 'rar', '7z', 'tar', 'gz', 'csv', 'xml', 'json'];
        $fileExtension = strtolower($file->getClientExtension());
        
        if (!in_array($fileExtension, $allowedExtensions)) {
            return redirect()->back()->withInput()->with('error', 'File type not allowed. Executable files (.exe, .bat) and application files are not permitted.');
        }

        // Generate unique filename
        $fileName = $file->getRandomName();
        $uploadPath = 'public/uploads/project_documents/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Move file to upload directory
        if (!$file->move($uploadPath, $fileName)) {
            return redirect()->back()->withInput()->with('error', 'Failed to upload file.');
        }

        // Prepare document data
        $documentData = [
            'project_id' => $projectId,
            'doc_type' => $this->request->getPost('category'),
            'description' => $this->request->getPost('description'),
            'doc_path' => 'public/uploads/project_documents/' . $fileName,
            'version_no' => 1,
            'created_by' => session()->get('admin_user_id')
        ];

        try {
            $documentId = $this->projectDocumentModel->insert($documentData);

            if ($documentId) {
                // Log the action
                $this->logAuditEvent('create', session()->get('admin_user_id'), 'project_documents', $documentId, [
                    'description' => 'Project document uploaded: ' . $file->getClientName(),
                    'project_id' => $projectId,
                    'file_path' => $documentData['doc_path']
                ]);

                return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                               ->with('success', 'Document uploaded successfully!');
            } else {
                // Delete uploaded file if database insert failed
                if (file_exists($uploadPath . $fileName)) {
                    unlink($uploadPath . $fileName);
                }
                return redirect()->back()->withInput()->with('errors', $this->projectDocumentModel->errors());
            }
        } catch (\Exception $e) {
            // Delete uploaded file if exception occurred
            if (file_exists($uploadPath . $fileName)) {
                unlink($uploadPath . $fileName);
            }
            return redirect()->back()->withInput()->with('error', 'Error uploading document: ' . $e->getMessage());
        }
    }

    /**
     * Show edit document form - GET request
     */
    public function edit($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->where('deleted_at', null)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        $data = [
            'title' => 'Edit Project Document - PROMIS Admin',
            'page_title' => 'Edit Project Document',
            'project' => $project,
            'document' => $document,
            'categories' => $this->getDocumentCategories()
        ];

        return view('admin/project_documents_edit', $data);
    }

    /**
     * Update document - POST request
     */
    public function update($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->where('deleted_at', null)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        // Validation rules
        $rules = [
            'description' => 'permit_empty|max_length[255]',
            'category' => 'permit_empty|max_length[50]'
        ];

        // Add file validation if new file is uploaded
        if ($this->request->getFile('document_file') && $this->request->getFile('document_file')->isValid()) {
            $rules['document_file'] = 'max_size[document_file,25600]'; // 25MB
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare update data
        $updateData = [
            'doc_type' => $this->request->getPost('category'),
            'description' => $this->request->getPost('description')
        ];

        // Handle file replacement if new file is uploaded
        $file = $this->request->getFile('document_file');
        if ($file && $file->isValid()) {
            // Validate file type
            $allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'svg', 'zip', 'rar', '7z', 'tar', 'gz', 'csv', 'xml', 'json'];
            $fileExtension = strtolower($file->getClientExtension());
            
            if (!in_array($fileExtension, $allowedExtensions)) {
                return redirect()->back()->withInput()->with('error', 'File type not allowed. Executable files (.exe, .bat) and application files are not permitted.');
            }

            // Generate unique filename
            $fileName = $file->getRandomName();
            $uploadPath = 'public/uploads/project_documents/';
            
            // Create directory if it doesn't exist
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move new file
            if ($file->move($uploadPath, $fileName)) {
                // Delete old file
                if (file_exists($document['doc_path'])) {
                    unlink($document['doc_path']);
                }

                // Update file information
                $updateData['doc_path'] = 'public/uploads/project_documents/' . $fileName;
                $updateData['version_no'] = ($document['version_no'] ?? 0) + 1;
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to upload new file.');
            }
        }

        try {
            $result = $this->projectDocumentModel->update($documentId, $updateData);

            if ($result) {
                // Log the action
                $this->logAuditEvent('update', session()->get('admin_user_id'), 'project_documents', $documentId, [
                    'description' => 'Project document updated',
                    'project_id' => $projectId,
                    'changes' => $updateData
                ]);

                return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                               ->with('success', 'Document updated successfully!');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectDocumentModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating document: ' . $e->getMessage());
        }
    }

    /**
     * Delete document - POST request
     */
    public function delete($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->where('deleted_at', null)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        try {
            $result = $this->projectDocumentModel->deleteDocument($documentId, session()->get('admin_user_id'));

            if ($result) {
                // Log the action
                $this->logAuditEvent('delete', session()->get('admin_user_id'), 'project_documents', $documentId, [
                    'description' => 'Project document deleted',
                    'project_id' => $projectId,
                    'doc_path' => $document['doc_path']
                ]);

                return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                               ->with('success', 'Document deleted successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to delete document.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting document: ' . $e->getMessage());
        }
    }

    /**
     * Download document - GET request
     */
    public function download($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->where('deleted_at', null)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        // Check if file exists
        if (!file_exists($document['doc_path'])) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'File not found on server.');
        }

        // Log the download action
        $this->logAuditEvent('download', session()->get('admin_user_id'), 'project_documents', $documentId, [
            'description' => 'Project document downloaded',
            'project_id' => $projectId,
            'doc_path' => $document['doc_path']
        ]);

        // Force download
        return $this->response->download($document['doc_path'], null, true);
    }

    /**
     * Get documents with filters
     */
    private function getDocumentsWithFilters($projectId, $filters)
    {
        $query = $this->projectDocumentModel->where('project_id', $projectId);

        // Apply category filter
        if (!empty($filters['category'])) {
            $query = $query->where('doc_type', $filters['category']);
        }

        // Apply search filter
        if (!empty($filters['search'])) {
            $query = $query->groupStart()
                          ->like('description', $filters['search'])
                          ->orLike('doc_path', $filters['search'])
                          ->orLike('doc_type', $filters['search'])
                          ->groupEnd();
        }

        return $query->orderBy('created_at', 'DESC')->findAll();
    }

    /**
     * Get document categories
     */
    private function getDocumentCategories()
    {
        return [
            'designs' => 'Designs',
            'feasibility_studies' => 'Feasibility Studies',
            'reports' => 'Reports',
            'contracts' => 'Contracts',
            'permits' => 'Permits',
            'other' => 'Other'
        ];
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $recordId, $additionalData = [])
    {
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => $recordId,
            'action' => $action,
            'new_data' => json_encode($additionalData),
            'user_id' => $userId,
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $db->table('audit_logs')->insert($auditData);
    }
}
