<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Tree Style Hierarchy Chart</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: calc(100vh - 40px);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        h1 {
            color: #2c3e50;
            font-size: 2.2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85em;
            color: #2c3e50;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #tree-container {
            flex: 1;
            width: 100%;
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            background: #fafafa;
            border: 2px solid #e0e0e0;
        }

        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node-rect {
            stroke-width: 2;
            rx: 8;
            ry: 8;
            filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
        }

        .node:hover .node-rect {
            filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.2));
            transform: scale(1.05);
        }

        .node-text {
            font-family: 'Segoe UI', sans-serif;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: middle;
            pointer-events: none;
        }

        .node-subtitle {
            font-family: 'Segoe UI', sans-serif;
            font-weight: 400;
            text-anchor: middle;
            dominant-baseline: middle;
            font-size: 11px;
            opacity: 0.8;
            pointer-events: none;
        }

        .link {
            fill: none;
            stroke: #3498db;
            stroke-width: 2;
            stroke-opacity: 0.8;
            transition: all 0.3s ease;
        }

        .link:hover {
            stroke-width: 3;
            stroke-opacity: 1;
        }

        /* Node colors by type */
        .country-node {
            fill: #e74c3c;
        }

        .province-node {
            fill: #f39c12;
        }

        .district-node {
            fill: #27ae60;
        }

        .llg-node {
            fill: #9b59b6;
        }

        .country-text {
            fill: white;
            font-size: 14px;
            font-weight: bold;
        }

        .province-text {
            fill: white;
            font-size: 13px;
        }

        .district-text {
            fill: white;
            font-size: 12px;
        }

        .llg-text {
            fill: #2c3e50;
            font-size: 11px;
        }

        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #3498db;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #3498db;
            transition: all 0.3s ease;
        }

        .zoom-btn:hover {
            background: #3498db;
            color: white;
            transform: scale(1.1);
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .collapse-indicator {
            fill: rgba(255, 255, 255, 0.8);
            stroke: none;
            font-family: 'Segoe UI', sans-serif;
            font-size: 16px;
            font-weight: bold;
            text-anchor: middle;
            dominant-baseline: middle;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌳 Administrative Hierarchy Tree</h1>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>Country</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>Province</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #27ae60;"></div>
                    <span>District</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #9b59b6;"></div>
                    <span>LLG</span>
                </div>
            </div>

            <div class="controls">
                <button class="control-btn" onclick="expandAll()">Expand All</button>
                <button class="control-btn" onclick="collapseAll()">Collapse All</button>
                <button class="control-btn" onclick="centerTree()">Center View</button>
                <button class="control-btn" onclick="resetZoom()">Reset Zoom</button>
            </div>
        </div>

        <div id="tree-container">
            <div class="zoom-controls">
                <div class="zoom-btn" onclick="zoomIn()">+</div>
                <div class="zoom-btn" onclick="zoomOut()">−</div>
            </div>
        </div>

        <div class="tooltip" id="tooltip"></div>
    </div>

    <script>
        // Hierarchical data structure
        const treeData = {
            name: "Papua New Guinea",
            type: "country",
            children: [
                {
                    name: "Western Province",
                    type: "province",
                    children: [
                        {
                            name: "North Fly District",
                            type: "district",
                            children: [
                                { name: "Kiunga Urban LLG", type: "llg" },
                                { name: "Kiunga Rural LLG", type: "llg" },
                                { name: "Ningerum LLG", type: "llg" }
                            ]
                        },
                        {
                            name: "South Fly District", 
                            type: "district",
                            children: [
                                { name: "Daru Urban LLG", type: "llg" },
                                { name: "Daru Rural LLG", type: "llg" },
                                { name: "Morehead LLG", type: "llg" }
                            ]
                        }
                    ]
                },
                {
                    name: "Eastern Highlands",
                    type: "province", 
                    children: [
                        {
                            name: "Goroka District",
                            type: "district",
                            children: [
                                { name: "Goroka Urban LLG", type: "llg" },
                                { name: "Bena LLG", type: "llg" },
                                { name: "Asaro LLG", type: "llg" }
                            ]
                        },
                        {
                            name: "Henganofi District",
                            type: "district", 
                            children: [
                                { name: "Henganofi LLG", type: "llg" },
                                { name: "Lamari LLG", type: "llg" }
                            ]
                        }
                    ]
                },
                {
                    name: "Morobe Province",
                    type: "province",
                    children: [
                        {
                            name: "Lae District",
                            type: "district",
                            children: [
                                { name: "Lae Urban LLG", type: "llg" },
                                { name: "Ahi LLG", type: "llg" },
                                { name: "Bukawa LLG", type: "llg" }
                            ]
                        }
                    ]
                }
            ]
        };

        // Set up dimensions and margins
        const container = d3.select("#tree-container");
        const containerRect = container.node().getBoundingClientRect();
        const margin = { top: 40, right: 40, bottom: 40, left: 40 };
        const width = containerRect.width - margin.left - margin.right;
        const height = containerRect.height - margin.top - margin.bottom;

        // Create SVG
        const svg = container.append("svg")
            .attr("width", containerRect.width)
            .attr("height", containerRect.height);

        const g = svg.append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        // Create tree layout
        const tree = d3.tree()
            .size([width, height])
            .separation((a, b) => {
                return (a.parent === b.parent ? 1 : 1.2) * 
                       (Math.max(getNodeWidth(a), getNodeWidth(b)) / 100);
            });

        // Create zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on("zoom", (event) => {
                g.attr("transform", 
                    `translate(${margin.left + event.transform.x},${margin.top + event.transform.y}) scale(${event.transform.k})`
                );
            });

        svg.call(zoom);

        let root = d3.hierarchy(treeData);
        let currentTransform = d3.zoomIdentity;

        function getNodeWidth(d) {
            const lengths = {
                'country': 140,
                'province': 120, 
                'district': 110,
                'llg': 100
            };
            return lengths[d.data.type] || 100;
        }

        function getNodeHeight(d) {
            return 50;
        }

        function getNodeColor(type) {
            const colors = {
                'country': '#e74c3c',
                'province': '#f39c12',
                'district': '#27ae60', 
                'llg': '#9b59b6'
            };
            return colors[type] || '#3498db';
        }

        function getTextColor(type) {
            return type === 'llg' ? '#2c3e50' : 'white';
        }

        function update(source) {
            const treeData = tree(root);
            const nodes = treeData.descendants();
            const links = treeData.descendants().slice(1);

            // Update nodes
            const node = g.selectAll(".node")
                .data(nodes, d => d.id || (d.id = ++i));

            const nodeEnter = node.enter().append("g")
                .attr("class", "node")
                .attr("transform", d => `translate(${source.x0 || 0},${source.y0 || 0})`)
                .on("click", click)
                .on("mouseover", showTooltip)
                .on("mouseout", hideTooltip);

            // Add rectangles for nodes
            nodeEnter.append("rect")
                .attr("class", d => `node-rect ${d.data.type}-node`)
                .attr("width", 0)
                .attr("height", 0)
                .attr("x", 0)
                .attr("y", 0)
                .style("fill", d => getNodeColor(d.data.type));

            // Add main text
            nodeEnter.append("text")
                .attr("class", d => `node-text ${d.data.type}-text`)
                .attr("dy", "-5px")
                .text(d => {
                    const maxLength = d.data.type === 'country' ? 15 : 12;
                    return d.data.name.length > maxLength ? 
                           d.data.name.substring(0, maxLength) + '...' : 
                           d.data.name;
                })
                .style("fill", d => getTextColor(d.data.type));

            // Add subtitle text
            nodeEnter.append("text")
                .attr("class", d => `node-subtitle ${d.data.type}-text`)
                .attr("dy", "8px")
                .text(d => {
                    const subtitles = {
                        'country': 'Country',
                        'province': 'Province',
                        'district': 'District',
                        'llg': 'LLG'
                    };
                    return subtitles[d.data.type] || '';
                })
                .style("fill", d => getTextColor(d.data.type))
                .style("opacity", 0.8);

            // Add collapse indicator
            nodeEnter.append("text")
                .attr("class", "collapse-indicator")
                .attr("dy", "25px")
                .style("opacity", 0);

            // Transition nodes to their new position
            const nodeUpdate = nodeEnter.merge(node);

            nodeUpdate.transition()
                .duration(750)
                .attr("transform", d => `translate(${d.x},${d.y})`);

            nodeUpdate.select(".node-rect")
                .transition()
                .duration(750)
                .attr("width", d => getNodeWidth(d))
                .attr("height", d => getNodeHeight(d))
                .attr("x", d => -getNodeWidth(d)/2)
                .attr("y", d => -getNodeHeight(d)/2);

            nodeUpdate.select(".collapse-indicator")
                .transition()
                .duration(750)
                .style("opacity", d => d._children ? 1 : 0)
                .text(d => d._children ? '+' : '');

            // Remove exiting nodes
            const nodeExit = node.exit().transition()
                .duration(750)
                .attr("transform", d => `translate(${source.x},${source.y})`)
                .remove();

            nodeExit.select(".node-rect")
                .attr("width", 0)
                .attr("height", 0);

            // Update links
            const link = g.selectAll(".link")
                .data(links, d => d.id);

            const linkEnter = link.enter().insert("path", "g")
                .attr("class", "link")
                .attr("d", d => {
                    const o = {x: source.x0 || 0, y: source.y0 || 0};
                    return diagonal(o, o);
                });

            const linkUpdate = linkEnter.merge(link);

            linkUpdate.transition()
                .duration(750)
                .attr("d", d => diagonal(d, d.parent));

            const linkExit = link.exit().transition()
                .duration(750)
                .attr("d", d => {
                    const o = {x: source.x, y: source.y};
                    return diagonal(o, o);
                })
                .remove();

            // Store old positions for transition
            nodes.forEach(d => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        }

        function diagonal(s, d) {
            const path = `M ${s.x} ${s.y}
                         C ${s.x} ${(s.y + d.y) / 2},
                           ${d.x} ${(s.y + d.y) / 2},
                           ${d.x} ${d.y}`;
            return path;
        }

        function click(event, d) {
            if (d.children) {
                d._children = d.children;
                d.children = null;
            } else {
                d.children = d._children;
                d._children = null;
            }
            update(d);
        }

        function showTooltip(event, d) {
            const tooltip = d3.select("#tooltip");
            tooltip.transition()
                .duration(200)
                .style("opacity", 1);
            tooltip.html(`
                <strong>${d.data.name}</strong><br>
                Type: ${d.data.type.charAt(0).toUpperCase() + d.data.type.slice(1)}<br>
                ${d.children || d._children ? `Children: ${(d.children || d._children).length}` : 'No children'}
            `)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        }

        function hideTooltip() {
            d3.select("#tooltip").transition()
                .duration(200)
                .style("opacity", 0);
        }

        // Control functions
        function expandAll() {
            function expand(d) {
                if (d._children) {
                    d.children = d._children;
                    d._children = null;
                }
                if (d.children) {
                    d.children.forEach(expand);
                }
            }
            expand(root);
            update(root);
        }

        function collapseAll() {
            function collapse(d) {
                if (d.children) {
                    d._children = d.children;
                    d.children = null;
                    d._children.forEach(collapse);
                }
            }
            root.children.forEach(collapse);
            update(root);
        }

        function centerTree() {
            const bounds = g.node().getBBox();
            const fullWidth = containerRect.width;
            const fullHeight = containerRect.height;
            const width = bounds.width;
            const height = bounds.height;
            const midX = bounds.x + width / 2;
            const midY = bounds.y + height / 2;
            const scale = 0.8 / Math.max(width / fullWidth, height / fullHeight);
            const translate = [fullWidth / 2 - scale * midX, fullHeight / 2 - scale * midY];
            
            svg.transition()
                .duration(750)
                .call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
        }

        function resetZoom() {
            svg.transition()
                .duration(750)
                .call(zoom.transform, d3.zoomIdentity);
        }

        function zoomIn() {
            svg.transition()
                .duration(300)
                .call(zoom.scaleBy, 1.5);
        }

        function zoomOut() {
            svg.transition()
                .duration(300)
                .call(zoom.scaleBy, 1 / 1.5);
        }

        // Initialize
        let i = 0;
        root.x0 = width / 2;
        root.y0 = 0;

        // Collapse some nodes initially
        if (root.children) {
            root.children.forEach(d => {
                if (d.children) {
                    d.children.forEach(child => {
                        if (child.children) {
                            child._children = child.children;
                            child.children = null;
                        }
                    });
                }
            });
        }

        update(root);

        // Make functions globally accessible
        window.expandAll = expandAll;
        window.collapseAll = collapseAll;
        window.centerTree = centerTree;
        window.resetZoom = resetZoom;
        window.zoomIn = zoomIn;
        window.zoomOut = zoomOut;
    </script>
</body>
</html>