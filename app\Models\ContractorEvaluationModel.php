<?php

namespace App\Models;

/**
 * Contractor Evaluation Model
 * 
 * Manages contractor M&E evaluations including milestone,
 * phase, project, and annual evaluations with ratings and findings.
 */
class ContractorEvaluationModel extends BaseModel
{
    protected $table      = 'contractor_evaluations';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'contractor_id', 'project_id', 'evaluation_date', 'evaluation_type',
        'reference_id', 'quality_rating', 'timeline_rating', 'budget_rating',
        'overall_rating', 'findings', 'recommendations', 'action_required',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'contractor_id'    => 'required|integer',
        'project_id'       => 'required|integer',
        'evaluation_date'  => 'required|valid_date',
        'evaluation_type'  => 'required|in_list[milestone,phase,project,annual]',
        'reference_id'     => 'integer',
        'quality_rating'   => 'in_list[excellent,good,satisfactory,poor,unacceptable]',
        'timeline_rating'  => 'in_list[ahead,on-time,minor-delay,major-delay,critical-delay]',
        'budget_rating'    => 'in_list[under-budget,on-budget,minor-overrun,major-overrun,critical-overrun]',
        'overall_rating'   => 'in_list[excellent,good,satisfactory,poor,unacceptable]'
    ];
    
    protected $validationMessages = [
        'contractor_id' => [
            'required' => 'Contractor ID is required'
        ],
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'evaluation_date' => [
            'required' => 'Evaluation date is required'
        ],
        'evaluation_type' => [
            'required' => 'Evaluation type is required'
        ]
    ];
    
    /**
     * Get evaluations by contractor
     */
    public function getByContractor(int $contractorId): array
    {
        return $this->select('contractor_evaluations.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = contractor_evaluations.project_id')
                   ->where('contractor_evaluations.contractor_id', $contractorId)
                   ->orderBy('contractor_evaluations.evaluation_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get evaluations by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->select('contractor_evaluations.*, contractors.name as contractor_name, contractors.contractor_code')
                   ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                   ->where('contractor_evaluations.project_id', $projectId)
                   ->orderBy('contractor_evaluations.evaluation_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get evaluations by type
     */
    public function getByType(string $evaluationType, ?int $orgId = null): array
    {
        $query = $this->select('contractor_evaluations.*, contractors.name as contractor_name, contractors.contractor_code, projects.title as project_title, projects.pro_code')
                     ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                     ->join('projects', 'projects.id = contractor_evaluations.project_id')
                     ->where('contractor_evaluations.evaluation_type', $evaluationType);
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractor_evaluations.evaluation_date', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get evaluations by overall rating
     */
    public function getByRating(string $rating, ?int $orgId = null): array
    {
        $query = $this->select('contractor_evaluations.*, contractors.name as contractor_name, contractors.contractor_code, projects.title as project_title')
                     ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                     ->join('projects', 'projects.id = contractor_evaluations.project_id')
                     ->where('contractor_evaluations.overall_rating', $rating);
        
        if ($orgId) {
            $query = $query->where('contractors.org_id', $orgId);
        }
        
        return $query->orderBy('contractor_evaluations.evaluation_date', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get contractor evaluation summary
     */
    public function getEvaluationSummary(int $contractorId): array
    {
        $evaluations = $this->where('contractor_id', $contractorId)->findAll();
        
        if (empty($evaluations)) {
            return [
                'total_evaluations' => 0,
                'rating_distribution' => [],
                'latest_evaluation' => null,
                'performance_trend' => null
            ];
        }
        
        $summary = [
            'total_evaluations' => count($evaluations),
            'rating_distribution' => [
                'quality' => [],
                'timeline' => [],
                'budget' => [],
                'overall' => []
            ],
            'latest_evaluation' => null,
            'performance_trend' => []
        ];
        
        // Count rating distributions
        foreach ($evaluations as $evaluation) {
            if ($evaluation['quality_rating']) {
                $summary['rating_distribution']['quality'][$evaluation['quality_rating']] = 
                    ($summary['rating_distribution']['quality'][$evaluation['quality_rating']] ?? 0) + 1;
            }
            if ($evaluation['timeline_rating']) {
                $summary['rating_distribution']['timeline'][$evaluation['timeline_rating']] = 
                    ($summary['rating_distribution']['timeline'][$evaluation['timeline_rating']] ?? 0) + 1;
            }
            if ($evaluation['budget_rating']) {
                $summary['rating_distribution']['budget'][$evaluation['budget_rating']] = 
                    ($summary['rating_distribution']['budget'][$evaluation['budget_rating']] ?? 0) + 1;
            }
            if ($evaluation['overall_rating']) {
                $summary['rating_distribution']['overall'][$evaluation['overall_rating']] = 
                    ($summary['rating_distribution']['overall'][$evaluation['overall_rating']] ?? 0) + 1;
            }
        }
        
        // Get latest evaluation
        $latest = $this->where('contractor_id', $contractorId)
                      ->orderBy('evaluation_date', 'DESC')
                      ->first();
        $summary['latest_evaluation'] = $latest;
        
        // Performance trend (last 6 evaluations)
        $recent = $this->where('contractor_id', $contractorId)
                      ->orderBy('evaluation_date', 'DESC')
                      ->limit(6)
                      ->findAll();
        
        $summary['performance_trend'] = array_reverse($recent);
        
        return $summary;
    }
    
    /**
     * Get evaluation statistics by organization
     */
    public function getEvaluationStatistics(int $orgId): array
    {
        $stats = [];
        
        // Total evaluations
        $stats['total'] = $this->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                              ->where('contractors.org_id', $orgId)
                              ->countAllResults();
        
        // By evaluation type
        $typeCounts = $this->select('evaluation_type, COUNT(*) as count')
                          ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                          ->where('contractors.org_id', $orgId)
                          ->groupBy('evaluation_type')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'evaluation_type');
        
        // By overall rating
        $ratingCounts = $this->select('overall_rating, COUNT(*) as count')
                            ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                            ->where('contractors.org_id', $orgId)
                            ->where('overall_rating IS NOT NULL')
                            ->groupBy('overall_rating')
                            ->findAll();
        
        $stats['by_rating'] = array_column($ratingCounts, 'count', 'overall_rating');
        
        // Evaluations requiring action
        $stats['action_required'] = $this->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                                        ->where('contractors.org_id', $orgId)
                                        ->where('contractor_evaluations.action_required IS NOT NULL')
                                        ->where('contractor_evaluations.action_required !=', '')
                                        ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get evaluations requiring action
     */
    public function getEvaluationsRequiringAction(int $orgId): array
    {
        return $this->select('contractor_evaluations.*, contractors.name as contractor_name, contractors.contractor_code, projects.title as project_title')
                   ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                   ->join('projects', 'projects.id = contractor_evaluations.project_id')
                   ->where('contractors.org_id', $orgId)
                   ->where('contractor_evaluations.action_required IS NOT NULL')
                   ->where('contractor_evaluations.action_required !=', '')
                   ->orderBy('contractor_evaluations.evaluation_date', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get recent evaluations for organization
     */
    public function getRecentEvaluations(int $orgId, int $limit = 10): array
    {
        return $this->select('contractor_evaluations.*, contractors.name as contractor_name, contractors.contractor_code, projects.title as project_title, projects.pro_code')
                   ->join('contractors', 'contractors.id = contractor_evaluations.contractor_id')
                   ->join('projects', 'projects.id = contractor_evaluations.project_id')
                   ->where('contractors.org_id', $orgId)
                   ->orderBy('contractor_evaluations.evaluation_date', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }
    
    /**
     * Check if evaluation exists for specific criteria
     */
    public function evaluationExists(int $contractorId, int $projectId, string $evaluationType, ?int $referenceId = null): bool
    {
        $query = $this->where('contractor_id', $contractorId)
                     ->where('project_id', $projectId)
                     ->where('evaluation_type', $evaluationType);
        
        if ($referenceId) {
            $query = $query->where('reference_id', $referenceId);
        }
        
        return $query->countAllResults() > 0;
    }
    
    /**
     * Get evaluation dashboard data
     */
    public function getEvaluationDashboard(int $orgId): array
    {
        return [
            'statistics' => $this->getEvaluationStatistics($orgId),
            'recent_evaluations' => $this->getRecentEvaluations($orgId, 5),
            'action_required' => $this->getEvaluationsRequiringAction($orgId),
            'poor_performers' => $this->getByRating('poor', $orgId),
            'unacceptable_performers' => $this->getByRating('unacceptable', $orgId)
        ];
    }
}
