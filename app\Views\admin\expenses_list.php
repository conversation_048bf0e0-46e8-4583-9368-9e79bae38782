<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-expenses" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-credit-card me-2"></i>
    Add Expense
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-credit-card me-2"></i>
            Project Expenses
        </h1>
        <p class="text-muted mb-0">
            Expense records for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Expense Statistics -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
    <!-- Total Expenses -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">💳</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
            $<?= number_format($expenseStats['total_amount'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Total Expenses</div>
    </div>

    <!-- Total Records -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📊</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?= $expenseStats['total_count'] ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Expense Records</div>
    </div>

    <!-- Average Expense -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📈</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            $<?= number_format($expenseStats['average_amount'], 2) ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Average Amount</div>
    </div>

    <!-- Latest Payment -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📅</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-secondary); margin-bottom: var(--spacing-xs);">
            <?php if (!empty($expenses)): ?>
                <?= date('M d', strtotime($expenses[0]['paid_on'])) ?>
            <?php else: ?>
                N/A
            <?php endif; ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Latest Payment</div>
    </div>
</div>

<!-- Expenses Table -->
<div class="card">
    <div class="card-header">
        💳 Expense Records
    </div>

    <?php if (!empty($expenses)): ?>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-color);">
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Description
                        </th>
                        <th style="padding: var(--spacing-md); text-align: right; font-weight: 600; color: var(--text-primary);">
                            Amount Paid
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Payment Date
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Milestone
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Supporting File
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expenses as $expense): ?>
                        <tr style="border-bottom: 1px solid var(--border-color);">
                            <td style="padding: var(--spacing-md);">
                                <div style="font-weight: 600; color: var(--text-primary);">
                                    <?= esc($expense['description']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                                    Created: <?= date('M d, Y', strtotime($expense['created_at'])) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: right;">
                                <div style="font-weight: 700; color: var(--brand-primary); font-size: 1.1rem;">
                                    $<?= number_format($expense['amount_paid'], 2) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <div style="font-weight: 600; color: var(--text-primary);">
                                    <?= date('M d, Y', strtotime($expense['paid_on'])) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <?php if (!empty($expense['milestone_title'])): ?>
                                    <div style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; display: inline-block;">
                                        <?= esc($expense['milestone_code']) ?>
                                    </div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                                        <?= esc($expense['milestone_title']) ?>
                                    </div>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">No milestone</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <?php if (!empty($expense['file_path'])): ?>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/download') ?>" 
                                       class="btn btn-sm btn-outline-primary" 
                                       style="font-size: 0.75rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                        📎 Download
                                    </a>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-style: italic;">No file</span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <div style="display: flex; gap: var(--spacing-xs); justify-content: center; flex-wrap: wrap;">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id']) ?>" 
                                       class="btn btn-sm btn-outline-secondary" 
                                       style="font-size: 0.75rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                        👁️ View
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/edit') ?>" 
                                       class="btn btn-sm btn-outline-primary" 
                                       style="font-size: 0.75rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                        ✏️ Edit
                                    </a>
                                    <form method="post" 
                                          action="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/delete') ?>" 
                                          style="display: inline-block;"
                                          onsubmit="return confirm('Are you sure you want to delete this expense record?');">
                                        <?= csrf_field() ?>
                                        <button type="submit" 
                                                class="btn btn-sm btn-outline-danger" 
                                                style="font-size: 0.75rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                            🗑️ Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="padding: var(--spacing-xl); text-align: center;">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md); opacity: 0.5;">💳</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Expenses Found</h3>
            <p style="color: var(--text-muted); margin-bottom: var(--spacing-lg);">
                No expense records have been created for this project yet.
            </p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/create') ?>" class="btn btn-primary">
                💳 Add First Expense
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Display Success/Error Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-secondary); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('success')) ?></p>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Mobile responsive table */
@media (max-width: 768px) {
    table {
        font-size: 0.875rem;
    }
    
    th, td {
        padding: var(--spacing-sm) !important;
    }
    
    .btn-sm {
        font-size: 0.7rem !important;
        padding: var(--spacing-xs) !important;
    }
    
    /* Stack action buttons vertically on mobile */
    td div[style*="display: flex"] {
        flex-direction: column !important;
        align-items: stretch !important;
    }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});
</script>

<?= $this->endSection() ?>
