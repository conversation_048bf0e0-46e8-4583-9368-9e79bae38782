<?php

namespace App\Models;

use CodeIgniter\Model;
use App\Traits\Auditable;

/**
 * Base Model for All Auditable Models
 *
 * This base model provides automatic audit logging for all CRUD operations
 * across all portals (Dakoii, Admin, Monitor). It automatically detects
 * the portal context and logs activities accordingly.
 */
class BaseModel extends Model
{
    use Auditable;

    protected $beforeInsert = ['createAudit'];
    protected $beforeUpdate = ['updateAudit'];
    protected $beforeDelete = ['deleteAudit'];

    protected function createAudit(array $data)
    {
        return $this->audit('create', $data);
    }

    protected function updateAudit(array $data)
    {
        return $this->audit('update', $data);
    }

    protected function deleteAudit(array $data)
    {
        return $this->audit('delete', $data);
    }

    /**
     * Skip audit logging for the next operation
     * Useful for bulk operations, seeders, or system operations
     */
    public function skipAudit(): self
    {
        $this->auditSkip = true;
        return $this;
    }
}
