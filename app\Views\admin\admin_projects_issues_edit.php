<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-secondary">
    ← Back to Issues List
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Issue Addressed
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update issue addressed by project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Issue Form -->
<div class="card">
    <div class="card-header">
        🎯 Issue Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/issues/' . $issue['id'] . '/edit') ?>" class="issue-edit-form">
            <?= csrf_field() ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Issue Description -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="description" class="form-label">
                            Issue Description <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="form-input"
                                  style="border: 2px solid var(--brand-danger); min-height: 150px; resize: vertical;"
                                  placeholder="Describe the problem or challenge this project addresses..."
                                  required><?= old('description', $issue['description']) ?></textarea>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Detailed description of the issue or problem addressed (max 500 characters)
                        </small>
                        <?php if (isset($errors['description'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['description']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Examples Section -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            💡 Examples of Issues Addressed
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                            <div><strong>Direct Issues:</strong></div>
                            <div style="margin-left: var(--spacing-md); margin-bottom: var(--spacing-sm);">
                                • "Lack of clean water access in rural communities"<br>
                                • "Poor road infrastructure limiting market access"<br>
                                • "Insufficient classroom space for growing student population"
                            </div>
                            <div><strong>Indirect Issues:</strong></div>
                            <div style="margin-left: var(--spacing-md);">
                                • "Reduced economic opportunities due to poor infrastructure"<br>
                                • "Health issues from contaminated water sources"<br>
                                • "Limited educational outcomes from overcrowded classrooms"
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Impact Type -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="issue_type" class="form-label">
                            Impact Type <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <select id="issue_type"
                                name="issue_type"
                                class="form-input"
                                style="border: 2px solid var(--brand-danger);"
                                required>
                            <option value="">Select impact type...</option>
                            <option value="direct" <?= old('issue_type', $issue['issue_type']) === 'direct' ? 'selected' : '' ?>>🎯 Direct Impact</option>
                            <option value="indirect" <?= old('issue_type', $issue['issue_type']) === 'indirect' ? 'selected' : '' ?>>🔄 Indirect Impact</option>
                        </select>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Choose whether this issue is directly or indirectly addressed by the project
                        </small>
                        <?php if (isset($errors['issue_type'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['issue_type']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Impact Type Explanation -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📊 Impact Type Guide
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                            <div style="margin-bottom: var(--spacing-sm);">
                                <strong style="color: var(--brand-secondary);">🎯 Direct Impact:</strong><br>
                                Primary problems that the project specifically targets and addresses through its main activities.
                            </div>
                            <div>
                                <strong style="color: var(--brand-accent);">🔄 Indirect Impact:</strong><br>
                                Secondary problems that are addressed as a result of solving the direct issues or through project spillover effects.
                            </div>
                        </div>
                    </div>

                    <!-- Audit Information -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-md); border-radius: var(--radius-sm);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📋 Audit Information
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-muted); line-height: 1.5;">
                            <div><strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($issue['created_at'])) ?></div>
                            <?php if ($issue['updated_at']): ?>
                                <div><strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($issue['updated_at'])) ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    🎯 Update Issue
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Form validation
document.querySelector('.issue-edit-form').addEventListener('submit', function(e) {
    const description = document.getElementById('description').value.trim();
    const issueType = document.getElementById('issue_type').value;

    if (!description || !issueType) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
});

// Update form styling based on selection
document.getElementById('issue_type').addEventListener('change', function() {
    const value = this.value;
    const examples = document.querySelector('[style*="Examples of Issues Addressed"]').parentElement;
    
    if (value === 'direct') {
        examples.style.borderLeft = '4px solid var(--brand-secondary)';
    } else if (value === 'indirect') {
        examples.style.borderLeft = '4px solid var(--brand-accent)';
    } else {
        examples.style.borderLeft = 'none';
    }
});

// Initialize form styling on page load
document.addEventListener('DOMContentLoaded', function() {
    const issueType = document.getElementById('issue_type').value;
    const examples = document.querySelector('[style*="Examples of Issues Addressed"]').parentElement;
    
    if (issueType === 'direct') {
        examples.style.borderLeft = '4px solid var(--brand-secondary)';
    } else if (issueType === 'indirect') {
        examples.style.borderLeft = '4px solid var(--brand-accent)';
    }
});
</script>

<?= $this->endSection() ?>
