<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Expenses</span>
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/edit') ?>" class="btn btn-primary btn-mobile">
    <span class="btn-icon">✏️</span>
    <span class="btn-text">Edit Expense</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Expense Details
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Expense record for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Expense Information -->
<div class="card">
    <div class="card-header">
        💳 Expense Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
            
            <!-- Left Column -->
            <div>
                <!-- Description -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs); font-size: 0.875rem;">
                        Description
                    </label>
                    <div style="background: var(--bg-secondary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                        <p style="margin: 0; color: var(--text-primary); line-height: 1.5;">
                            <?= esc($expense['description']) ?>
                        </p>
                    </div>
                </div>

                <!-- Amount Paid -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs); font-size: 0.875rem;">
                        Amount Paid
                    </label>
                    <div style="background: var(--bg-secondary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary);">
                            $<?= number_format($expense['amount_paid'], 2) ?>
                        </div>
                    </div>
                </div>

                <!-- Payment Date -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs); font-size: 0.875rem;">
                        Payment Date
                    </label>
                    <div style="background: var(--bg-secondary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                        <div style="font-weight: 600; color: var(--text-primary);">
                            <?= date('F d, Y', strtotime($expense['paid_on'])) ?>
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                            <?= date('l', strtotime($expense['paid_on'])) ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div>
                <!-- Related Milestone -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs); font-size: 0.875rem;">
                        Related Milestone
                    </label>
                    <div style="background: var(--bg-secondary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                        <?php if (!empty($expense['milestone_title'])): ?>
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                <div style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                    <?= esc($expense['milestone_code']) ?>
                                </div>
                                <div>
                                    <div style="font-weight: 600; color: var(--text-primary);">
                                        <?= esc($expense['milestone_title']) ?>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div style="color: var(--text-muted); font-style: italic;">
                                No milestone linked to this expense
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Supporting Document -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs); font-size: 0.875rem;">
                        Supporting Document
                    </label>
                    <div style="background: var(--bg-secondary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                        <?php if (!empty($expense['file_path'])): ?>
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                <span style="font-size: 2rem;">📎</span>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; color: var(--text-primary);">
                                        <?= basename($expense['file_path']) ?>
                                    </div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted);">
                                        Uploaded: <?= date('M d, Y', strtotime($expense['created_at'])) ?>
                                    </div>
                                </div>
                                <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/download') ?>" 
                                   class="btn btn-sm btn-primary" 
                                   style="font-size: 0.75rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                    📥 Download
                                </a>
                            </div>
                        <?php else: ?>
                            <div style="color: var(--text-muted); font-style: italic;">
                                No supporting document available
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Record Information -->
                <div style="margin-bottom: var(--spacing-lg);">
                    <label style="display: block; font-weight: 600; color: var(--text-secondary); margin-bottom: var(--spacing-xs); font-size: 0.875rem;">
                        Record Information
                    </label>
                    <div style="background: var(--bg-secondary); padding: var(--spacing-md); border-radius: var(--radius-sm); border: 1px solid var(--border-color);">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); font-size: 0.75rem;">
                            <div>
                                <div style="color: var(--text-muted);">Created:</div>
                                <div style="color: var(--text-primary); font-weight: 600;">
                                    <?= date('M d, Y g:i A', strtotime($expense['created_at'])) ?>
                                </div>
                            </div>
                            <?php if (!empty($expense['updated_at'])): ?>
                                <div>
                                    <div style="color: var(--text-muted);">Last Updated:</div>
                                    <div style="color: var(--text-primary); font-weight: 600;">
                                        <?= date('M d, Y g:i A', strtotime($expense['updated_at'])) ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color); flex-wrap: wrap;">
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses') ?>" class="btn btn-secondary btn-mobile">
                <span class="btn-icon">←</span>
                <span class="btn-text">Back to List</span>
            </a>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/edit') ?>" class="btn btn-primary btn-mobile">
                <span class="btn-icon">✏️</span>
                <span class="btn-text">Edit Expense</span>
            </a>
            <form method="post" 
                  action="<?= base_url('admin/projects/' . $project['id'] . '/expenses/' . $expense['id'] . '/delete') ?>" 
                  style="display: inline-block;"
                  onsubmit="return confirm('Are you sure you want to delete this expense record? This action cannot be undone.');">
                <?= csrf_field() ?>
                <button type="submit" class="btn btn-danger btn-mobile">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">Delete Expense</span>
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Display Success/Error Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-secondary); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('success')) ?></p>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    div[style*="display: flex"][style*="justify-content: flex-end"] {
        justify-content: stretch !important;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    div[style*="display: flex"][style*="gap: var(--spacing-md)"] {
        gap: var(--spacing-sm) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});
</script>

<?= $this->endSection() ?>
