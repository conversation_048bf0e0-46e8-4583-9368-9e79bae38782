<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectMilestoneModel;
use App\Models\ProjectMilestoneFileModel;

/**
 * Admin Project Milestone Assessment Controller
 * 
 * Handles milestone assessment with evidence file uploads and verification workflow.
 * Follows RESTful approach with separate methods for each operation.
 */
class AdminProjectMilestoneAssessmentController extends BaseController
{
    protected $projectModel;
    protected $projectMilestoneModel;
    protected $projectMilestoneFileModel;
    
    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectMilestoneModel = new ProjectMilestoneModel();
        $this->projectMilestoneFileModel = new ProjectMilestoneFileModel();
    }

    /**
     * Display milestone assessment overview - GET request
     */
    public function index($projectId, $milestoneId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get milestone
        $milestone = $this->projectMilestoneModel->where('id', $milestoneId)
                                                ->where('project_id', $projectId)
                                                ->where('deleted_at', null)
                                                ->first();

        if (!$milestone) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Milestone not found.');
        }

        // Get milestone evidence files
        $evidenceFiles = $this->projectMilestoneFileModel->getByMilestone($milestoneId);

        // Get file statistics
        $fileStats = $this->projectMilestoneFileModel->getFileStatistics($projectId, $milestoneId);

        $data = [
            'title' => 'Milestone Assessment - PROMIS Admin',
            'page_title' => 'Milestone Assessment',
            'project' => $project,
            'milestone' => $milestone,
            'evidenceFiles' => $evidenceFiles,
            'fileStats' => $fileStats
        ];

        return view('admin/admin_milestones_assessment_index', $data);
    }

    /**
     * Show evidence upload form - GET request
     */
    public function upload($projectId, $milestoneId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get milestone
        $milestone = $this->projectMilestoneModel->where('id', $milestoneId)
                                                ->where('project_id', $projectId)
                                                ->where('deleted_at', null)
                                                ->first();

        if (!$milestone) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Milestone not found.');
        }

        $data = [
            'title' => 'Upload Evidence - PROMIS Admin',
            'page_title' => 'Upload Evidence',
            'project' => $project,
            'milestone' => $milestone
        ];

        return view('admin/admin_milestones_assessment_upload', $data);
    }

    /**
     * Process evidence file upload - POST request
     */
    public function store($projectId, $milestoneId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get milestone
        $milestone = $this->projectMilestoneModel->where('id', $milestoneId)
                                                ->where('project_id', $projectId)
                                                ->where('deleted_at', null)
                                                ->first();

        if (!$milestone) {
            return redirect()->to(base_url('admin/projects/' . $projectId))
                           ->with('error', 'Milestone not found.');
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'title' => 'required|max_length[150]',
            'description' => 'permit_empty',
            'file_type' => 'required|in_list[image,document,video,audio,other]',
            'evidence_file' => 'uploaded[evidence_file]|max_size[evidence_file,25600]' // 25MB max
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        // Handle file upload
        $file = $this->request->getFile('evidence_file');
        
        if (!$file->isValid()) {
            return redirect()->back()->withInput()->with('error', 'File upload failed: ' . $file->getErrorString());
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'public/uploads/milestones/' . $projectId . '/' . $milestoneId;
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Generate unique filename
        $fileName = $file->getRandomName();
        $filePath = 'public/uploads/milestones/' . $projectId . '/' . $milestoneId . '/' . $fileName;

        try {
            // Move file to upload directory
            $file->move($uploadPath, $fileName);

            // Save file record to database
            $fileData = [
                'project_id' => $projectId,
                'milestone_id' => $milestoneId,
                'file_path' => $filePath,
                'file_type' => $this->request->getPost('file_type'),
                'file_size' => round($file->getSize() / 1024), // Convert to KB
                'title' => $this->request->getPost('title'),
                'description' => $this->request->getPost('description') ?: null,
                'is_verified' => 0, // Default to unverified
                'created_by' => $adminUserId
            ];

            $success = $this->projectMilestoneFileModel->uploadFile($fileData);
            
            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/milestones/' . $milestoneId . '/assessment'))
                               ->with('success', 'Evidence file uploaded successfully!');
            } else {
                // Delete uploaded file if database insert failed
                if (file_exists(FCPATH . $filePath)) {
                    unlink(FCPATH . $filePath);
                }
                return redirect()->back()->withInput()->with('error', 'Failed to save file record.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error uploading file: ' . $e->getMessage());
        }
    }

    /**
     * Verify evidence file - POST request
     */
    public function verify($projectId, $milestoneId, $fileId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get file
        $file = $this->projectMilestoneFileModel->where('id', $fileId)
                                               ->where('project_id', $projectId)
                                               ->where('milestone_id', $milestoneId)
                                               ->where('deleted_at', null)
                                               ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'Evidence file not found.');
        }

        // Get verification data
        $isVerified = $this->request->getPost('is_verified') === '1';
        $verificationNotes = $this->request->getPost('verification_notes');

        try {
            $success = $this->projectMilestoneFileModel->verifyFile($fileId, $isVerified, $verificationNotes, $adminUserId);
            
            if ($success) {
                $message = $isVerified ? 'Evidence file verified successfully!' : 'Evidence file marked as unverified.';
                return redirect()->to(base_url('admin/projects/' . $projectId . '/milestones/' . $milestoneId . '/assessment'))
                               ->with('success', $message);
            } else {
                return redirect()->back()->with('error', 'Failed to update verification status.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error updating verification: ' . $e->getMessage());
        }
    }

    /**
     * Delete evidence file - POST request
     */
    public function delete($projectId, $milestoneId, $fileId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get file
        $file = $this->projectMilestoneFileModel->where('id', $fileId)
                                               ->where('project_id', $projectId)
                                               ->where('milestone_id', $milestoneId)
                                               ->where('deleted_at', null)
                                               ->first();

        if (!$file) {
            return redirect()->back()->with('error', 'Evidence file not found.');
        }

        try {
            $success = $this->projectMilestoneFileModel->deleteFile($fileId, $adminUserId);
            
            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/milestones/' . $milestoneId . '/assessment'))
                               ->with('success', 'Evidence file deleted successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to delete evidence file.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error deleting file: ' . $e->getMessage());
        }
    }
}
