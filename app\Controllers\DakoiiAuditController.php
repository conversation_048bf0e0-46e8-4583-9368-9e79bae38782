<?php

namespace App\Controllers;

use App\Models\AuditLogModel;

/**
 * Dakoii Audit Controller
 * 
 * Handles audit trail viewing and filtering for the Dakoii portal.
 * Provides RESTful interface for audit log management.
 */
class DakoiiAuditController extends BaseController
{
    protected $auditLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
    }

    /**
     * Display audit trail list with filtering options
     * GET /dakoii/audit
     */
    public function index()
    {
        // Get filter parameters
        $portal = $this->request->getVar('portal') ?? 'dakoii';
        $action = $this->request->getVar('action');
        $tableName = $this->request->getVar('table_name');
        $userId = $this->request->getVar('user_id');
        $organizationId = $this->request->getVar('organization_id');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');
        $limit = (int)($this->request->getVar('limit') ?? 50);

        // Build query
        $query = $this->auditLogModel->orderBy('id', 'DESC');

        // Apply filters
        if ($portal) {
            $query = $query->where('portal', $portal);
        }
        if ($action) {
            $query = $query->where('action', $action);
        }
        if ($tableName) {
            $query = $query->where('table_name', $tableName);
        }
        if ($userId) {
            $query = $query->where('user_id', $userId);
        }
        if ($organizationId) {
            $query = $query->where('organization_id', $organizationId);
        }
        if ($dateFrom) {
            $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        }
        if ($dateTo) {
            $query = $query->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Get paginated results
        $auditLogs = $query->paginate($limit);
        $pager = $this->auditLogModel->pager;

        // Get filter options for dropdowns
        $filterOptions = $this->getFilterOptions();

        $data = [
            'title' => 'Audit Trail',
            'page_title' => 'Audit Trail',
            'audit_logs' => $auditLogs,
            'pager' => $pager,
            'filter_options' => $filterOptions,
            'current_filters' => [
                'portal' => $portal,
                'action' => $action,
                'table_name' => $tableName,
                'user_id' => $userId,
                'organization_id' => $organizationId,
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'limit' => $limit,
            ]
        ];

        return view('dakoii/dakoii_audit_list', $data);
    }

    /**
     * View detailed audit log entry
     * GET /dakoii/audit/{id}
     */
    public function show($id)
    {
        $auditLog = $this->auditLogModel->find($id);

        if (!$auditLog) {
            return redirect()->to(base_url('dakoii/audit'))
                ->with('error', 'Audit log entry not found.');
        }

        // Decode JSON data for better display
        $auditLog['old_data_decoded'] = $auditLog['old_data'] ? json_decode($auditLog['old_data'], true) : null;
        $auditLog['new_data_decoded'] = $auditLog['new_data'] ? json_decode($auditLog['new_data'], true) : null;

        $data = [
            'title' => 'Audit Log Details',
            'page_title' => 'Audit Log Details',
            'audit_log' => $auditLog
        ];

        return view('dakoii/dakoii_audit_view', $data);
    }

    /**
     * Get audit statistics for dashboard
     * GET /dakoii/audit/stats
     */
    public function stats()
    {
        $portal = $this->request->getVar('portal') ?? 'dakoii';
        $stats = $this->auditLogModel->getPortalAuditStats($portal);

        $data = [
            'title' => 'Audit Statistics',
            'page_title' => 'Audit Statistics',
            'stats' => $stats,
            'portal' => $portal
        ];

        return view('dakoii/dakoii_audit_stats', $data);
    }

    /**
     * Export audit logs to CSV
     * GET /dakoii/audit/export
     */
    public function export()
    {
        // Get filter parameters (same as index method)
        $portal = $this->request->getVar('portal') ?? 'dakoii';
        $action = $this->request->getVar('action');
        $tableName = $this->request->getVar('table_name');
        $userId = $this->request->getVar('user_id');
        $organizationId = $this->request->getVar('organization_id');
        $dateFrom = $this->request->getVar('date_from');
        $dateTo = $this->request->getVar('date_to');
        $limit = (int)($this->request->getVar('limit') ?? 1000); // Higher limit for export

        // Build query (same logic as index)
        $query = $this->auditLogModel->orderBy('id', 'DESC');

        if ($portal) $query = $query->where('portal', $portal);
        if ($action) $query = $query->where('action', $action);
        if ($tableName) $query = $query->where('table_name', $tableName);
        if ($userId) $query = $query->where('user_id', $userId);
        if ($organizationId) $query = $query->where('organization_id', $organizationId);
        if ($dateFrom) $query = $query->where('created_at >=', $dateFrom . ' 00:00:00');
        if ($dateTo) $query = $query->where('created_at <=', $dateTo . ' 23:59:59');

        $auditLogs = $query->findAll($limit);

        // Generate CSV
        $filename = 'audit_logs_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'ID', 'Date/Time', 'Portal', 'Module', 'Action', 'Table', 'Record ID',
            'User ID', 'Username', 'User Type', 'User Full Name',
            'Organization ID', 'Organization Name', 'Organization Type',
            'Project ID', 'Project Title', 'IP Address', 'Description'
        ]);
        
        // CSV data
        foreach ($auditLogs as $log) {
            fputcsv($output, [
                $log['id'],
                $log['created_at'],
                $log['portal'],
                $log['module'],
                $log['action'],
                $log['table_name'],
                $log['primary_key'],
                $log['user_id'],
                $log['username'],
                $log['user_type'],
                $log['user_full_name'],
                $log['organization_id'],
                $log['organization_name'],
                $log['organization_type'],
                $log['project_id'],
                $log['project_title'],
                $log['ip_address'],
                $log['description']
            ]);
        }
        
        fclose($output);
        exit;
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions(): array
    {
        return [
            'portals' => [
                'dakoii' => 'Dakoii Portal',
                'admin' => 'Admin Portal',
                'monitor' => 'Monitor Portal'
            ],
            'actions' => [
                'create' => 'Create',
                'update' => 'Update',
                'delete' => 'Delete',
                'login' => 'Login',
                'logout' => 'Logout',
                'access' => 'Access'
            ],
            'tables' => $this->auditLogModel->select('table_name')
                ->distinct()
                ->orderBy('table_name')
                ->findColumn('table_name'),
            'users' => $this->auditLogModel->select('user_id, username, user_full_name')
                ->where('user_id IS NOT NULL')
                ->distinct()
                ->orderBy('username')
                ->findAll(),
            'organizations' => $this->auditLogModel->select('organization_id, organization_name')
                ->where('organization_id IS NOT NULL')
                ->distinct()
                ->orderBy('organization_name')
                ->findAll()
        ];
    }
}
