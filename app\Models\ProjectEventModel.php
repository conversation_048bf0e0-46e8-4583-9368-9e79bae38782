<?php

namespace App\Models;

/**
 * Project Event Model
 * 
 * Handles project events including delays, incidents, and other significant occurrences.
 * Tracks event impact, resolution, and status changes.
 */
class ProjectEventModel extends BaseModel
{
    protected $table      = 'project_events';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'event_type', 'severity', 'title', 'description',
        'event_date', 'resolution_date', 'impact_days', 'impact_description',
        'resolution_description', 'status', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'event_type'     => 'in_list[delay,suspension,resumption,incident,natural_disaster,funding_issue,resource_issue,stakeholder_issue,other]',
        'severity'       => 'in_list[low,medium,high,critical]',
        'title'          => 'required|max_length[150]',
        'description'    => 'required',
        'event_date'     => 'required|valid_date',
        'status'         => 'in_list[active,resolved,monitoring]',
        'impact_days'    => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'title' => [
            'required' => 'Event title is required'
        ],
        'description' => [
            'required' => 'Event description is required'
        ],
        'event_date' => [
            'required' => 'Event date is required'
        ]
    ];
    
    /**
     * Get events by project
     */
    public function getByProject(int $projectId, ?string $status = null): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('event_date', 'DESC')->findAll();
    }
    
    /**
     * Get events by milestone
     */
    public function getByMilestone(int $milestoneId, ?string $status = null): array
    {
        $query = $this->where('milestone_id', $milestoneId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('event_date', 'DESC')->findAll();
    }
    
    /**
     * Get events by type
     */
    public function getByType(string $eventType, ?string $status = null): array
    {
        $query = $this->select('project_events.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_events.project_id')
                     ->where('project_events.event_type', $eventType);
        
        if ($status) {
            $query = $query->where('project_events.status', $status);
        }
        
        return $query->orderBy('project_events.event_date', 'DESC')->findAll();
    }
    
    /**
     * Get events by severity
     */
    public function getBySeverity(string $severity, ?string $status = null): array
    {
        $query = $this->select('project_events.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_events.project_id')
                     ->where('project_events.severity', $severity);
        
        if ($status) {
            $query = $query->where('project_events.status', $status);
        }
        
        return $query->orderBy('project_events.event_date', 'DESC')->findAll();
    }
    
    /**
     * Get active events
     */
    public function getActiveEvents(?int $projectId = null): array
    {
        $query = $this->select('project_events.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_events.project_id')
                     ->where('project_events.status', 'active');
        
        if ($projectId) {
            $query = $query->where('project_events.project_id', $projectId);
        }
        
        return $query->orderBy('project_events.severity', 'DESC')
                    ->orderBy('project_events.event_date', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get critical events
     */
    public function getCriticalEvents(?int $projectId = null): array
    {
        $query = $this->select('project_events.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_events.project_id')
                     ->where('project_events.severity', 'critical')
                     ->whereIn('project_events.status', ['active', 'monitoring']);
        
        if ($projectId) {
            $query = $query->where('project_events.project_id', $projectId);
        }
        
        return $query->orderBy('project_events.event_date', 'DESC')->findAll();
    }
    
    /**
     * Get overdue events (active events older than 30 days)
     */
    public function getOverdueEvents(?int $projectId = null): array
    {
        $overdueDate = date('Y-m-d', strtotime('-30 days'));
        
        $query = $this->select('project_events.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_events.project_id')
                     ->where('project_events.status', 'active')
                     ->where('project_events.event_date <', $overdueDate);
        
        if ($projectId) {
            $query = $query->where('project_events.project_id', $projectId);
        }
        
        return $query->orderBy('project_events.event_date', 'ASC')->findAll();
    }
    
    /**
     * Create event
     */
    public function createEvent(array $eventData): bool
    {
        $eventData['created_by'] = session()->get('admin_user_id') ?? session()->get('user_id');
        $eventData['created_at'] = date('Y-m-d H:i:s');
        
        return $this->insert($eventData) !== false;
    }
    
    /**
     * Resolve event
     */
    public function resolveEvent(int $eventId, string $resolutionDescription, ?int $resolvedBy = null): bool
    {
        $data = [
            'status' => 'resolved',
            'resolution_date' => date('Y-m-d'),
            'resolution_description' => $resolutionDescription,
            'updated_by' => $resolvedBy
        ];
        
        return $this->update($eventId, $data);
    }
    
    /**
     * Update event status
     */
    public function updateEventStatus(int $eventId, string $status, ?int $updatedBy = null): bool
    {
        $data = [
            'status' => $status,
            'updated_by' => $updatedBy
        ];
        
        // If resolving, set resolution date
        if ($status === 'resolved' && !$this->find($eventId)['resolution_date']) {
            $data['resolution_date'] = date('Y-m-d');
        }
        
        return $this->update($eventId, $data);
    }
    
    /**
     * Get event statistics
     */
    public function getEventStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total events
        $stats['total_events'] = $query->countAllResults(false);
        
        // Events by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status');
        
        if ($projectId) {
            $statusCounts = $statusCounts->where('project_id', $projectId);
        }
        
        $statusResults = $statusCounts->findAll();
        $stats['by_status'] = array_column($statusResults, 'count', 'status');
        
        // Events by severity
        $severityCounts = $this->select('severity, COUNT(*) as count')
                              ->groupBy('severity');
        
        if ($projectId) {
            $severityCounts = $severityCounts->where('project_id', $projectId);
        }
        
        $severityResults = $severityCounts->findAll();
        $stats['by_severity'] = array_column($severityResults, 'count', 'severity');
        
        // Events by type
        $typeCounts = $this->select('event_type, COUNT(*) as count')
                          ->groupBy('event_type');
        
        if ($projectId) {
            $typeCounts = $typeCounts->where('project_id', $projectId);
        }
        
        $typeResults = $typeCounts->findAll();
        $stats['by_type'] = array_column($typeResults, 'count', 'event_type');
        
        // Total impact days
        $impactQuery = $this->selectSum('impact_days', 'total_impact_days');
        if ($projectId) {
            $impactQuery = $impactQuery->where('project_id', $projectId);
        }
        
        $impactResult = $impactQuery->first();
        $stats['total_impact_days'] = $impactResult['total_impact_days'] ?? 0;
        
        // Average resolution time (for resolved events)
        $stats['average_resolution_days'] = $this->getAverageResolutionTime($projectId);
        
        return $stats;
    }
    
    /**
     * Get average resolution time in days
     */
    public function getAverageResolutionTime(?int $projectId = null): float
    {
        $query = $this->select('DATEDIFF(resolution_date, event_date) as resolution_days')
                     ->where('status', 'resolved')
                     ->where('resolution_date IS NOT NULL');
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        $results = $query->findAll();
        
        if (empty($results)) {
            return 0.0;
        }
        
        $totalDays = array_sum(array_column($results, 'resolution_days'));
        return round($totalDays / count($results), 2);
    }
    
    /**
     * Get events with project and milestone info
     */
    public function getEventsWithDetails(?int $projectId = null): array
    {
        $query = $this->select('project_events.*, projects.title as project_title, projects.pro_code, project_milestones.title as milestone_title, project_milestones.milestone_code, users.name as created_by_name')
                     ->join('projects', 'projects.id = project_events.project_id')
                     ->join('project_milestones', 'project_milestones.id = project_events.milestone_id', 'left')
                     ->join('users', 'users.id = project_events.created_by', 'left');
        
        if ($projectId) {
            $query = $query->where('project_events.project_id', $projectId);
        }
        
        return $query->orderBy('project_events.event_date', 'DESC')->findAll();
    }
}
