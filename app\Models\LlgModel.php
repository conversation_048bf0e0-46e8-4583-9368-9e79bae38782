<?php

namespace App\Models;

/**
 * Model for the 'llgs' table.
 * Represents Local-Level Governments (LLGs) with district reference, code, name, type, map defaults, and audit columns.
 */
class LlgModel extends BaseModel
{
    protected $table = 'llgs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'district_id', 'llg_code', 'name', 'llg_type', 'geojson_id',
        'map_centre_lat', 'map_centre_lng', 'map_zoom',
        'created_by', 'updated_by', 'deleted_by',
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    /**
     * Validation rules:
     * - district_id, llg_code, name, and llg_type are required
     * - llg_type must be either 'urban' or 'rural'
     * - geojson_id is optional but must be a string if provided
     */
    protected $validationRules = [
        'district_id' => 'required',
        'llg_code'    => 'required',
        'name'        => 'required',
        'llg_type'    => 'permit_empty|in_list[urban,rural]',
        'geojson_id'  => 'permit_empty|string|max_length[50]',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
} 