<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-secondary">
    ← Back to Outcomes List
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Project Outcome
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update measurable deliverable for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Outcome Form -->
<div class="card">
    <div class="card-header">
        🎯 Outcome Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/outcomes/' . $outcome['id'] . '/edit') ?>" class="outcome-edit-form">
            <?= csrf_field() ?>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Outcome Description -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="outcome_text" class="form-label">
                            Outcome Description <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <textarea id="outcome_text"
                                  name="outcome_text"
                                  class="form-input"
                                  style="border: 2px solid var(--brand-danger); min-height: 120px; resize: vertical;"
                                  placeholder="e.g., Bridge construction, Double classroom building, Water well drilling..."
                                  required><?= old('outcome_text', $outcome['outcome_text']) ?></textarea>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Detailed description of the measurable deliverable (max 255 characters)
                        </small>
                        <?php if (isset($errors['outcome_text'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['outcome_text']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Examples Section -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            💡 Examples of Project Outcomes
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                            <div><strong>Infrastructure:</strong> "1 x bridge", "2 x double classroom", "1 x water well"</div>
                            <div><strong>Training:</strong> "50 x teachers trained", "100 x students graduated"</div>
                            <div><strong>Equipment:</strong> "10 x computers installed", "5 x vehicles delivered"</div>
                            <div><strong>Services:</strong> "1000 x people served", "500 x consultations provided"</div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Quantity -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="quantity" class="form-label">
                            Quantity <span style="color: var(--brand-danger);">*</span>
                        </label>
                        <input type="number"
                               id="quantity"
                               name="quantity"
                               class="form-input"
                               style="border: 2px solid var(--brand-danger);"
                               value="<?= old('quantity', $outcome['quantity']) ?>"
                               placeholder="1"
                               step="0.01"
                               min="0.01"
                               required>
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Measurable quantity of the deliverable
                        </small>
                        <?php if (isset($errors['quantity'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['quantity']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Unit -->
                    <div class="form-group" style="margin-bottom: var(--spacing-lg);">
                        <label for="unit" class="form-label">
                            Unit of Measurement
                        </label>
                        <input type="text"
                               id="unit"
                               name="unit"
                               class="form-input"
                               style="border: 2px solid var(--brand-secondary);"
                               value="<?= old('unit', $outcome['unit']) ?>"
                               placeholder="e.g., pieces, meters, people, classrooms, bridges..."
                               maxlength="50">
                        <small style="color: var(--text-muted); font-size: 0.75rem;">
                            Unit of measurement for the quantity (optional, max 50 characters)
                        </small>
                        <?php if (isset($errors['unit'])): ?>
                            <div style="color: var(--brand-danger); font-size: 0.75rem; margin-top: var(--spacing-xs);">
                                <?= esc($errors['unit']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Common Units -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📏 Common Units
                        </h4>
                        <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-xs);">
                            <button type="button" onclick="setUnit('pieces')" class="unit-btn">pieces</button>
                            <button type="button" onclick="setUnit('meters')" class="unit-btn">meters</button>
                            <button type="button" onclick="setUnit('people')" class="unit-btn">people</button>
                            <button type="button" onclick="setUnit('classrooms')" class="unit-btn">classrooms</button>
                            <button type="button" onclick="setUnit('bridges')" class="unit-btn">bridges</button>
                            <button type="button" onclick="setUnit('wells')" class="unit-btn">wells</button>
                            <button type="button" onclick="setUnit('kilometers')" class="unit-btn">kilometers</button>
                            <button type="button" onclick="setUnit('buildings')" class="unit-btn">buildings</button>
                        </div>
                    </div>

                    <!-- Audit Information -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-md); border-radius: var(--radius-sm);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📋 Audit Information
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-muted); line-height: 1.5;">
                            <div><strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($outcome['created_at'])) ?></div>
                            <?php if ($outcome['updated_at']): ?>
                                <div><strong>Last Updated:</strong> <?= date('M j, Y g:i A', strtotime($outcome['updated_at'])) ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/outcomes') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    🎯 Update Outcome
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Unit buttons */
.unit-btn {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.unit-btn:hover {
    background: var(--brand-primary);
    color: white;
    border-color: var(--brand-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Set unit function
function setUnit(unit) {
    document.getElementById('unit').value = unit;
}

// Form validation
document.querySelector('.outcome-edit-form').addEventListener('submit', function(e) {
    const outcomeText = document.getElementById('outcome_text').value.trim();
    const quantity = document.getElementById('quantity').value;

    if (!outcomeText || !quantity || parseFloat(quantity) <= 0) {
        e.preventDefault();
        alert('Please fill in all required fields with valid values.');
        return false;
    }
});
</script>

<?= $this->endSection() ?>
