<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-secondary btn-mobile">
    <span class="btn-icon">←</span>
    <span class="btn-text">Back to Issues</span>
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Add Issue Addressed
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Document a problem or challenge addressed by project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Create Issue Form -->
<div class="card">
    <div class="card-header">
        🎯 Issue Information
    </div>

    <div style="padding: var(--spacing-xl);">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="issue-create-form">
            <?= csrf_field() ?>

            <div class="form-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">

                <!-- Left Column -->
                <div>
                    <!-- Issue Description -->
                    <div class="mb-4">
                        <label for="description" class="form-label fw-semibold">
                            Issue Description <span class="text-danger">*</span>
                        </label>
                        <textarea id="description"
                                  name="description"
                                  class="form-control border-danger"
                                  rows="6"
                                  placeholder="Describe the problem or challenge this project addresses..."
                                  required><?= old('description') ?></textarea>
                        <div class="form-text">
                            Detailed description of the issue or problem addressed (max 500 characters)
                        </div>
                        <?php if (isset($errors['description'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['description']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Examples Section -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-lightbulb me-2"></i>
                            Examples of Issues Addressed
                        </h6>
                        <div class="small">
                            <div><strong>Direct Issues:</strong></div>
                            <ul class="mb-2">
                                <li>"Lack of clean water access in rural communities"</li>
                                <li>"Poor road infrastructure limiting market access"</li>
                                <li>"Insufficient classroom space for growing student population"</li>
                            </ul>
                            <div><strong>Indirect Issues:</strong></div>
                            <ul class="mb-0">
                                <li>"Reduced economic opportunities due to poor infrastructure"</li>
                                <li>"Health issues from contaminated water sources"</li>
                                <li>"Limited educational outcomes from overcrowded classrooms"</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <!-- Impact Type -->
                    <div class="mb-4">
                        <label for="issue_type" class="form-label fw-semibold">
                            Impact Type <span class="text-danger">*</span>
                        </label>
                        <select id="issue_type"
                                name="issue_type"
                                class="form-select border-danger"
                                required>
                            <option value="">Select impact type...</option>
                            <option value="direct" <?= old('issue_type') === 'direct' ? 'selected' : '' ?>>🎯 Direct Impact</option>
                            <option value="indirect" <?= old('issue_type') === 'indirect' ? 'selected' : '' ?>>🔄 Indirect Impact</option>
                        </select>
                        <div class="form-text">
                            Choose whether this issue is directly or indirectly addressed by the project
                        </div>
                        <?php if (isset($errors['issue_type'])): ?>
                            <div class="text-danger small mt-1">
                                <?= esc($errors['issue_type']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Impact Type Explanation -->
                    <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md); margin-bottom: var(--spacing-lg);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📊 Impact Type Guide
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                            <div style="margin-bottom: var(--spacing-sm);">
                                <strong style="color: var(--brand-secondary);">🎯 Direct Impact:</strong><br>
                                Primary problems that the project specifically targets and addresses through its main activities.
                            </div>
                            <div>
                                <strong style="color: var(--brand-accent);">🔄 Indirect Impact:</strong><br>
                                Secondary problems that are addressed as a result of solving the direct issues or through project spillover effects.
                            </div>
                        </div>
                    </div>

                    <!-- Assessment Purpose -->
                    <div style="background: var(--bg-secondary); padding: var(--spacing-lg); border-radius: var(--radius-md); border-left: 4px solid var(--brand-primary);">
                        <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                            📈 For Impact Assessment
                        </h4>
                        <div style="font-size: 0.75rem; color: var(--text-secondary); line-height: 1.5;">
                            This information helps evaluate the comprehensive impact of the project by linking activities to problem-solving outcomes for M&E assessment.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues') ?>" class="btn btn-secondary btn-mobile">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">Cancel</span>
                </a>
                <button type="submit" class="btn btn-primary btn-mobile">
                    <span class="btn-icon">🎯</span>
                    <span class="btn-text">Add Issue</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Display Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <h4 style="margin: 0 0 var(--spacing-sm) 0; font-size: 1rem;">Validation Errors</h4>
        <ul style="margin: 0; padding-left: var(--spacing-md);">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li style="font-size: 0.875rem;"><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div style="position: fixed; top: 20px; right: 20px; background: var(--brand-danger); color: white; padding: var(--spacing-md); border-radius: var(--radius-md); z-index: 1000; max-width: 400px;">
        <p style="margin: 0; font-size: 0.875rem;"><?= esc(session()->getFlashdata('error')) ?></p>
    </div>
<?php endif; ?>

<style>
/* Form styling */
.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 44px;
}

.form-input:focus {
    outline: none;
    border-width: 2px;
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-friendly button styling */
.btn-mobile {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 0.875rem;
    line-height: 1.5;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.btn-mobile .btn-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.btn-mobile .btn-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Form actions container */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

/* Enhanced select dropdowns */
select.form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Enhanced textareas */
textarea.form-input {
    resize: vertical;
    min-height: 150px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .form-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .btn-mobile {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        min-height: 48px;
    }

    .form-input {
        min-height: 48px;
        font-size: 16px;
        padding: var(--spacing-md);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .btn-mobile .btn-text {
        font-size: 0.875rem;
    }

    .form-actions {
        gap: var(--spacing-sm);
    }

    .card > div[style*="padding"] {
        padding: var(--spacing-lg) !important;
    }
}

/* Focus improvements for accessibility */
.btn-mobile:focus {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Loading state for submit button */
.btn-mobile.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-mobile.loading .btn-text::after {
    content: "...";
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}

/* Auto-hide flash messages */
.flash-message {
    animation: slideIn 0.3s ease, slideOut 0.3s ease 4.7s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// Auto-hide flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('[style*="position: fixed"][style*="top: 20px"]');
    flashMessages.forEach(function(message) {
        message.classList.add('flash-message');
        setTimeout(function() {
            message.style.display = 'none';
        }, 5000);
    });
});

// Form validation
document.querySelector('.issue-create-form').addEventListener('submit', function(e) {
    const description = document.getElementById('description').value.trim();
    const issueType = document.getElementById('issue_type').value;

    if (!description || !issueType) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
});

// Update form styling based on selection
document.getElementById('issue_type').addEventListener('change', function() {
    const value = this.value;
    const examples = document.querySelector('[style*="Examples of Issues Addressed"]').parentElement;
    
    if (value === 'direct') {
        examples.style.borderLeft = '4px solid var(--brand-secondary)';
    } else if (value === 'indirect') {
        examples.style.borderLeft = '4px solid var(--brand-accent)';
    } else {
        examples.style.borderLeft = 'none';
    }
});
</script>

<?= $this->endSection() ?>
