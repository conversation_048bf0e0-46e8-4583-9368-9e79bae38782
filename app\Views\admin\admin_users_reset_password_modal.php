<!-- Reset Password Modal Content -->
<div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 500px; width: 90%;">
    <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
        <div style="width: 50px; height: 50px; border-radius: 50%; background: var(--brand-warning); display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
            🔑
        </div>
        <div>
            <h3 style="color: var(--text-primary); margin: 0; margin-bottom: var(--spacing-xs);">
                Reset Password
            </h3>
            <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                Generate new temporary password
            </p>
        </div>
    </div>

    <!-- User Information -->
    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                <?= strtoupper(substr($user['name'], 0, 1)) ?>
            </div>
            <div>
                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                    <?= esc($user['name']) ?>
                </div>
                <div style="font-size: 0.75rem; color: var(--text-muted);">
                    @<?= esc($user['username']) ?> • <?= esc($user['email']) ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Message -->
    <div style="background: #FEF2F2; border: 1px solid var(--brand-danger); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
        <h4 style="color: var(--brand-danger); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
            ⚠️ Password Reset Confirmation
        </h4>
        <ul style="color: var(--text-secondary); font-size: 0.875rem; margin: 0; padding-left: var(--spacing-lg);">
            <li>This will generate a new 4-digit temporary password</li>
            <li>The user's current password will be invalidated</li>
            <li>The new password will be displayed for you to share</li>
            <li>The user should change this password on next login</li>
        </ul>
    </div>

    <!-- Confirmation Question -->
    <p style="color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-lg); text-align: center;">
        Are you sure you want to reset the password for <strong><?= esc($user['name']) ?></strong>?
    </p>
    
    <!-- Action Buttons -->
    <div class="d-flex gap-md justify-content-between">
        <button onclick="hideResetPasswordModal()" class="btn btn-secondary" style="flex: 1;">
            Cancel
        </button>
        <button onclick="confirmResetPassword()" class="btn btn-danger" style="flex: 1;">
            Reset Password
        </button>
    </div>
</div>
