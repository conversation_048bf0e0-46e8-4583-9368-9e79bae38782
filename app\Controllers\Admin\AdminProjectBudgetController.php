<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectBudgetItemModel;

/**
 * Admin Project Budget Controller
 * 
 * Handles CRUD operations for project budget items in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectBudgetController extends BaseController
{
    protected $projectModel;
    protected $projectBudgetItemModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectBudgetItemModel = new ProjectBudgetItemModel();
    }

    /**
     * Show budget list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get budget items for this project
        $budgetItems = $this->projectBudgetItemModel->getByProject($projectId, false);

        // Get budget statistics
        $budgetStats = $this->projectBudgetItemModel->getBudgetStatistics($projectId);

        $data = [
            'title' => 'Project Budget - PROMIS Admin',
            'page_title' => 'Project Budget',
            'project' => $project,
            'budgetItems' => $budgetItems,
            'budgetStats' => $budgetStats
        ];

        return view('admin/admin_projects_budgets_list', $data);
    }

    /**
     * Show create budget form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Create Budget Item - PROMIS Admin',
            'page_title' => 'Create Budget Item',
            'project' => $project
        ];

        return view('admin/admin_projects_budgets_create', $data);
    }

    /**
     * Store budget item - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'item_code' => 'required|max_length[30]',
            'description' => 'required|max_length[255]',
            'amount_planned' => 'required|decimal|greater_than[0]',
            'status' => 'permit_empty|in_list[active,removed]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check if item code already exists for this project
        $itemCode = $this->request->getPost('item_code');
        if ($this->projectBudgetItemModel->itemCodeExists($projectId, $itemCode)) {
            return redirect()->back()->withInput()->with('error', 'Item code already exists for this project.');
        }

        // Prepare data
        $budgetData = [
            'project_id' => $projectId,
            'item_code' => $itemCode,
            'description' => $this->request->getPost('description'),
            'amount_planned' => $this->request->getPost('amount_planned'),
            'status' => $this->request->getPost('status') ?: 'active',
            'created_by' => $adminUserId
        ];

        try {
            $budgetId = $this->projectBudgetItemModel->insert($budgetData);

            if ($budgetId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                               ->with('success', 'Budget item created successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectBudgetItemModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating budget item: ' . $e->getMessage());
        }
    }

    /**
     * Show edit budget form - GET request
     */
    public function edit($projectId, $budgetId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get budget item
        $budgetItem = $this->projectBudgetItemModel->where('id', $budgetId)
                                                  ->where('project_id', $projectId)
                                                  ->where('deleted_at', null)
                                                  ->first();

        if (!$budgetItem) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                           ->with('error', 'Budget item not found.');
        }

        $data = [
            'title' => 'Edit Budget Item - PROMIS Admin',
            'page_title' => 'Edit Budget Item',
            'project' => $project,
            'budgetItem' => $budgetItem
        ];

        return view('admin/admin_projects_budgets_edit', $data);
    }

    /**
     * Update budget item - POST request
     */
    public function update($projectId, $budgetId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get budget item
        $budgetItem = $this->projectBudgetItemModel->where('id', $budgetId)
                                                  ->where('project_id', $projectId)
                                                  ->where('deleted_at', null)
                                                  ->first();

        if (!$budgetItem) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                           ->with('error', 'Budget item not found.');
        }

        // Validation rules
        $rules = [
            'item_code' => 'required|max_length[30]',
            'description' => 'required|max_length[255]',
            'amount_planned' => 'required|decimal|greater_than[0]',
            'status' => 'permit_empty|in_list[active,removed]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check if item code already exists for this project (excluding current item)
        $itemCode = $this->request->getPost('item_code');
        if ($this->projectBudgetItemModel->itemCodeExists($projectId, $itemCode, $budgetId)) {
            return redirect()->back()->withInput()->with('error', 'Item code already exists for this project.');
        }

        // Prepare data
        $budgetData = [
            'item_code' => $itemCode,
            'description' => $this->request->getPost('description'),
            'amount_planned' => $this->request->getPost('amount_planned'),
            'status' => $this->request->getPost('status') ?: 'active',
            'updated_by' => $adminUserId
        ];

        try {
            $result = $this->projectBudgetItemModel->update($budgetId, $budgetData);

            if ($result) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                               ->with('success', 'Budget item updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectBudgetItemModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating budget item: ' . $e->getMessage());
        }
    }

    /**
     * Delete budget item - POST request
     */
    public function delete($projectId, $budgetId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get budget item
        $budgetItem = $this->projectBudgetItemModel->where('id', $budgetId)
                                                  ->where('project_id', $projectId)
                                                  ->where('deleted_at', null)
                                                  ->first();

        if (!$budgetItem) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                           ->with('error', 'Budget item not found.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectBudgetItemModel->update($budgetId, ['deleted_by' => $adminUserId]);
            $this->projectBudgetItemModel->delete($budgetId);

            return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                           ->with('success', 'Budget item deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/budgets'))
                           ->with('error', 'Error deleting budget item: ' . $e->getMessage());
        }
    }
}
