<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'PROMIS Admin Portal' ?></title>

    <!-- Bootstrap 5.3.x CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/system_images/favicon.ico') ?>">
    <style>
        :root {
            /* Professional Light Theme Color Palette */
            --promis-bg-primary: #FFFFFF;
            --promis-bg-secondary: #F8FAFC;
            --promis-bg-tertiary: #F1F5F9;
            --promis-bg-accent: #EFF6FF;

            /* Surface Colors */
            --promis-surface-card: #FFFFFF;
            --promis-surface-card-hover: #F8FAFC;
            --promis-surface-overlay: rgba(255, 255, 255, 0.95);
            --promis-surface-sidebar: #1E293B;
            --promis-surface-header: #FFFFFF;

            /* Professional Gradients */
            --promis-gradient-primary: linear-gradient(135deg, #3B82F6, #1D4ED8);
            --promis-gradient-secondary: linear-gradient(135deg, #10B981, #059669);
            --promis-gradient-accent: linear-gradient(135deg, #8B5CF6, #7C3AED);

            /* Text Colors */
            --promis-text-primary: #1E293B;
            --promis-text-secondary: #475569;
            --promis-text-tertiary: #64748B;
            --promis-text-muted: #94A3B8;
            --promis-text-white: #FFFFFF;
            --promis-text-sidebar: #E2E8F0;
            --promis-text-sidebar-muted: #94A3B8;

            /* Brand Colors */
            --promis-brand-primary: #3B82F6;
            --promis-brand-secondary: #10B981;
            --promis-brand-accent: #8B5CF6;
            --promis-brand-danger: #EF4444;
            --promis-brand-warning: #F59E0B;

            /* Typography */
            --promis-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

            /* Layout */
            --promis-sidebar-width: 280px;
            --promis-sidebar-collapsed-width: 80px;
            --promis-header-height: 70px;
        }

        body {
            font-family: var(--promis-font-primary);
            background-color: var(--promis-bg-secondary);
            color: var(--promis-text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Layout Structure */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar - Standard Feature */
        .promis-sidebar {
            width: var(--promis-sidebar-width);
            background-color: var(--promis-surface-sidebar);
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1030;
            transition: all 0.3s ease;
            overflow-y: auto;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .promis-sidebar.collapsed {
            width: var(--promis-sidebar-collapsed-width);
        }

        .promis-sidebar-header {
            padding: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .promis-sidebar.collapsed .promis-sidebar-header {
            padding: 1.5rem 1rem;
        }

        .promis-sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--promis-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .promis-sidebar.collapsed .promis-sidebar-logo {
            font-size: 1rem;
        }

        .promis-sidebar-nav {
            padding: 1.5rem;
        }

        .promis-nav-item {
            margin-bottom: 0.5rem;
        }

        .promis-nav-link {
            display: flex;
            align-items: center;
            padding: 1rem;
            color: var(--promis-text-sidebar-muted);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .promis-nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--promis-text-sidebar);
        }

        .promis-nav-link.active {
            background: var(--promis-gradient-primary);
            color: var(--promis-text-white);
        }

        .promis-nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 1rem;
            flex-shrink: 0;
            font-size: 1.125rem;
        }

        .promis-sidebar.collapsed .promis-nav-text {
            display: none;
        }

        .promis-sidebar.collapsed .promis-nav-icon {
            margin-right: 0;
        }

        /* Main Content */
        .promis-main-content {
            flex: 1;
            margin-left: var(--promis-sidebar-width);
            transition: all 0.3s ease;
        }

        .promis-sidebar.collapsed + .promis-main-content {
            margin-left: var(--promis-sidebar-collapsed-width);
        }

        /* Header - Standard Feature */
        .promis-header {
            height: var(--promis-header-height);
            background-color: var(--promis-surface-header);
            border-bottom: 1px solid #E5E7EB;
            position: sticky;
            top: 0;
            z-index: 1020;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .promis-sidebar-toggle {
            background: none;
            border: none;
            color: var(--promis-text-secondary);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            min-height: 40px;
        }

        .promis-sidebar-toggle:hover {
            background-color: var(--promis-bg-tertiary);
            color: var(--promis-text-primary);
        }

        .promis-sidebar-toggle:active {
            transform: scale(0.95);
        }

        .promis-page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--promis-text-primary);
            margin: 0;
        }

        .promis-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--promis-gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--promis-text-white);
        }

        .promis-user-avatar:hover {
            box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        /* Content Area */
        .promis-content {
            padding: 2rem;
            min-height: calc(100vh - var(--promis-header-height));
        }

        /* Flash Messages - Bootstrap Enhanced */
        .promis-flash-message {
            border-left: 4px solid;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
        }

        /* Custom Card Hover Effects */
        .promis-card-hover {
            transition: all 0.3s ease;
        }

        .promis-card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }

        /* Custom Button Enhancements */
        .promis-btn-gradient {
            background: var(--promis-gradient-primary);
            border: none;
            color: var(--promis-text-white);
            transition: all 0.3s ease;
        }

        .promis-btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            color: var(--promis-text-white);
        }

        /* Form Input Enhancements */
        .promis-form-required {
            border-color: #dc3545 !important;
        }

        .promis-form-optional {
            border-color: #198754 !important;
        }

        .promis-form-input:focus {
            border-color: var(--promis-brand-primary);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        /* Mobile Backdrop */
        .promis-mobile-backdrop {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1025;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .promis-mobile-backdrop.active {
            display: block;
            opacity: 1;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .promis-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                width: 280px;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                z-index: 1030;
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            }

            .promis-sidebar.mobile-open {
                transform: translateX(0);
            }

            .promis-main-content {
                margin-left: 0;
                width: 100%;
            }

            .promis-content {
                padding: 1rem;
            }

            .promis-sidebar-toggle {
                display: block !important;
            }

            /* Ensure sidebar content is scrollable on mobile */
            .promis-sidebar-nav {
                height: calc(100vh - 80px);
                overflow-y: auto;
            }

            .promis-page-title {
                font-size: 1.25rem;
            }
        }

        /* Hide backdrop on desktop */
        @media (min-width: 769px) {
            .promis-mobile-backdrop {
                display: none !important;
            }
        }
    </style>
    <?= isset($additional_css) ? $additional_css : '' ?>
</head>
<body>
    <!-- Mobile Backdrop -->
    <div class="promis-mobile-backdrop" id="mobileBackdrop"></div>

    <div class="admin-layout">
        <!-- Sidebar - Standard across all admin pages -->
        <aside class="promis-sidebar" id="sidebar">
            <div class="promis-sidebar-header">
                <div class="promis-sidebar-logo">PROMIS</div>
            </div>
            <nav class="promis-sidebar-nav">
                <!-- Standard Navigation Items -->
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/dashboard') ?>" class="promis-nav-link <?= (current_url() == base_url('admin/dashboard')) ? 'active' : '' ?>">
                        <i class="bi bi-speedometer2 promis-nav-icon"></i>
                        <span class="promis-nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/projects') ?>" class="promis-nav-link <?= (strpos(current_url(), 'admin/projects') !== false) ? 'active' : '' ?>">
                        <i class="bi bi-folder promis-nav-icon"></i>
                        <span class="promis-nav-text">Projects</span>
                    </a>
                </div>
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/users') ?>" class="promis-nav-link <?= (strpos(current_url(), 'admin/users') !== false) ? 'active' : '' ?>">
                        <i class="bi bi-people promis-nav-icon"></i>
                        <span class="promis-nav-text">Users</span>
                    </a>
                </div>
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/contractors') ?>" class="promis-nav-link <?= (strpos(current_url(), 'admin/contractors') !== false) ? 'active' : '' ?>">
                        <i class="bi bi-building promis-nav-icon"></i>
                        <span class="promis-nav-text">Contractors</span>
                    </a>
                </div>
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/organizations') ?>" class="promis-nav-link <?= (strpos(current_url(), 'admin/organizations') !== false) ? 'active' : '' ?>">
                        <i class="bi bi-buildings promis-nav-icon"></i>
                        <span class="promis-nav-text">Organizations</span>
                    </a>
                </div>
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/reports') ?>" class="promis-nav-link <?= (strpos(current_url(), 'admin/reports') !== false) ? 'active' : '' ?>">
                        <i class="bi bi-graph-up promis-nav-icon"></i>
                        <span class="promis-nav-text">Reports</span>
                    </a>
                </div>
                <div class="promis-nav-item">
                    <a href="<?= base_url('admin/settings') ?>" class="promis-nav-link <?= (strpos(current_url(), 'admin/settings') !== false) ? 'active' : '' ?>">
                        <i class="bi bi-gear promis-nav-icon"></i>
                        <span class="promis-nav-text">Settings</span>
                    </a>
                </div>

                <!-- Logout - Always at bottom -->
                <div class="promis-nav-item" style="margin-top: auto; border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 1rem;">
                    <a href="<?= base_url('auth/logout') ?>" class="promis-nav-link">
                        <i class="bi bi-box-arrow-right promis-nav-icon"></i>
                        <span class="promis-nav-text">Logout</span>
                    </a>
                </div>

                <!-- User Info - Standard across all pages -->
                <div class="promis-nav-item mt-3 pt-3" style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <div class="d-flex align-items-center gap-2 px-3">
                        <div class="rounded-circle d-flex align-items-center justify-content-center fw-bold text-white"
                             style="width: 32px; height: 32px; background: var(--promis-gradient-secondary); font-size: 0.875rem;">
                            <?= strtoupper(substr(session()->get('admin_username') ?? 'A', 0, 1)) ?>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold text-light" style="font-size: 0.875rem;">
                                <?= esc(session()->get('admin_username') ?? 'Admin User') ?>
                            </div>
                            <div class="text-muted text-uppercase" style="font-size: 0.75rem; letter-spacing: 0.05em;">
                                <?= esc(session()->get('admin_role') ?? 'Administrator') ?>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="promis-main-content">
            <!-- Header - Standard across all admin pages -->
            <header class="promis-header d-flex align-items-center justify-content-between px-4">
                <div class="d-flex align-items-center">
                    <button class="promis-sidebar-toggle me-3" id="sidebarToggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div>
                        <h1 class="promis-page-title"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h1>
                        <?php if (session()->get('admin_organization_name')): ?>
                            <div class="text-muted" style="font-size: 0.75rem; margin-top: -0.25rem;">
                                <?= esc(session()->get('admin_organization_name')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <!-- Header actions section - for page-specific buttons/actions -->
                    <?= $this->renderSection('header_actions') ?>
                    <div class="dropdown">
                        <div class="promis-user-avatar" data-bs-toggle="dropdown" aria-expanded="false"
                             title="<?= session()->get('admin_username') ?? 'Admin User' ?>">
                            <?= strtoupper(substr(session()->get('admin_username') ?? 'A', 0, 1)) ?>
                        </div>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?= base_url('admin/profile') ?>">
                                <i class="bi bi-person me-2"></i>Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="promis-content">
                <!-- Flash Messages - Bootstrap Alerts -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show promis-flash-message" role="alert">
                        <i class="bi bi-check-circle me-2"></i>
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show promis-flash-message" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('warning')): ?>
                    <div class="alert alert-warning alert-dismissible fade show promis-flash-message" role="alert">
                        <i class="bi bi-exclamation-circle me-2"></i>
                        <?= session()->getFlashdata('warning') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger alert-dismissible fade show promis-flash-message" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <ul class="mb-0 ps-3">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Page Content - This is where individual pages render their content -->
                <?= $this->renderSection('content') ?>
            </div>
        </main>
    </div>

    <!-- Bootstrap 5.3.x JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Custom JavaScript -->
    <script>
        // Sidebar Toggle Functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const backdrop = document.getElementById('mobileBackdrop');

            // Check if we're on mobile
            if (window.innerWidth <= 768) {
                // Mobile: toggle mobile-open class and backdrop
                sidebar.classList.toggle('mobile-open');
                backdrop.classList.toggle('active');

                // Prevent body scroll when sidebar is open
                if (sidebar.classList.contains('mobile-open')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            } else {
                // Desktop: toggle collapsed class
                sidebar.classList.toggle('collapsed');
                // Store preference in localStorage
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            }
        });

        // Restore sidebar state on page load
        document.addEventListener('DOMContentLoaded', function() {
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
            }
        });

        // Close mobile sidebar when clicking backdrop
        document.getElementById('mobileBackdrop').addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const backdrop = document.getElementById('mobileBackdrop');

                sidebar.classList.remove('mobile-open');
                backdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !toggle.contains(e.target) &&
                sidebar.classList.contains('mobile-open')) {

                const backdrop = document.getElementById('mobileBackdrop');
                sidebar.classList.remove('mobile-open');
                backdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Handle window resize - close mobile sidebar if switching to desktop
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            const backdrop = document.getElementById('mobileBackdrop');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('mobile-open');
                backdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Auto-hide flash messages after 5 seconds (Bootstrap alerts auto-dismiss)
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.promis-flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    const alert = new bootstrap.Alert(message);
                    alert.close();
                }, 5000);
            });
        });

        // Initialize Bootstrap tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>

    <!-- Additional JavaScript section for page-specific scripts -->
    <?= isset($additional_js) ? $additional_js : '' ?>
</body>
</html>
