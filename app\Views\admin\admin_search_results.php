<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-search me-2"></i>
            Global Search
        </h1>
        <p class="text-muted mb-0">
            Search across users, organizations, and projects
        </p>
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-search me-2"></i>
            Search
        </h5>
    </div>
    <div class="card-body">
        <form method="get" action="<?= base_url('admin/search') ?>" class="search-form">
            <div class="d-flex gap-3 align-items-end">

                <!-- Search Query -->
                <div class="flex-grow-1">
                    <label for="search_query" class="form-label fw-semibold">Search Query</label>
                    <input type="text" id="search_query" name="q" class="form-control"
                           placeholder="Enter search terms..." value="<?= esc($query) ?>" autofocus>
                </div>

                <!-- Module Filter -->
                <div style="min-width: 150px;">
                    <label for="module" class="form-label fw-semibold">Module</label>
                    <select name="module" id="module" class="form-select">
                        <option value="">All Modules</option>
                        <option value="users" <?= ($module === 'users') ? 'selected' : '' ?>>Users</option>
                        <option value="organizations" <?= ($module === 'organizations') ? 'selected' : '' ?>>Organizations</option>
                        <option value="projects" <?= ($module === 'projects') ? 'selected' : '' ?>>Projects</option>
                    </select>
                </div>

                <!-- Search Button -->
                <div>
                    <button type="submit" class="btn btn-primary">
                        🔍 Search
                    </button>
                </div>
            </div>
        </form>

        <!-- Save Search -->
        <?php if (!empty($query)): ?>
            <div style="margin-top: var(--spacing-lg); padding-top: var(--spacing-lg); border-top: 1px solid #E5E7EB;">
                <form method="post" action="<?= base_url('admin/search/save') ?>" style="display: flex; gap: var(--spacing-md); align-items-end;">
                    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                    <input type="hidden" name="search_query" value="<?= esc($query) ?>" />
                    <input type="hidden" name="search_module" value="<?= esc($module) ?>" />
                    
                    <div style="flex: 1;">
                        <label for="search_name" class="form-label">Save this search</label>
                        <input 
                            type="text" 
                            id="search_name"
                            name="search_name" 
                            class="form-input" 
                            placeholder="Enter search name..." 
                        >
                    </div>
                    
                    <div>
                        <button type="submit" class="btn btn-secondary">
                            💾 Save
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Saved Searches -->
<?php 
$savedSearches = session()->get('admin_saved_searches') ?: [];
if (!empty($savedSearches)): 
?>
<div class="card mb-xl">
    <div class="card-header">
        Saved Searches
    </div>
    <div style="padding: var(--spacing-lg);">
        <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-md);">
            <?php foreach ($savedSearches as $savedSearch): ?>
                <div style="display: flex; align-items: center; gap: var(--spacing-sm); background: var(--bg-tertiary); padding: var(--spacing-sm) var(--spacing-md); border-radius: var(--radius-md);">
                    <a href="<?= base_url('admin/search/saved/' . $savedSearch['id']) ?>" style="color: var(--brand-primary); text-decoration: none; font-weight: 500;">
                        <?= esc($savedSearch['name']) ?>
                    </a>
                    <form method="post" action="<?= base_url('admin/search/saved/' . $savedSearch['id']) ?>" style="display: inline;">
                        <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                        <input type="hidden" name="_method" value="DELETE" />
                        <button type="submit" style="background: none; border: none; color: var(--text-muted); cursor: pointer; padding: 0;" onclick="return confirm('Delete this saved search?')" title="Delete">
                            ✕
                        </button>
                    </form>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Search Results -->
<?php if (!empty($query)): ?>
    
    <!-- Results Statistics -->
    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
        
        <div class="card">
            <div style="text-align: center;">
                <h3 style="color: var(--text-primary); font-size: 1.5rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['total_results']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Total Results
                </p>
            </div>
        </div>

        <div class="card">
            <div style="text-align: center;">
                <h3 style="color: var(--brand-primary); font-size: 1.25rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['users_count']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Users
                </p>
            </div>
        </div>

        <div class="card">
            <div style="text-align: center;">
                <h3 style="color: var(--brand-secondary); font-size: 1.25rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['organizations_count']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Organizations
                </p>
            </div>
        </div>

        <div class="card">
            <div style="text-align: center;">
                <h3 style="color: var(--brand-accent); font-size: 1.25rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['projects_count']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Projects
                </p>
            </div>
        </div>
    </div>

    <!-- Results Sections -->
    <div style="display: grid; grid-template-columns: 1fr; gap: var(--spacing-xl);">
        
        <!-- Users Results -->
        <?php if (!empty($results['users'])): ?>
            <div class="card">
                <div class="card-header">
                    👥 Users (<?= count($results['users']) ?> found)
                </div>
                <div style="padding: var(--spacing-lg);">
                    <?php foreach ($results['users'] as $user): ?>
                        <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md) 0; border-bottom: 1px solid #E5E7EB;">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                <?= strtoupper(substr($user['name'], 0, 1)) ?>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc($user['name']) ?>
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    @<?= esc($user['username']) ?> • <?= esc($user['email']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= esc($user['org_name'] ?? 'No Organization') ?> • <?= esc(ucfirst($user['role'])) ?>
                                </div>
                            </div>
                            <div>
                                <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    View
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Organizations Results -->
        <?php if (!empty($results['organizations'])): ?>
            <div class="card">
                <div class="card-header">
                    🏢 Organizations (<?= count($results['organizations']) ?> found)
                </div>
                <div style="padding: var(--spacing-lg);">
                    <?php foreach ($results['organizations'] as $org): ?>
                        <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md) 0; border-bottom: 1px solid #E5E7EB;">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                <?= strtoupper(substr($org['name'], 0, 1)) ?>
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc($org['name']) ?>
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    Code: <?= esc($org['org_code']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= esc(substr($org['description'] ?? '', 0, 100)) ?>...
                                </div>
                            </div>
                            <div>
                                <span class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    View
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Projects Results -->
        <?php if (!empty($results['projects'])): ?>
            <div class="card">
                <div class="card-header">
                    📁 Projects (<?= count($results['projects']) ?> found)
                </div>
                <div style="padding: var(--spacing-lg);">
                    <?php foreach ($results['projects'] as $project): ?>
                        <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md) 0; border-bottom: 1px solid #E5E7EB;">
                            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-accent); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                📁
                            </div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    <?= esc($project['title']) ?>
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    Code: <?= esc($project['pro_code']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= esc($project['org_name'] ?? 'No Organization') ?> • <?= esc(ucfirst($project['status'] ?? 'Unknown')) ?>
                                </div>
                            </div>
                            <div>
                                <span class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    View
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- No Results -->
        <?php if ($stats['total_results'] === 0): ?>
            <div class="card">
                <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🔍</div>
                    <p style="font-size: 1.125rem; margin-bottom: var(--spacing-md);">No results found</p>
                    <p>Try different search terms or check your spelling.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

<?php else: ?>
    
    <!-- Search Instructions -->
    <div class="card">
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-lg);">🔍</div>
            <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Global Search</h3>
            <p style="font-size: 1.125rem; margin-bottom: var(--spacing-md);">Search across all modules in the PROMIS system</p>
            <ul style="text-align: left; display: inline-block; color: var(--text-secondary);">
                <li>Search users by name, username, or email</li>
                <li>Find organizations by name, code, or description</li>
                <li>Locate projects by title, code, or description</li>
                <li>Use filters to narrow down results</li>
                <li>Save frequently used searches</li>
            </ul>
        </div>
    </div>

<?php endif; ?>

<?= $this->endSection() ?>
