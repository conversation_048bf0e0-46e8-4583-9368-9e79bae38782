<?= $this->extend('templates/dakoii_public_template') ?>

<?= $this->section('content') ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-error">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-error">
        <ul style="margin: 0; padding-left: 20px;">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form action="<?= base_url('dakoii/authenticate') ?>" method="post" id="loginForm">
    <?= csrf_field() ?>
    
    <div class="form-group">
        <label for="identifier" class="form-label">Email or Username</label>
        <input 
            type="text" 
            id="identifier" 
            name="identifier" 
            class="form-input" 
            placeholder="Enter your email or username"
            value="<?= old('identifier') ?>"
            required
            autocomplete="username"
        >
    </div>

    <div class="form-group">
        <label for="password" class="form-label">Password</label>
        <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            placeholder="Enter your password"
            required
            autocomplete="current-password"
        >
    </div>

    <div class="form-group" style="display: flex; align-items: center; margin-bottom: var(--spacing-lg);">
        <input 
            type="checkbox" 
            id="remember_me" 
            name="remember_me" 
            value="1"
            style="margin-right: var(--spacing-sm);"
        >
        <label for="remember_me" style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">
            Remember me for 30 days
        </label>
    </div>

    <button type="submit" class="btn btn-primary" id="loginBtn">
        <span id="loginText">Sign In</span>
        <span id="loginLoader" class="loading" style="display: none;"></span>
    </button>

    <div class="forgot-password">
        <a href="<?= base_url('dakoii/password-reset') ?>">Forgot your password?</a>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const loginText = document.getElementById('loginText');
    const loginLoader = document.getElementById('loginLoader');

    loginForm.addEventListener('submit', function(e) {
        // Show loading state
        loginBtn.disabled = true;
        loginText.style.display = 'none';
        loginLoader.style.display = 'inline-block';
        
        // Re-enable after 10 seconds as fallback
        setTimeout(function() {
            loginBtn.disabled = false;
            loginText.style.display = 'inline';
            loginLoader.style.display = 'none';
        }, 10000);
    });

    // Auto-focus on first empty field
    const identifier = document.getElementById('identifier');
    const password = document.getElementById('password');
    
    if (!identifier.value) {
        identifier.focus();
    } else if (!password.value) {
        password.focus();
    }

    // Enter key handling
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !loginBtn.disabled) {
            loginForm.submit();
        }
    });
});
</script>

<style>
/* Additional styles for login form */
.form-group {
    position: relative;
}

.form-input:focus {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    box-shadow: none !important;
}

/* Checkbox styling */
input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-sm);
    background: var(--glass-bg);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

input[type="checkbox"]:checked {
    background: var(--gradient-primary);
    border-color: transparent;
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

input[type="checkbox"]:focus {
    outline: 2px solid rgba(0, 212, 255, 0.3);
    outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .login-card {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
    }
    
    .form-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Loading animation enhancement */
.loading {
    border-top-color: var(--text-primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form validation styles */
.form-input:invalid {
    border-color: rgba(255, 0, 110, 0.5);
}

.form-input:valid {
    border-color: rgba(6, 255, 165, 0.5);
}

/* Alert message animations */
.alert {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus trap for accessibility */
.login-card:focus-within {
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}
</style>

<?= $this->endSection() ?>
