<?php

namespace App\Models;

/**
 * Project Officer Model
 * 
 * Handles many-to-many relationship between projects and officers
 * with role assignments and activity tracking.
 */
class ProjectOfficerModel extends BaseModel
{
    protected $table      = 'project_officers';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'user_id', 'role', 'is_active',
        'removal_reason', 'created_by', 'deleted_by'
    ];
    
    protected $useTimestamps = false; // Custom timestamp handling
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id' => 'required|integer',
        'user_id'    => 'required|integer',
        'role'       => 'required|in_list[lead,certifier,support]',
        'is_active'  => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'user_id' => [
            'required' => 'User ID is required'
        ],
        'role' => [
            'required' => 'Role is required',
            'in_list' => 'Role must be lead, certifier, or support'
        ]
    ];
    
    /**
     * Get officers by project
     */
    public function getByProject(int $projectId, bool $activeOnly = true): array
    {
        $query = $this->select('project_officers.*, users.username, users.name, users.email')
                     ->join('users', 'users.id = project_officers.user_id')
                     ->where('project_officers.project_id', $projectId);
        
        if ($activeOnly) {
            $query = $query->where('project_officers.is_active', 1);
        }
        
        return $query->orderBy('project_officers.role', 'ASC')
                    ->orderBy('project_officers.created_at', 'ASC')
                    ->findAll();
    }
    
    /**
     * Get projects by officer
     */
    public function getByOfficer(int $userId, bool $activeOnly = true): array
    {
        $query = $this->select('project_officers.*, projects.title as project_title, projects.pro_code, projects.status as project_status')
                     ->join('projects', 'projects.id = project_officers.project_id')
                     ->where('project_officers.user_id', $userId);
        
        if ($activeOnly) {
            $query = $query->where('project_officers.is_active', 1);
        }
        
        return $query->orderBy('project_officers.created_at', 'DESC')->findAll();
    }
    
    /**
     * Get officers by role
     */
    public function getByRole(string $role, bool $activeOnly = true): array
    {
        $query = $this->select('project_officers.*, users.username, users.name, projects.title as project_title')
                     ->join('users', 'users.id = project_officers.user_id')
                     ->join('projects', 'projects.id = project_officers.project_id')
                     ->where('project_officers.role', $role);
        
        if ($activeOnly) {
            $query = $query->where('project_officers.is_active', 1);
        }
        
        return $query->orderBy('project_officers.created_at', 'DESC')->findAll();
    }
    
    /**
     * Add officer to project
     */
    public function addOfficer(int $projectId, int $userId, string $role, ?int $createdBy = null): bool
    {
        // Check if user is already assigned to this project with the same role
        $existing = $this->where('project_id', $projectId)
                        ->where('user_id', $userId)
                        ->where('role', $role)
                        ->where('is_active', 1)
                        ->first();
        
        if ($existing) {
            return false; // Already assigned with this role
        }
        
        $data = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'role' => $role,
            'is_active' => 1,
            'created_by' => $createdBy,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->insert($data) !== false;
    }
    
    /**
     * Remove officer from project
     */
    public function removeOfficer(int $projectId, int $userId, string $role, string $reason, ?int $deletedBy = null): bool
    {
        $assignment = $this->where('project_id', $projectId)
                          ->where('user_id', $userId)
                          ->where('role', $role)
                          ->where('is_active', 1)
                          ->first();
        
        if (!$assignment) {
            return false;
        }
        
        return $this->update($assignment['id'], [
            'is_active' => 0,
            'removal_reason' => $reason,
            'deleted_by' => $deletedBy,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Get project lead officer
     */
    public function getProjectLead(int $projectId): ?array
    {
        return $this->select('project_officers.*, users.username, users.name, users.email')
                   ->join('users', 'users.id = project_officers.user_id')
                   ->where('project_officers.project_id', $projectId)
                   ->where('project_officers.role', 'lead')
                   ->where('project_officers.is_active', 1)
                   ->first();
    }
    
    /**
     * Get project certifiers
     */
    public function getProjectCertifiers(int $projectId): array
    {
        return $this->select('project_officers.*, users.username, users.name, users.email')
                   ->join('users', 'users.id = project_officers.user_id')
                   ->where('project_officers.project_id', $projectId)
                   ->where('project_officers.role', 'certifier')
                   ->where('project_officers.is_active', 1)
                   ->findAll();
    }
    
    /**
     * Check if user has role in project
     */
    public function hasRole(int $projectId, int $userId, string $role): bool
    {
        return $this->where('project_id', $projectId)
                   ->where('user_id', $userId)
                   ->where('role', $role)
                   ->where('is_active', 1)
                   ->countAllResults() > 0;
    }
    
    /**
     * Get officer statistics
     */
    public function getOfficerStatistics(): array
    {
        $stats = [];
        
        // Active assignments
        $stats['active_assignments'] = $this->where('is_active', 1)->countAllResults();
        
        // Role distribution
        $roleCounts = $this->select('role, COUNT(*) as count')
                          ->where('is_active', 1)
                          ->groupBy('role')
                          ->findAll();
        
        $stats['by_role'] = array_column($roleCounts, 'count', 'role');
        
        // Most active officers
        $stats['most_active'] = $this->select('user_id, COUNT(*) as project_count, users.username, users.name')
                                    ->join('users', 'users.id = project_officers.user_id')
                                    ->where('is_active', 1)
                                    ->groupBy('user_id')
                                    ->orderBy('project_count', 'DESC')
                                    ->limit(10)
                                    ->findAll();
        
        return $stats;
    }
    
    /**
     * Get officer assignment history
     */
    public function getAssignmentHistory(int $userId): array
    {
        return $this->select('project_officers.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_officers.project_id')
                   ->where('project_officers.user_id', $userId)
                   ->orderBy('project_officers.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Transfer officer role
     */
    public function transferRole(int $projectId, int $fromUserId, int $toUserId, string $role, ?int $transferredBy = null): bool
    {
        $this->db->transStart();
        
        // Remove old assignment
        $this->removeOfficer($projectId, $fromUserId, $role, 'Role transferred', $transferredBy);
        
        // Add new assignment
        $this->addOfficer($projectId, $toUserId, $role, $transferredBy);
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }
}
