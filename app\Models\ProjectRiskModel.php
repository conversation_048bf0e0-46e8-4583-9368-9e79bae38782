<?php

namespace App\Models;

/**
 * Project Risk Model
 * 
 * Handles project risk management with impact and likelihood assessment.
 */
class ProjectRiskModel extends BaseModel
{
    protected $table      = 'project_risks';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'risk_type', 'risk_level', 'description',
        'mitigation', 'approval_status', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'        => 'required|integer',
        'milestone_id'      => 'permit_empty|integer',
        'risk_type'         => 'required|in_list[proposed,foreseen,witnessed]',
        'risk_level'        => 'required|in_list[low,medium,high,critical]',
        'description'       => 'required',
        'approval_status'   => 'in_list[pending,approved,rejected]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'risk_type' => [
            'required' => 'Risk type is required'
        ],
        'risk_level' => [
            'required' => 'Risk level is required'
        ],
        'description' => [
            'required' => 'Risk description is required'
        ]
    ];
    
    /**
     * Get risks by project
     */
    public function getByProject(int $projectId, ?string $status = null): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get risks by approval status
     */
    public function getByApprovalStatus(string $approvalStatus): array
    {
        return $this->select('project_risks.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_risks.project_id')
                   ->where('project_risks.approval_status', $approvalStatus)
                   ->orderBy('project_risks.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get pending and approved risks
     */
    public function getActiveRisks(?int $projectId = null): array
    {
        $query = $this->whereIn('approval_status', ['pending', 'approved']);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('risk_level', 'DESC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get high priority risks
     */
    public function getHighPriorityRisks(?int $projectId = null): array
    {
        $query = $this->groupStart()
                     ->where('risk_level', 'critical')
                     ->orWhere('risk_level', 'high')
                     ->groupEnd()
                     ->whereIn('approval_status', ['pending', 'approved']);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('risk_level', 'DESC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }
    
    /**
     * Calculate risk score based on risk level
     */
    public function calculateRiskScore(string $riskLevel): int
    {
        $riskScores = [
            'low' => 1,
            'medium' => 2,
            'high' => 3,
            'critical' => 4
        ];
        
        return $riskScores[$riskLevel] ?? 1;
    }
    
    /**
     * Get risk matrix by risk level and type
     */
    public function getRiskMatrix(int $projectId): array
    {
        $risks = $this->getByProject($projectId);
        $matrix = [];
        
        foreach ($risks as $risk) {
            $score = $this->calculateRiskScore($risk['risk_level']);
            $risk['risk_score'] = $score;
            
            if (!isset($matrix[$risk['risk_level']])) {
                $matrix[$risk['risk_level']] = [];
            }
            
            if (!isset($matrix[$risk['risk_level']][$risk['risk_type']])) {
                $matrix[$risk['risk_level']][$risk['risk_type']] = [];
            }
            
            $matrix[$risk['risk_level']][$risk['risk_type']][] = $risk;
        }
        
        return $matrix;
    }
    
    /**
     * Update risk approval status
     */
    public function updateApprovalStatus(int $riskId, string $approvalStatus, ?int $updatedBy = null): bool
    {
        return $this->update($riskId, [
            'approval_status' => $approvalStatus,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Update mitigation strategy
     */
    public function updateMitigation(int $riskId, string $mitigation, ?int $updatedBy = null): bool
    {
        return $this->update($riskId, [
            'mitigation' => $mitigation,
            'updated_by' => $updatedBy
        ]);
    }
    
    /**
     * Get risk statistics
     */
    public function getRiskStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total risks
        $stats['total_risks'] = $query->countAllResults();
        
        // Risks by approval status
        $statusCounts = $this->select('approval_status, COUNT(*) as count')
                            ->groupBy('approval_status')
                            ->findAll();
        
        $stats['by_approval_status'] = array_column($statusCounts, 'count', 'approval_status');
        
        // Risks by risk level
        $riskLevelCounts = $this->select('risk_level, COUNT(*) as count')
                               ->groupBy('risk_level')
                               ->findAll();
        
        $stats['by_risk_level'] = array_column($riskLevelCounts, 'count', 'risk_level');
        
        // Risks by type
        $typeCounts = $this->select('risk_type, COUNT(*) as count')
                          ->groupBy('risk_type')
                          ->findAll();
        
        $stats['by_risk_type'] = array_column($typeCounts, 'count', 'risk_type');
        
        // Pending and approved risks count
        $activeQuery = $this->whereIn('approval_status', ['pending', 'approved']);
        if ($projectId) {
            $activeQuery = $activeQuery->where('project_id', $projectId);
        }
        $stats['active_risks'] = $activeQuery->countAllResults();
        
        // High priority risks count
        $highPriorityQuery = $this->whereIn('risk_level', ['high', 'critical'])
                                 ->whereIn('approval_status', ['pending', 'approved']);
        if ($projectId) {
            $highPriorityQuery = $highPriorityQuery->where('project_id', $projectId);
        }
        $stats['high_priority_risks'] = $highPriorityQuery->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get risks requiring attention
     */
    public function getRisksRequiringAttention(?int $projectId = null): array
    {
        $query = $this->whereIn('risk_level', ['high', 'critical'])
                     ->where('approval_status', 'pending');
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('risk_level', 'DESC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }
    
    /**
     * Search risks
     */
    public function searchRisks(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->groupStart()
                     ->like('description', $searchTerm)
                     ->orLike('mitigation', $searchTerm)
                     ->groupEnd();
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Reject risk
     */
    public function rejectRisk(int $riskId, ?int $updatedBy = null): bool
    {
        return $this->updateApprovalStatus($riskId, 'rejected', $updatedBy);
    }
    
    /**
     * Approve risk
     */
    public function approveRisk(int $riskId, ?int $updatedBy = null): bool
    {
        return $this->updateApprovalStatus($riskId, 'approved', $updatedBy);
    }
}
