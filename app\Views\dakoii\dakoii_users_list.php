<?= $this->extend('templates/dakoii_portal_template') ?>



<?= $this->section('header_actions') ?>
<?php if (in_array($current_user['role'], ['admin', 'moderator'])): ?>
<a href="<?= base_url('dakoii/users/create') ?>" class="btn btn-primary">
    <i class="icon">➕</i> Create User
</a>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">


    <!-- Statistics Cards -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-xl); margin-bottom: var(--spacing-2xl);">
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <div style="font-size: 2rem;">👥</div>
                <div>
                    <div style="font-size: 2rem; font-weight: 700; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?= number_format($stats['total_users']) ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Total Users</div>
                </div>
            </div>
        </div>
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <div style="font-size: 2rem;">✅</div>
                <div>
                    <div style="font-size: 2rem; font-weight: 700; background: var(--gradient-secondary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?= number_format($stats['active_users']) ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Active Users</div>
                </div>
            </div>
        </div>
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <div style="font-size: 2rem;">👑</div>
                <div>
                    <div style="font-size: 2rem; font-weight: 700; background: var(--gradient-accent); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        <?= number_format($stats['admin_users']) ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Administrators</div>
                </div>
            </div>
        </div>
        <div class="card">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <div style="font-size: 2rem;">🛡️</div>
                <div>
                    <div style="font-size: 2rem; font-weight: 700; color: var(--text-primary);">
                        <?= number_format($stats['moderator_users']) ?>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Moderators</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card">
        <div class="card-header">Filter Users</div>
        <form method="GET" action="<?= base_url('dakoii/users') ?>" style="display: flex; gap: var(--spacing-md); align-items: end; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 200px;">
                <label class="form-label">Search</label>
                <input type="text" name="search" placeholder="Search users..." value="<?= esc($search) ?>" class="form-input">
            </div>
            <div style="flex: 1; min-width: 150px;">
                <label class="form-label">Role</label>
                <select name="role" class="form-input">
                    <option value="">All Roles</option>
                    <option value="admin" <?= $roleFilter === 'admin' ? 'selected' : '' ?>>Admin</option>
                    <option value="moderator" <?= $roleFilter === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                    <option value="user" <?= $roleFilter === 'user' ? 'selected' : '' ?>>User</option>
                </select>
            </div>
            <div style="flex: 1; min-width: 150px;">
                <label class="form-label">Status</label>
                <select name="status" class="form-input">
                    <option value="">All Status</option>
                    <option value="active" <?= $statusFilter === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $statusFilter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>
            <div style="flex: 1; min-width: 150px;">
                <label class="form-label">Per Page</label>
                <select name="per_page" class="form-input">
                    <option value="10" <?= $perPage == 10 ? 'selected' : '' ?>>10 per page</option>
                    <option value="25" <?= $perPage == 25 ? 'selected' : '' ?>>25 per page</option>
                    <option value="50" <?= $perPage == 50 ? 'selected' : '' ?>>50 per page</option>
                    <option value="100" <?= $perPage == 100 ? 'selected' : '' ?>>100 per page</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Filter</button>
            <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">Clear</a>
        </form>
    </div>

    <!-- Bulk Actions -->
    <?php if ($current_user['role'] === 'admin'): ?>
    <div class="card" id="bulkActions" style="display: none; border: 2px solid #FFB700;">
        <div class="card-header">Bulk Actions</div>
        <form method="POST" action="<?= base_url('dakoii/users/bulk-action') ?>" id="bulkForm">
            <?= csrf_field() ?>
            <div style="display: flex; gap: var(--spacing-md); align-items: center;">
                <select name="bulk_action" class="form-input" required style="flex: 1;">
                    <option value="">Select Action</option>
                    <option value="activate">Activate</option>
                    <option value="deactivate">Deactivate</option>
                    <option value="delete">Delete</option>
                </select>
                <button type="submit" class="btn btn-primary" onclick="return confirm('Are you sure you want to perform this bulk action?')">
                    Apply to Selected
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearSelection()">Cancel</button>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <!-- Users List -->
    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: var(--spacing-xl);">
        <?php if (empty($users)): ?>
            <div class="card" style="grid-column: 1 / -1; text-align: center; padding: var(--spacing-2xl);">
                <div style="font-size: 4rem; margin-bottom: var(--spacing-md); opacity: 0.5;">👥</div>
                <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">No Users Found</h3>
                <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">No users match your current filters.</p>
                <?php if (in_array($current_user['role'], ['admin', 'moderator'])): ?>
                    <a href="<?= base_url('dakoii/users/create') ?>" class="btn btn-primary">Create First User</a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($users as $user): ?>
                <div class="card">
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md); padding-bottom: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <?php if ($current_user['role'] === 'admin'): ?>
                            <input type="checkbox" name="user_ids[]" value="<?= $user['id'] ?>" class="user-checkbox" onchange="toggleBulkActions()" style="margin-right: var(--spacing-sm);">
                        <?php endif; ?>
                        <div style="width: 60px; height: 60px; border-radius: 50%; overflow: hidden; flex-shrink: 0;">
                            <?php if (!empty($user['id_photo_path']) && file_exists(ROOTPATH . $user['id_photo_path'])): ?>
                                <img src="<?= base_url($user['id_photo_path']) ?>" alt="<?= esc($user['name']) ?>" style="width: 100%; height: 100%; object-fit: cover;">
                            <?php else: ?>
                                <div style="width: 100%; height: 100%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.5rem; color: white;">
                                    <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div style="flex: 1;">
                            <h3 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary);"><?= esc($user['name']) ?></h3>
                            <p style="margin: 0; font-family: var(--font-mono); color: var(--text-secondary); font-size: 0.9rem;"><?= esc($user['user_code']) ?></p>
                            <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;"><?= esc($user['email']) ?></p>
                        </div>
                    </div>

                    <div style="margin-bottom: var(--spacing-md);">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <span style="font-weight: 500; color: var(--text-secondary);">Username:</span>
                            <span style="color: var(--text-primary);"><?= esc($user['username']) ?></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <span style="font-weight: 500; color: var(--text-secondary);">Role:</span>
                            <span style="color: var(--text-primary);">
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.8rem; font-weight: 500; text-transform: uppercase;
                                    <?php if ($user['role'] === 'admin'): ?>background: #dc3545; color: white;
                                    <?php elseif ($user['role'] === 'moderator'): ?>background: #fd7e14; color: white;
                                    <?php else: ?>background: #6c757d; color: white;<?php endif; ?>">
                                    <?= ucfirst($user['role']) ?>
                                </span>
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <span style="font-weight: 500; color: var(--text-secondary);">Status:</span>
                            <span style="color: var(--text-primary);">
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.8rem; font-weight: 500; text-transform: uppercase;
                                    <?= $user['is_activated'] ? 'background: #28a745; color: white;' : 'background: #6c757d; color: white;' ?>">
                                    <?= $user['is_activated'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                            <span style="font-weight: 500; color: var(--text-secondary);">Last Login:</span>
                            <span style="color: var(--text-primary);">
                                <?= $user['last_login_at'] ? date('M j, Y g:i A', strtotime($user['last_login_at'])) : 'Never' ?>
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-xs) 0;">
                            <span style="font-weight: 500; color: var(--text-secondary);">Created:</span>
                            <span style="color: var(--text-primary);"><?= date('M j, Y', strtotime($user['created_at'])) ?></span>
                        </div>
                    </div>

                    <div style="display: flex; gap: var(--spacing-xs); flex-wrap: wrap;">
                        <a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-primary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                            👁️ View
                        </a>
                        
                        <?php 
                        $canEdit = false;
                        if ($current_user['role'] === 'admin') {
                            $canEdit = true;
                        } elseif ($current_user['role'] === 'moderator' && $user['role'] !== 'admin') {
                            $canEdit = true;
                        } elseif ($current_user['id'] == $user['id']) {
                            $canEdit = true;
                        }
                        ?>
                        
                        <?php if ($canEdit): ?>
                            <a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-secondary" style="font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);">
                                ✏️ Edit
                            </a>
                        <?php endif; ?>

                        <?php if ($current_user['role'] === 'admin' && $user['id'] != $current_user['id']): ?>
                            <?php if (!$user['is_activated']): ?>
                                <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/resend-activation') ?>" style="display: inline;">
                                    <?= csrf_field() ?>
                                    <button type="submit" class="btn" style="background: #17a2b8; color: white; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);" title="Resend Activation Email">
                                        📧 Resend
                                    </button>
                                </form>
                            <?php endif; ?>

                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/toggle-status') ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn" style="background: <?= $user['is_activated'] ? '#ffc107' : '#28a745' ?>; color: <?= $user['is_activated'] ? '#000' : '#fff' ?>; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);"
                                        onclick="return confirm('Are you sure you want to <?= $user['is_activated'] ? 'deactivate' : 'activate' ?> this user?')">
                                    <?= $user['is_activated'] ? '⏸️ Deactivate' : '▶️ Activate' ?>
                                </button>
                            </form>

                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id'] . '/reset-password') ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn" style="background: #ffc107; color: #000; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);"
                                        onclick="return confirm('Are you sure you want to reset this user\'s password?')"
                                        title="Reset Password">
                                    🔑 Reset
                                </button>
                            </form>

                            <form method="POST" action="<?= base_url('dakoii/users/' . $user['id']) ?>" style="display: inline;">
                                <?= csrf_field() ?>
                                <input type="hidden" name="_method" value="DELETE">
                                <button type="submit" class="btn" style="background: #dc3545; color: white; font-size: 0.8rem; padding: var(--spacing-xs) var(--spacing-sm);"
                                        onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')"
                                        title="Delete User">
                                    🗑️ Delete
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($pager): ?>
        <div style="margin-top: var(--spacing-lg); display: flex; justify-content: center;">
            <?= $pager->links() ?>
        </div>
    <?php endif; ?>
</div>

<script>
function toggleBulkActions() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');

    if (checkboxes.length > 0) {
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    toggleBulkActions();
}

// Add selected user IDs to bulk form
document.getElementById('bulkForm')?.addEventListener('submit', function(e) {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');

    if (checkboxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one user.');
        return;
    }

    // Remove existing hidden inputs
    const existingInputs = this.querySelectorAll('input[name="user_ids[]"]');
    existingInputs.forEach(input => input.remove());

    // Add selected user IDs
    checkboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'user_ids[]';
        hiddenInput.value = checkbox.value;
        this.appendChild(hiddenInput);
    });
});
</script>
<?= $this->endSection() ?>
