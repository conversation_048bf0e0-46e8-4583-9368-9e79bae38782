<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-documents" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-file-earmark-plus me-2"></i>
    Upload Document
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-folder me-2"></i>
            Project Documents
        </h1>
        <p class="text-muted mb-0">
            Document repository for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Document Statistics -->
<div class="row g-3 mb-4">
    <!-- Total Documents -->
    <div class="col-md-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="fs-1 mb-2">📁</div>
                <div class="fs-3 fw-bold text-primary mb-1">
                    <?= $documentStats['total_documents'] ?? 0 ?>
                </div>
                <div class="text-muted small">Total Documents</div>
            </div>
        </div>
    </div>

    <!-- Recent Uploads -->
    <div class="col-md-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="fs-1 mb-2">📤</div>
                <div class="fs-3 fw-bold text-success mb-1">
                    <?= $documentStats['recent_uploads'] ?? 0 ?>
                </div>
                <div class="text-muted small">Recent Uploads</div>
            </div>
        </div>
    </div>

    <!-- Document Types -->
    <div class="col-md-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="fs-1 mb-2">🏷️</div>
                <div class="fs-3 fw-bold text-info mb-1">
                    <?= count($documentStats['by_type'] ?? []) ?>
                </div>
                <div class="text-muted small">Document Types</div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <i class="bi bi-funnel me-2"></i>Filter Documents
    </div>
    <div class="card-body">
        <form method="GET" action="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="category" class="form-label fw-semibold">Category</label>
                    <select name="category" id="category" class="form-select border-success">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $key => $label): ?>
                            <option value="<?= $key ?>" <?= ($filters['category'] == $key) ? 'selected' : '' ?>>
                                <?= esc($label) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="search" class="form-label fw-semibold">Search</label>
                    <input type="text" name="search" id="search" class="form-control border-success"
                           placeholder="Search by description, path, or type..."
                           value="<?= esc($filters['search'] ?? '') ?>">
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-1"></i>Filter
                        </button>
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Documents List -->
<div class="card">
    <div class="card-header">
        <i class="bi bi-folder me-2"></i>Project Documents
    </div>

    <?php if (!empty($documents)): ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="fw-semibold">Description</th>
                        <th class="text-center fw-semibold">Category</th>
                        <th class="fw-semibold">File Path</th>
                        <th class="text-center fw-semibold">Version</th>
                        <th class="text-center fw-semibold">Upload Date</th>
                        <th class="text-center fw-semibold">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $document): ?>
                        <tr>
                            <td class="align-top">
                                <?php if (!empty($document['description'])): ?>
                                    <div class="fw-semibold mb-1">
                                        <?= esc($document['description']) ?>
                                    </div>
                                <?php else: ?>
                                    <div class="fst-italic text-muted">No description</div>
                                <?php endif; ?>
                                <div class="small text-muted">
                                    Created: <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                </div>
                            </td>
                            <td class="text-center align-top">
                                <?php if (!empty($document['doc_type'])): ?>
                                    <span class="badge bg-info">
                                        <?= esc($categories[$document['doc_type']] ?? $document['doc_type']) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">
                                        Unspecified
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="align-top">
                                <div class="fw-medium">
                                    <i class="bi bi-file-earmark me-1"></i><?= esc(basename($document['doc_path'])) ?>
                                </div>
                            </td>
                            <td class="text-center align-top">
                                <span class="badge bg-warning text-dark">
                                    v<?= $document['version_no'] ?? 1 ?>
                                </span>
                            </td>
                            <td class="text-center align-top">
                                <div class="fw-medium">
                                    <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                </div>
                                <div class="small text-muted">
                                    <?= date('g:i A', strtotime($document['created_at'])) ?>
                                </div>
                            </td>
                            <td class="text-center align-top">
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>"
                                       class="btn btn-sm btn-primary" title="Download">
                                        <i class="bi bi-download"></i>
                                    </a>
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>"
                                       class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button onclick="showDeleteModal(<?= $document['id'] ?>, '<?= esc($document['description'] ?? 'Document') ?>')"
                                            class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="card-body text-center py-5">
            <div class="display-1 mb-3">📁</div>
            <h3 class="text-secondary mb-2">No Documents Found</h3>
            <p class="text-muted mb-4">Upload your first project document to get started.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
                <i class="bi bi-file-earmark-plus me-2"></i>Upload First Document
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>Delete Project Document
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">
                    Are you sure you want to delete the document "<span id="deleteDocumentText" class="fw-semibold"></span>"?
                </p>
                <div class="alert alert-warning d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <div>This action cannot be undone and will permanently delete the file from the server.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" onclick="confirmDelete()" class="btn btn-danger">
                    <i class="bi bi-trash me-2"></i>Delete Document
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentDeleteDocumentId = null;
let deleteModal = null;

document.addEventListener('DOMContentLoaded', function() {
    deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
});

function showDeleteModal(documentId, documentDescription) {
    currentDeleteDocumentId = documentId;
    document.getElementById('deleteDocumentText').textContent = documentDescription || 'this document';
    deleteModal.show();
}

function confirmDelete() {
    if (currentDeleteDocumentId) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/documents/') ?>' + currentDeleteDocumentId + '/delete';

        // Add CSRF token if available
        <?php if (csrf_token()): ?>
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);
        <?php endif; ?>

        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<style>
/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }
}

/* Color-coded Form Input System */
.border-success {
    border-color: #198754 !important;
    border-width: 2px !important;
}

.border-success:focus {
    border-color: #146c43 !important;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25) !important;
}

.border-danger {
    border-color: #dc3545 !important;
    border-width: 2px !important;
}

.border-danger:focus {
    border-color: #b02a37 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
}

/* Enhanced table styling */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Button group improvements */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
<?= $this->endSection() ?>
