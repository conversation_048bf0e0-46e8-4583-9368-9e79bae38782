<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<!-- Dashboard Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Monitoring Dashboard
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Track project progress, monitor budgets, and stay updated on all project activities.
        </p>
    </div>
    <div>
        <a href="<?= base_url('monitoring/reports') ?>" class="btn btn-primary">
            📊 Generate Report
        </a>
    </div>
</div>

<!-- Project Statistics Cards -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
    
    <!-- Total Projects -->
    <div class="card" style="background: linear-gradient(135deg, #3B82F6, #1D4ED8); color: white; border: none;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 style="font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
                    <?= number_format($stats['total_projects']) ?>
                </h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Total Projects</p>
                <small style="opacity: 0.8; font-size: 0.75rem;">
                    <?= $stats['active_projects'] ?> active
                </small>
            </div>
            <div style="font-size: 3rem; opacity: 0.3;">📁</div>
        </div>
    </div>

    <!-- Active Projects -->
    <div class="card" style="background: linear-gradient(135deg, #10B981, #059669); color: white; border: none;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 style="font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
                    <?= number_format($stats['active_projects']) ?>
                </h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Active Projects</p>
                <small style="opacity: 0.8; font-size: 0.75rem;">
                    <?= $stats['on_track_projects'] ?> on track
                </small>
            </div>
            <div style="font-size: 3rem; opacity: 0.3;">🚀</div>
        </div>
    </div>

    <!-- Budget Utilization -->
    <div class="card" style="background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; border: none;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 style="font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
                    <?= number_format($stats['budget_utilization'], 1) ?>%
                </h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Budget Used</p>
                <small style="opacity: 0.8; font-size: 0.75rem;">
                    $<?= number_format($stats['spent_budget']) ?> of $<?= number_format($stats['total_budget']) ?>
                </small>
            </div>
            <div style="font-size: 3rem; opacity: 0.3;">💰</div>
        </div>
    </div>

    <!-- Overdue Projects -->
    <div class="card" style="background: linear-gradient(135deg, #EF4444, #DC2626); color: white; border: none;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h3 style="font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
                    <?= number_format($stats['overdue_projects']) ?>
                </h3>
                <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">Overdue Projects</p>
                <small style="opacity: 0.8; font-size: 0.75rem;">Require attention</small>
            </div>
            <div style="font-size: 3rem; opacity: 0.3;">⚠️</div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
    
    <!-- Project Activities -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3>Recent Project Activities</h3>
            <a href="<?= base_url('monitoring/activities') ?>" class="btn btn-secondary" style="font-size: 0.75rem; padding: var(--spacing-sm) var(--spacing-md);">
                View All
            </a>
        </div>
        
        <?php if (!empty($project_activities)): ?>
            <div style="max-height: 500px; overflow-y: auto;">
                <?php foreach ($project_activities as $activity): ?>
                    <div style="display: flex; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid #E5E7EB;">
                        <div style="width: 50px; height: 50px; border-radius: 50%; background: var(--bg-accent); display: flex; align-items: center; justify-content: center; margin-right: var(--spacing-md); font-size: 1.5rem;">
                            <?= $activity['icon'] ?>
                        </div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                <?= esc($activity['project_name']) ?>
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: var(--spacing-xs);">
                                <strong><?= esc($activity['activity']) ?>:</strong> <?= esc($activity['description']) ?>
                            </div>
                            <div style="display: flex; gap: var(--spacing-md); font-size: 0.75rem; color: var(--text-muted);">
                                <span>Progress: <?= $activity['progress'] ?>%</span>
                                <span>Budget: $<?= number_format($activity['budget_used']) ?></span>
                                <span><?= esc($activity['timestamp']) ?></span>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <span class="status-badge status-<?= $activity['status'] ?>" style="
                                padding: var(--spacing-xs) var(--spacing-sm);
                                border-radius: var(--radius-sm);
                                font-size: 0.75rem;
                                font-weight: 600;
                                text-transform: uppercase;
                                <?php
                                switch($activity['status']) {
                                    case 'completed':
                                        echo 'background: #D1FAE5; color: #065F46;';
                                        break;
                                    case 'in_progress':
                                        echo 'background: #DBEAFE; color: #1E40AF;';
                                        break;
                                    case 'delayed':
                                        echo 'background: #FEF3C7; color: #92400E;';
                                        break;
                                    default:
                                        echo 'background: #F3F4F6; color: #374151;';
                                }
                                ?>
                            ">
                                <?= ucfirst(str_replace('_', ' ', $activity['status'])) ?>
                            </span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📊</div>
                <p>No project activities to display</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Quick Actions & Summary -->
    <div>
        <!-- Quick Actions -->
        <div class="card mb-xl">
            <div class="card-header">
                <h3>Quick Actions</h3>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                <a href="<?= base_url('monitoring/projects') ?>" class="btn btn-primary" style="justify-content: flex-start; text-align: left;">
                    <span style="margin-right: var(--spacing-md);">📁</span>
                    View All Projects
                </a>
                
                <a href="<?= base_url('monitoring/reports/create') ?>" class="btn btn-secondary" style="justify-content: flex-start; text-align: left;">
                    <span style="margin-right: var(--spacing-md);">📊</span>
                    Create Report
                </a>
                
                <a href="<?= base_url('monitoring/budget') ?>" class="btn btn-secondary" style="justify-content: flex-start; text-align: left;">
                    <span style="margin-right: var(--spacing-md);">💰</span>
                    Budget Overview
                </a>
                
                <a href="<?= base_url('monitoring/timeline') ?>" class="btn btn-secondary" style="justify-content: flex-start; text-align: left;">
                    <span style="margin-right: var(--spacing-md);">📅</span>
                    Project Timeline
                </a>
                
                <a href="<?= base_url('monitoring/alerts') ?>" class="btn btn-secondary" style="justify-content: flex-start; text-align: left;">
                    <span style="margin-right: var(--spacing-md);">🔔</span>
                    View Alerts
                </a>
            </div>
        </div>

        <!-- Project Summary -->
        <div class="card">
            <div class="card-header">
                <h3>Project Summary</h3>
            </div>
            
            <div style="display: flex; flex-direction: column; gap: var(--spacing-lg);">
                <!-- Completed Projects -->
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Completed</span>
                    <span style="font-weight: 600; color: var(--brand-secondary);"><?= $stats['completed_projects'] ?></span>
                </div>
                
                <!-- Pending Projects -->
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Pending</span>
                    <span style="font-weight: 600; color: var(--brand-warning);"><?= $stats['pending_projects'] ?></span>
                </div>
                
                <!-- Budget Remaining -->
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Budget Remaining</span>
                    <span style="font-weight: 600; color: var(--brand-primary);">$<?= number_format($stats['remaining_budget']) ?></span>
                </div>
                
                <!-- Progress Bar -->
                <div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: var(--spacing-sm);">
                        <span style="color: var(--text-secondary); font-size: 0.875rem;">Overall Progress</span>
                        <span style="font-weight: 600; color: var(--text-primary);"><?= round(($stats['completed_projects'] / max($stats['total_projects'], 1)) * 100, 1) ?>%</span>
                    </div>
                    <div style="width: 100%; height: 8px; background: #E5E7EB; border-radius: 4px; overflow: hidden;">
                        <div style="height: 100%; background: var(--gradient-secondary); width: <?= round(($stats['completed_projects'] / max($stats['total_projects'], 1)) * 100, 1) ?>%; transition: width 0.3s ease;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('monitoring/export?format=csv') ?>" class="btn btn-secondary">
    📥 Export CSV
</a>
<a href="<?= base_url('monitoring/reports/create') ?>" class="btn btn-primary">
    📊 New Report
</a>
<?= $this->endSection() ?>
