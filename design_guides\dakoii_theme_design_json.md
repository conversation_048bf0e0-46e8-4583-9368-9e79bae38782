{"designSystem": {"name": "Modern Dashboard UI", "version": "1.0.0", "description": "A comprehensive design system for creating modern, dark-themed dashboards with glassmorphic elements", "theme": {"mode": "dark", "style": "glassmorphic-modern", "characteristics": ["Dark navy background", "Translucent card components", "Vibrant gradient accents", "Subtle blur effects", "Neon-like glows", "Rounded corners throughout"]}, "colorPalette": {"background": {"primary": "#0A0E27", "secondary": "#151B3C", "tertiary": "#1E2749"}, "surface": {"card": "rgba(30, 39, 73, 0.6)", "cardHover": "rgba(30, 39, 73, 0.8)", "glassMorphism": {"background": "rgba(255, 255, 255, 0.05)", "border": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)"}}, "gradients": {"primary": {"from": "#FF006E", "to": "#8338EC", "angle": "135deg"}, "secondary": {"from": "#06FFA5", "to": "#00D4FF", "angle": "45deg"}, "accent": {"from": "#FFB700", "to": "#FF006E", "angle": "90deg"}}, "text": {"primary": "#FFFFFF", "secondary": "#B8BCC8", "tertiary": "#7C8091", "muted": "#4A4E5C"}, "dataVisualization": {"series1": "#FF006E", "series2": "#00D4FF", "series3": "#8338EC", "series4": "#06FFA5", "series5": "#FFB700"}}, "typography": {"fontFamily": {"primary": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif", "mono": "'JetBrains Mono', 'Fira Code', monospace"}, "sizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "2rem", "4xl": "2.5rem"}, "weights": {"light": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeight": {"tight": 1.2, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"unit": "0.25rem", "scale": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}}, "borderRadius": {"sm": "0.375rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem", "2xl": "1.5rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)", "glow": {"primary": "0 0 20px rgba(255, 0, 110, 0.3)", "secondary": "0 0 20px rgba(0, 212, 255, 0.3)"}}, "layout": {"grid": {"columns": 12, "gap": "1.5rem", "container": {"maxWidth": "1440px", "padding": "2rem"}}, "cardGrid": {"columns": {"mobile": 1, "tablet": 2, "desktop": 3, "wide": 4}, "aspectRatios": {"square": "1:1", "wide": "16:9", "tall": "9:16", "dashboard": "4:3"}}}, "components": {"card": {"base": {"background": "rgba(30, 39, 73, 0.6)", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "1rem", "padding": "1.5rem", "transition": "all 0.3s ease"}, "hover": {"transform": "translateY(-2px)", "boxShadow": "0 10px 30px rgba(0, 0, 0, 0.2)"}, "header": {"fontSize": "0.875rem", "fontWeight": 600, "color": "#B8BCC8", "marginBottom": "1rem"}}, "chart": {"types": ["line", "bar", "donut", "radial", "area", "scatter"], "styling": {"background": "transparent", "gridLines": {"color": "rgba(255, 255, 255, 0.05)", "dashArray": "3,3"}, "axis": {"color": "#7C8091", "fontSize": "0.75rem"}, "tooltip": {"background": "rgba(0, 0, 0, 0.8)", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "0.5rem", "padding": "0.5rem 0.75rem"}}}, "progressBar": {"height": "0.5rem", "background": "rgba(255, 255, 255, 0.1)", "borderRadius": "0.25rem", "fill": {"gradient": true, "animation": "ease-out 0.6s"}}, "donutChart": {"size": {"sm": "120px", "md": "180px", "lg": "240px"}, "strokeWidth": {"sm": 8, "md": 12, "lg": 16}, "centerText": {"fontSize": "2rem", "fontWeight": 700}}, "statistics": {"value": {"fontSize": "2.5rem", "fontWeight": 700, "gradient": true}, "label": {"fontSize": "0.875rem", "color": "#7C8091", "textTransform": "uppercase", "letterSpacing": "0.05em"}, "trend": {"positive": "#06FFA5", "negative": "#FF006E", "neutral": "#7C8091"}}, "navigation": {"sidebar": {"width": "80px", "background": "rgba(20, 27, 45, 0.8)", "backdropFilter": "blur(10px)", "borderRight": "1px solid rgba(255, 255, 255, 0.05)"}, "item": {"height": "48px", "padding": "0 1rem", "borderRadius": "0.5rem", "transition": "all 0.2s ease", "hover": {"background": "rgba(255, 255, 255, 0.05)", "color": "#FFFFFF"}, "active": {"background": "linear-gradient(135deg, #FF006E, #8338EC)", "color": "#FFFFFF"}}}}, "animations": {"transitions": {"fast": "0.15s ease", "normal": "0.3s ease", "slow": "0.6s ease"}, "effects": {"fadeIn": {"opacity": "0 to 1", "duration": "0.3s"}, "slideUp": {"transform": "translateY(20px) to translateY(0)", "opacity": "0 to 1", "duration": "0.4s"}, "scale": {"transform": "scale(0.95) to scale(1)", "duration": "0.3s"}}, "chartAnimations": {"drawLine": {"strokeDasharray": "1000", "strokeDashoffset": "1000 to 0", "duration": "2s"}, "fillBar": {"scaleY": "0 to 1", "transformOrigin": "bottom", "duration": "0.6s", "stagger": "0.1s"}}}, "responsive": {"breakpoints": {"mobile": "0px", "tablet": "768px", "desktop": "1024px", "wide": "1440px"}, "scaling": {"fontSize": {"mobile": "0.875", "tablet": "0.9375", "desktop": "1"}, "spacing": {"mobile": "0.75", "tablet": "0.875", "desktop": "1"}}}, "accessibility": {"focusStates": {"outline": "2px solid #00D4FF", "outlineOffset": "2px"}, "contrastRatios": {"text": "4.5:1", "largeText": "3:1", "ui": "3:1"}, "motion": {"reducedMotion": "respect prefers-reduced-motion"}}, "patterns": {"dataDisplay": {"emptyState": {"icon": true, "message": "No data available", "action": "optional CTA button"}, "loadingState": {"skeleton": true, "shimmer": "linear-gradient animation", "duration": "1.5s"}, "errorState": {"icon": "warning icon", "message": "error description", "retry": "action button"}}, "interactions": {"hover": {"cards": "lift and glow", "buttons": "brighten and scale", "charts": "highlight data point"}, "click": {"feedback": "scale down briefly", "ripple": "optional ripple effect"}}}}}