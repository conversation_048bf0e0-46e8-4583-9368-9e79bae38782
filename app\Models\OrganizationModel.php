<?php

namespace App\Models;

/**
 * Model for the 'organizations' table.
 * Represents organizations with business identifiers, status, branding, contact, location, web/social, and audit fields.
 */
class OrganizationModel extends BaseModel
{
    /**
     * @var string The database table this model represents.
     */
    protected $table = 'organizations';

    /**
     * @var string The primary key for the table.
     */
    protected $primaryKey = 'id';

    /**
     * @var bool Whether the model should use auto-incrementing primary keys.
     */
    protected $useAutoIncrement = true;

    /**
     * @var string The type of result to return. 'array' or 'object'.
     */
    protected $returnType = 'array';

    /**
     * @var bool Whether the model should use soft deletes.
     */
    protected $useSoftDeletes = true;

    /**
     * @var array The fields that can be mass-assigned.
     * Includes all business, status, branding, contact, location, web/social, and audit fields.
     */
    protected $allowedFields = [
        // Business identifiers
        'org_code', 'name', 'description',
        // Status & licence
        'license_status', 'is_active',
        // Branding
        'logo_path', 'wallpaper_path',
        // Contact & address
        'contact_email', 'contact_phone', 'address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country',
        // Headquarters GPS
        'hq_lat', 'hq_lng',
        // Web & social
        'website_url', 'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url',
        // Location locks
        'country_lock', 'province_lock', 'district_lock', 'llg_lock',
        // Audit-by columns
        'created_by', 'updated_by', 'deleted_by',
    ];

    /**
     * @var bool Whether the model should use timestamps.
     */
    protected $useTimestamps = true;

    /**
     * @var string The name of the `created_at` column.
     */
    protected $createdField = 'created_at';

    /**
     * @var string The name of the `updated_at` column.
     */
    protected $updatedField = 'updated_at';

    /**
     * @var string The name of the `deleted_at` column.
     */
    protected $deletedField = 'deleted_at';

    /**
     * @var array Validation rules for the model's fields.
     * Enforces uniqueness, required fields, and correct formats.
     */
    protected $validationRules = [
        'org_code'      => 'required|is_unique[organizations.org_code]',
        'name'          => 'required',
        'license_status'=> 'required',
        'is_active'     => 'required',
        'contact_email' => 'permit_empty|valid_email',
    ];

    /**
     * @var array Custom error messages for validation rules.
     */
    protected $validationMessages = [];

    /**
     * @var bool Whether to skip validation on insert/update.
     */
    protected $skipValidation = false;

    /**
     * @var bool Whether to clean validation rules before validation.
     */
    protected $cleanValidationRules = true;

    /**
     * @var bool Whether to allow callbacks.
     */
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
}