<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;

/**
 * Admin User Controller
 * 
 * Handles user management for the PROMIS Admin Portal including:
 * - Two-step user creation process
 * - User listing and management
 * - Password reset functionality
 * - Session management
 */
class AdminUserController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
    }

    /**
     * List all users - GET request
     */
    public function listUsers()
    {
        // Get admin's organization - only show users from the same organization
        $adminOrganizationId = session()->get('admin_organization_id');
        $adminOrganizationName = session()->get('admin_organization_name');

        if (!$adminOrganizationId) {
            return redirect()->to(base_url('admin/dashboard'))
                           ->with('error', 'Unable to determine your organization. Please contact system administrator.');
        }

        // Get filters from query parameters (excluding organization since it's auto-set)
        $filters = [
            'role' => $this->request->getGet('role'),
            'status' => $this->request->getGet('status'),
            'organization' => $adminOrganizationId, // Automatically filter by admin's organization
            'search' => $this->request->getGet('search')
        ];

        // Get users with organization details
        $users = $this->getUsersWithFilters($filters);

        $data = [
            'title' => 'User Management - PROMIS Admin',
            'page_title' => 'User Management',
            'users' => $users,
            'admin_organization_name' => $adminOrganizationName,
            'filters' => $filters
        ];

        return view('admin/admin_users_list', $data);
    }

    /**
     * Show user creation step 1 form - GET request
     */
    public function createUserStep1()
    {
        $data = [
            'title' => 'Create User - Step 1 - PROMIS Admin',
            'page_title' => 'Create User - Account Details'
        ];

        return view('admin/admin_users_create_step1', $data);
    }

    /**
     * Process user creation step 1 - POST request
     */
    public function processUserStep1()
    {
        // Validation rules for step 1 (CSRF is handled automatically by CI4)
        $rules = [
            'username' => 'required|min_length[3]|is_unique[users.username]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'name' => 'required|min_length[2]',
            'phone' => 'permit_empty|min_length[10]',
            'department' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Store step 1 data in session
        $step1Data = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'name' => $this->request->getPost('name'),
            'phone' => $this->request->getPost('phone'),
            'department' => $this->request->getPost('department')
        ];

        session()->set('user_creation_step1', $step1Data);

        // Generate temporary password
        $tempPassword = $this->generateTempPassword();
        session()->set('user_temp_password', $tempPassword);

        return redirect()->to(base_url('admin/users/create/step2/temp'))
                        ->with('success', 'Account details saved. Please configure roles and permissions.');
    }

    /**
     * Show user creation step 2 form - GET request
     */
    public function createUserStep2($tempId = null)
    {
        // Check if step 1 data exists
        $step1Data = session()->get('user_creation_step1');
        if (!$step1Data) {
            return redirect()->to(base_url('admin/users/create'))
                           ->with('error', 'Please complete step 1 first.');
        }

        // Get admin's organization (users will be created in the same organization as the admin)
        $adminOrganizationId = session()->get('admin_organization_id');
        $adminOrganizationName = session()->get('admin_organization_name');

        if (!$adminOrganizationId) {
            return redirect()->to(base_url('admin/users/create'))
                           ->with('error', 'Unable to determine your organization. Please contact system administrator.');
        }

        $data = [
            'title' => 'Create User - Step 2 - PROMIS Admin',
            'page_title' => 'Create User - Roles & Permissions',
            'step1_data' => $step1Data,
            'admin_organization_id' => $adminOrganizationId,
            'admin_organization_name' => $adminOrganizationName,
            'temp_password' => session()->get('user_temp_password')
        ];

        return view('admin/admin_users_create_step2', $data);
    }

    /**
     * Process user creation step 2 and complete user creation - POST request
     */
    public function processUserStep2($tempId = null)
    {
        // Check if step 1 data exists
        $step1Data = session()->get('user_creation_step1');
        if (!$step1Data) {
            return redirect()->to(base_url('admin/users/create'))
                           ->with('error', 'Session expired. Please start over.');
        }

        // Get admin's organization (users will be created in the same organization as the admin)
        $adminOrganizationId = session()->get('admin_organization_id');

        if (!$adminOrganizationId) {
            return redirect()->to(base_url('admin/users/create'))
                           ->with('error', 'Unable to determine your organization. Please contact system administrator.');
        }

        // Validation rules for step 2 (CSRF is handled automatically by CI4)
        $rules = [
            'role' => 'required|in_list[admin,moderator,editor,user]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Combine step 1 and step 2 data
        $userData = array_merge($step1Data, [
            'organization_id' => $adminOrganizationId, // Automatically use admin's organization
            'role' => $this->request->getPost('role'),
            'password_hash' => password_hash(session()->get('user_temp_password'), PASSWORD_ARGON2ID),
            'is_activated' => 1, // Auto-activate for admin-created users
            'is_project_officer' => $this->request->getPost('is_project_officer') ? 1 : 0,
            'is_evaluator' => $this->request->getPost('is_evaluator') ? 1 : 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'created_by' => session()->get('admin_user_id'),
            'created_at' => date('Y-m-d H:i:s')
        ]);

        try {
            $userId = $this->userModel->insert($userData);
            
            if ($userId) {
                // Clear session data
                session()->remove(['user_creation_step1', 'user_temp_password']);
                
                // Log the action
                $this->logAuditEvent('create', session()->get('admin_user_id'), 'users', $userId, [
                    'description' => 'New user created: ' . $userData['username'],
                    'user_data' => $userData
                ]);

                $organizationName = session()->get('admin_organization_name');
                return redirect()->to(base_url('admin/users'))
                               ->with('success', 'User created successfully! Username: ' . $userData['username'] . ', Organization: ' . $organizationName . ', Temporary Password: ' . session()->get('user_temp_password'));
            } else {
                return redirect()->back()->withInput()->with('errors', $this->userModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating user: ' . $e->getMessage());
        }
    }

    /**
     * Show edit user form - GET request
     */
    public function editUser($userId)
    {
        // Get admin's organization to ensure user belongs to same organization
        $adminOrganizationId = session()->get('admin_organization_id');
        $adminOrganizationName = session()->get('admin_organization_name');

        $user = $this->userModel->where('id', $userId)
                                ->where('organization_id', $adminOrganizationId)
                                ->first();

        if (!$user) {
            return redirect()->to(base_url('admin/users'))
                           ->with('error', 'User not found or access denied.');
        }

        // Prevent editing admin users (only moderator, editor, user roles can be edited)
        if ($user['role'] === 'admin') {
            return redirect()->to(base_url('admin/users'))
                           ->with('error', 'Administrator accounts cannot be edited.');
        }

        $data = [
            'title' => 'Edit User - PROMIS Admin',
            'page_title' => 'Edit User',
            'user' => $user,
            'admin_organization_name' => $adminOrganizationName
        ];

        return view('admin/admin_users_edit', $data);
    }

    /**
     * Process user update - POST request
     */
    public function updateUser($userId)
    {
        // Get admin's organization to ensure user belongs to same organization
        $adminOrganizationId = session()->get('admin_organization_id');

        $user = $this->userModel->where('id', $userId)
                                ->where('organization_id', $adminOrganizationId)
                                ->first();

        if (!$user) {
            return redirect()->to(base_url('admin/users'))
                           ->with('error', 'User not found or access denied.');
        }

        // Prevent editing admin users
        if ($user['role'] === 'admin') {
            return redirect()->to(base_url('admin/users'))
                           ->with('error', 'Administrator accounts cannot be edited.');
        }

        // Validation rules that properly exclude the current user
        $newEmail = $this->request->getPost('email');
        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'email' => "required|valid_email|max_length[100]|is_unique[users.email,id,{$userId}]",
            'role' => 'required|in_list[moderator,editor,user]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $updateData = [
            'name' => $this->request->getPost('name'),
            'email' => $newEmail,
            'role' => $this->request->getPost('role'),
            'is_project_officer' => $this->request->getPost('is_project_officer') ? 1 : 0,
            'is_evaluator' => $this->request->getPost('is_evaluator') ? 1 : 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'updated_by' => session()->get('admin_user_id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            // Temporarily skip model validation to avoid conflicts with controller validation
            $this->userModel->skipValidation(true);
            $updated = $this->userModel->update($userId, $updateData);
            $this->userModel->skipValidation(false);

            if ($updated) {
                // Log the action
                $this->logAuditEvent('update', session()->get('admin_user_id'), 'users', $userId, [
                    'description' => 'User updated: ' . $updateData['name'],
                    'changes' => $updateData
                ]);

                return redirect()->to(base_url('admin/users'))
                               ->with('success', 'User updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->userModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating user: ' . $e->getMessage());
        }
    }

    /**
     * Show password reset modal - GET request
     */
    public function showResetPasswordModal($userId)
    {
        $user = $this->userModel->find($userId);

        if (!$user) {
            return $this->response->setStatusCode(404)->setBody('User not found');
        }

        $data = [
            'user' => $user
        ];

        return view('admin/admin_users_reset_password_modal', $data);
    }

    /**
     * Process password reset - POST request
     */
    public function processPasswordReset($userId)
    {
        $user = $this->userModel->find($userId);
        
        if (!$user) {
            return redirect()->back()->with('error', 'User not found');
        }

        // Generate new temporary password
        $tempPassword = $this->generateTempPassword();
        $hashedPassword = password_hash($tempPassword, PASSWORD_ARGON2ID);

        // Update user password
        $updateData = [
            'password_hash' => $hashedPassword,
            'updated_by' => session()->get('admin_user_id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->userModel->update($userId, $updateData)) {
            // Log the action
            $this->logAuditEvent('update', session()->get('admin_user_id'), 'users', $userId, [
                'description' => 'Password reset for user: ' . $user['username'],
                'action_type' => 'password_reset'
            ]);

            return redirect()->back()->with('success', 'Password reset successfully! New temporary password: ' . $tempPassword);
        } else {
            return redirect()->back()->with('error', 'Failed to reset password');
        }
    }

    /**
     * View active sessions - GET request
     */
    public function viewActiveSessions()
    {
        // This is a placeholder - in a real implementation, you'd query session storage
        $sessions = [
            [
                'user_id' => session()->get('admin_user_id'),
                'username' => session()->get('admin_username'),
                'ip_address' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent(),
                'login_time' => date('Y-m-d H:i:s', session()->get('admin_last_activity')),
                'session_id' => session()->session_id,
                'status' => 'active'
            ]
        ];

        $data = [
            'title' => 'Active Sessions - PROMIS Admin',
            'page_title' => 'Active User Sessions',
            'sessions' => $sessions
        ];

        return view('admin/admin_users_sessions', $data);
    }

    /**
     * Terminate session - POST request
     */
    public function terminateSession($sessionId)
    {
        // In a real implementation, you'd terminate the specific session
        // For now, we'll just show a success message
        return redirect()->back()->with('success', 'Session terminated successfully');
    }

    /**
     * Get users with filters applied
     */
    private function getUsersWithFilters($filters)
    {
        $builder = $this->userModel->select('users.*, organizations.name as org_name')
                                  ->join('organizations', 'organizations.id = users.organization_id', 'left')
                                  ->where('users.deleted_at', null);

        // Apply filters
        if (!empty($filters['role'])) {
            $builder->where('users.role', $filters['role']);
        }

        if (!empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $builder->where('users.is_activated', 1);
            } else {
                $builder->where('users.is_activated', 0);
            }
        }

        if (!empty($filters['organization'])) {
            $builder->where('users.organization_id', $filters['organization']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('users.name', $filters['search'])
                   ->orLike('users.username', $filters['search'])
                   ->orLike('users.email', $filters['search'])
                   ->groupEnd();
        }

        return $builder->orderBy('users.created_at', 'DESC')->findAll();
    }

    /**
     * Generate temporary password
     */
    private function generateTempPassword()
    {
        return str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $primaryKey, $data = [])
    {
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => (string)$primaryKey,
            'action' => $action,
            'new_data' => json_encode($data),
            'user_id' => $userId,
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'user_management',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'session_id' => session()->session_id,
            'request_url' => current_url(),
            'description' => $data['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $db->table('audit_logs')->insert($auditData);
    }
}
