<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class OrganizationSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'org_code' => '12345',
                'name' => 'Sample Organization',
                'description' => 'This is a sample organization for testing purposes.',
                'license_status' => 'paid',
                'is_active' => 1,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-123-4567',
                'address_line1' => '123 Main Street',
                'city' => 'Sample City',
                'state' => 'Sample State',
                'postal_code' => '12345',
                'country' => 'Sample Country',
                'website_url' => 'https://www.sample.org',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'org_code' => '67890',
                'name' => 'Test Organization',
                'description' => 'Another test organization with unpaid license.',
                'license_status' => 'unpaid',
                'is_active' => 1,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-987-6543',
                'address_line1' => '456 Test Avenue',
                'city' => 'Test City',
                'state' => 'Test State',
                'postal_code' => '67890',
                'country' => 'Test Country',
                'website_url' => 'https://www.test.org',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'org_code' => '11111',
                'name' => 'Inactive Organization',
                'description' => 'This organization is inactive for testing.',
                'license_status' => 'paid',
                'is_active' => 0,
                'contact_email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ]
        ];

        $builder = $this->db->table('organizations');
        
        foreach ($data as $org) {
            // Check if organization already exists
            $existing = $builder->where('org_code', $org['org_code'])->get()->getRow();
            
            if (!$existing) {
                $builder->insert($org);
                echo "Created organization: " . $org['name'] . " (" . $org['org_code'] . ")\n";
            } else {
                echo "Organization already exists: " . $org['name'] . " (" . $org['org_code'] . ")\n";
            }
        }

        // Create some test users for the organizations
        $this->createTestUsers();
    }

    private function createTestUsers()
    {
        $userData = [
            [
                'organization_id' => 1, // Sample Organization
                'user_code' => 'ADMIN001',
                'username' => 'sampleadmin',
                'email' => '<EMAIL>',
                'name' => 'Sample Admin',
                'role' => 'admin',
                'password_hash' => password_hash('admin123', PASSWORD_ARGON2ID),
                'is_activated' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'organization_id' => 1, // Sample Organization
                'user_code' => 'MOD001',
                'username' => 'samplemod',
                'email' => '<EMAIL>',
                'name' => 'Sample Moderator',
                'role' => 'moderator',
                'password_hash' => password_hash('mod123', PASSWORD_ARGON2ID),
                'is_activated' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'organization_id' => 2, // Test Organization
                'user_code' => 'ADMIN002',
                'username' => 'testadmin',
                'email' => '<EMAIL>',
                'name' => 'Test Admin',
                'role' => 'admin',
                'password_hash' => password_hash('test123', PASSWORD_ARGON2ID),
                'is_activated' => 0, // Pending activation
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ],
            [
                'organization_id' => 2, // Test Organization
                'user_code' => 'USER001',
                'username' => 'testuser',
                'email' => '<EMAIL>',
                'name' => 'Test User',
                'role' => 'user',
                'password_hash' => password_hash('user123', PASSWORD_ARGON2ID),
                'is_activated' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1,
            ]
        ];

        $builder = $this->db->table('users');

        foreach ($userData as $user) {
            // Check if user already exists
            $existing = $builder->where('email', $user['email'])->get()->getRow();

            if (!$existing) {
                $builder->insert($user);
                echo "Created user: " . $user['name'] . " (" . $user['email'] . ")\n";
            } else {
                echo "User already exists: " . $user['name'] . " (" . $user['email'] . ")\n";
            }
        }
    }
}
