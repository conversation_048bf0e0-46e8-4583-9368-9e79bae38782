<?php /** @var array $organization */ ?>

<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations/' . $organization['id']) ?>" class="btn btn-secondary">
    ← Back to Organization
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <ul style="margin: 0; padding-left: 20px;">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <!-- Current Organization Info -->
    <div class="current-info">
        <?php if ($organization['logo_path']): ?>
            <img src="<?= base_url($organization['logo_path']) ?>" alt="Current Logo" style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover;">
        <?php else: ?>
            <div class="avatar-placeholder">
                <?= strtoupper(substr($organization['name'], 0, 1)) ?>
            </div>
        <?php endif; ?>

        <div class="current-details">
            <h3><?= esc($organization['name']) ?></h3>
            <p><strong>Code:</strong> <?= esc($organization['org_code']) ?></p>
            <p><strong>Status:</strong>
                <span class="role-badge <?= $organization['is_active'] ? 'role-admin' : 'role-user' ?>">
                    <?= $organization['is_active'] ? 'Active' : 'Inactive' ?>
                </span>
            </p>
        </div>
    </div>

    <form action="<?= base_url('dakoii/organizations/' . $organization['id'] . '/update') ?>" method="post" enctype="multipart/form-data" id="editOrgForm">
        <?= csrf_field() ?>

        <!-- Basic Information -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Basic Information</div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                <div class="form-group">
                    <label for="name" class="form-label">Organization Name *</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input"
                        placeholder="Enter organization name"
                        value="<?= esc($organization['name']) ?>"
                        required
                        maxlength="150"
                    >
                    <div class="form-help">This will be the display name for the organization</div>
                </div>

                <div class="form-group">
                    <label for="org_code_display" class="form-label">Organization Code</label>
                    <input
                        type="text"
                        id="org_code_display"
                        class="form-input"
                        value="<?= esc($organization['org_code']) ?>"
                        disabled
                        style="background: var(--glass-bg); color: var(--text-muted);"
                    >
                    <div class="form-help">Organization code cannot be changed</div>
                </div>
            </div>

            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <textarea
                    id="description"
                    name="description"
                    class="form-input"
                    placeholder="Brief description of the organization"
                    rows="3"
                    maxlength="1000"
                ><?= esc($organization['description']) ?></textarea>
                <div class="form-help">Optional description (max 1000 characters)</div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Contact Information</div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                <div class="form-group">
                    <label for="contact_email" class="form-label">Contact Email</label>
                    <input
                        type="email"
                        id="contact_email"
                        name="contact_email"
                        class="form-input"
                        placeholder="<EMAIL>"
                        value="<?= esc($organization['contact_email']) ?>"
                        maxlength="100"
                    >
                </div>

                <div class="form-group">
                    <label for="contact_phone" class="form-label">Contact Phone</label>
                    <input
                        type="tel"
                        id="contact_phone"
                        name="contact_phone"
                        class="form-input"
                        placeholder="+****************"
                        value="<?= esc($organization['contact_phone']) ?>"
                        maxlength="20"
                    >
                </div>
            </div>

            <div class="form-group">
                <label for="website_url" class="form-label">Website URL</label>
                <input
                    type="url"
                    id="website_url"
                    name="website_url"
                    class="form-input"
                    placeholder="https://www.organization.com"
                    value="<?= esc($organization['website_url']) ?>"
                    maxlength="150"
                >
            </div>
        </div>

        <!-- Address Information -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Address Information</div>

            <div class="form-group">
                <label for="address_line1" class="form-label">Address Line 1</label>
                <input
                    type="text"
                    id="address_line1"
                    name="address_line1"
                    class="form-input"
                    placeholder="Street address"
                    value="<?= esc($organization['address_line1']) ?>"
                    maxlength="150"
                >
            </div>

            <div class="form-group">
                <label for="address_line2" class="form-label">Address Line 2</label>
                <input
                    type="text"
                    id="address_line2"
                    name="address_line2"
                    class="form-input"
                    placeholder="Apartment, suite, etc. (optional)"
                    value="<?= esc($organization['address_line2']) ?>"
                    maxlength="150"
                >
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--spacing-lg);">
                <div class="form-group">
                    <label for="city" class="form-label">City</label>
                    <input
                        type="text"
                        id="city"
                        name="city"
                        class="form-input"
                        placeholder="City"
                        value="<?= esc($organization['city']) ?>"
                        maxlength="100"
                    >
                </div>

                <div class="form-group">
                    <label for="state" class="form-label">State/Province</label>
                    <input
                        type="text"
                        id="state"
                        name="state"
                        class="form-input"
                        placeholder="State or Province"
                        value="<?= esc($organization['state']) ?>"
                        maxlength="100"
                    >
                </div>

                <div class="form-group">
                    <label for="country" class="form-label">Country</label>
                    <input
                        type="text"
                        id="country"
                        name="country"
                        class="form-input"
                        placeholder="Country"
                        value="<?= esc($organization['country']) ?>"
                        maxlength="100"
                    >
                </div>
            </div>

            <div class="form-group">
                <label for="postal_code" class="form-label">Postal Code</label>
                <input
                    type="text"
                    id="postal_code"
                    name="postal_code"
                    class="form-input"
                    placeholder="Postal/ZIP code"
                    value="<?= esc($organization['postal_code']) ?>"
                    maxlength="20"
                    style="max-width: 200px;"
                >
            </div>
        </div>

        <!-- Branding -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Branding</div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                <div class="form-group">
                    <label for="logo" class="form-label">Logo</label>
                    <input
                        type="file"
                        id="logo"
                        name="logo"
                        class="form-input"
                        accept="image/jpeg,image/png,image/gif,image/webp"
                    >
                    <div class="form-help">Max 25MB. Formats: JPEG, PNG, GIF, WebP. Leave empty to keep current logo.</div>
                    <?php if ($organization['logo_path']): ?>
                        <div style="margin-top: var(--spacing-sm);">
                            <small style="color: var(--text-tertiary);">Current: <?= basename($organization['logo_path']) ?></small>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="form-group">
                    <label for="wallpaper" class="form-label">Wallpaper</label>
                    <input
                        type="file"
                        id="wallpaper"
                        name="wallpaper"
                        class="form-input"
                        accept="image/jpeg,image/png,image/gif,image/webp"
                    >
                    <div class="form-help">Max 25MB. Formats: JPEG, PNG, GIF, WebP. Leave empty to keep current wallpaper.</div>
                    <?php if ($organization['wallpaper_path']): ?>
                        <div style="margin-top: var(--spacing-sm);">
                            <small style="color: var(--text-tertiary);">Current: <?= basename($organization['wallpaper_path']) ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Social Media -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Social Media</div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                <div class="form-group">
                    <label for="facebook_url" class="form-label">Facebook URL</label>
                    <input
                        type="url"
                        id="facebook_url"
                        name="facebook_url"
                        class="form-input"
                        placeholder="https://facebook.com/organization"
                        value="<?= esc($organization['facebook_url']) ?>"
                        maxlength="150"
                    >
                </div>

                <div class="form-group">
                    <label for="twitter_url" class="form-label">Twitter URL</label>
                    <input
                        type="url"
                        id="twitter_url"
                        name="twitter_url"
                        class="form-input"
                        placeholder="https://twitter.com/organization"
                        value="<?= esc($organization['twitter_url']) ?>"
                        maxlength="150"
                    >
                </div>

                <div class="form-group">
                    <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                    <input
                        type="url"
                        id="linkedin_url"
                        name="linkedin_url"
                        class="form-input"
                        placeholder="https://linkedin.com/company/organization"
                        value="<?= esc($organization['linkedin_url']) ?>"
                        maxlength="150"
                    >
                </div>

                <div class="form-group">
                    <label for="instagram_url" class="form-label">Instagram URL</label>
                    <input
                        type="url"
                        id="instagram_url"
                        name="instagram_url"
                        class="form-input"
                        placeholder="https://instagram.com/organization"
                        value="<?= esc($organization['instagram_url']) ?>"
                        maxlength="150"
                    >
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
            <a href="<?= base_url('dakoii/organizations/' . $organization['id']) ?>" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <span id="submitText">Update Organization</span>
                <span id="submitLoader" class="loading" style="display: none;"></span>
            </button>
        </div>
    </form>
</div>

<style>
.form-help {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid;
    backdrop-filter: blur(10px);
}

.alert-error {
    background: rgba(255, 0, 110, 0.1);
    border-color: rgba(255, 0, 110, 0.3);
    color: #FF006E;
}

.form-input[type="file"] {
    padding: var(--spacing-sm);
}

.form-input:focus {
    transform: translateY(-1px);
}

textarea.form-input {
    resize: vertical;
    min-height: 80px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editOrgForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitLoader = document.getElementById('submitLoader');

    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        submitText.style.display = 'none';
        submitLoader.style.display = 'inline-block';

        // Re-enable after 30 seconds as fallback
        setTimeout(function() {
            submitBtn.disabled = false;
            submitText.style.display = 'inline';
            submitLoader.style.display = 'none';
        }, 30000);
    });

    // Character counter for description
    const description = document.getElementById('description');
    const maxLength = 1000;

    description.addEventListener('input', function() {
        const remaining = maxLength - this.value.length;
        let helpText = this.parentNode.querySelector('.form-help');
        helpText.textContent = `Optional description (${remaining} characters remaining)`;

        if (remaining < 100) {
            helpText.style.color = remaining < 0 ? '#FF006E' : '#FFB700';
        } else {
            helpText.style.color = 'var(--text-tertiary)';
        }
    });
});
</script>
<?= $this->endSection() ?>