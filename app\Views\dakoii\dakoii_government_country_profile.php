<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/countries') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Countries
</a>
<?php if (isset($country)): ?>
<a href="<?= base_url('dakoii/government/countries/' . $country['id'] . '/edit') ?>" class="btn btn-primary">
    <i class="icon">✏️</i> Edit Country
</a>
<?php endif; ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <?php if (isset($country)): ?>
        <!-- Success Message -->
        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success" style="margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: var(--radius-md); color: #28a745;">
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div style="margin-bottom: var(--spacing-xl);">
            <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
                <span style="font-size: 2.5rem;">🌍</span>
                <?= esc($country['name']) ?>
            </h1>
            <p style="color: var(--text-secondary); font-size: 1rem;">
                Country profile and administrative information.
            </p>
        </div>

        <!-- Main Content Grid -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
            
            <!-- Left Column - Main Information -->
            <div style="display: flex; flex-direction: column; gap: var(--spacing-xl);">
                
                <!-- Country Information Card -->
                <div class="card">
                    <div class="card-header">Country Information</div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-xl);">
                        <div>
                            <div style="margin-bottom: var(--spacing-lg);">
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                                    <span style="font-weight: 600; color: var(--text-secondary);">Country Name:</span>
                                    <span style="color: var(--text-primary); font-weight: 500;"><?= esc($country['name']) ?></span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                                    <span style="font-weight: 600; color: var(--text-secondary);">ISO2 Code:</span>
                                    <span style="color: var(--text-primary); font-family: var(--font-mono); background: rgba(0, 212, 255, 0.1); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm);"><?= esc($country['iso2']) ?></span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                                    <span style="font-weight: 600; color: var(--text-secondary);">ISO3 Code:</span>
                                    <span style="color: var(--text-primary); font-family: var(--font-mono); background: rgba(0, 212, 255, 0.1); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm);"><?= esc($country['iso3']) ?></span>
                                </div>
                                <?php if (isset($country['created_at'])): ?>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--glass-border);">
                                    <span style="font-weight: 600; color: var(--text-secondary);">Created:</span>
                                    <span style="color: var(--text-primary);"><?= date('M j, Y', strtotime($country['created_at'])) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if (isset($country['updated_at'])): ?>
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0;">
                                    <span style="font-weight: 600; color: var(--text-secondary);">Last Updated:</span>
                                    <span style="color: var(--text-primary);"><?= date('M j, Y', strtotime($country['updated_at'])) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class="card">
                    <div class="card-header">Quick Actions</div>
                    <div style="display: flex; gap: var(--spacing-md); flex-wrap: wrap;">
                        <a href="<?= base_url('dakoii/government/countries/' . $country['id'] . '/edit') ?>" class="btn btn-primary">
                            ✏️ Edit Country
                        </a>
                        <a href="<?= base_url('dakoii/government/provinces/by-country/' . $country['id']) ?>" class="btn btn-secondary">
                            🏞️ View Provinces
                        </a>
                        <form method="POST" action="<?= base_url('dakoii/government/countries/' . $country['id'] . '/delete') ?>" style="display: inline;">
                            <?= csrf_field() ?>
                            <button type="submit" class="btn" style="background: #dc3545; color: white;"
                                    onclick="return confirm('Are you sure you want to delete this country? This will also delete all associated provinces, districts, and LLGs.')"
                                    title="Delete Country">
                                🗑️ Delete Country
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Right Column - Side Information -->
            <div style="display: flex; flex-direction: column; gap: var(--spacing-xl);">
                
                <!-- Administrative Hierarchy Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                            <span>🏛️</span>
                            Administrative Structure
                        </h3>
                    </div>
                    <div style="padding: var(--spacing-lg);">
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border-radius: var(--radius-md); border: 1px solid rgba(0, 212, 255, 0.3);">
                                <div style="width: 40px; height: 40px; background: rgba(0, 212, 255, 0.2); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">🌍</div>
                                <div style="flex: 1;">
                                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Country</div>
                                    <div style="color: var(--accent-primary); font-weight: 600;"><?= esc($country['name']) ?></div>
                                </div>
                            </div>
                            
                            <div style="padding-left: var(--spacing-lg); border-left: 2px solid var(--glass-border);">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-sm);">
                                    <span style="font-size: 1.25rem;">🏞️</span>
                                    <div>
                                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Provinces</div>
                                        <a href="<?= base_url('dakoii/government/provinces/by-country/' . $country['id']) ?>" style="color: var(--accent-primary); text-decoration: none; font-weight: 500;">View All Provinces</a>
                                    </div>
                                </div>
                                
                                <div style="padding-left: var(--spacing-lg); border-left: 2px solid var(--glass-border);">
                                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-sm);">
                                        <span style="font-size: 1.25rem;">🏘️</span>
                                        <div>
                                            <div style="color: var(--text-secondary); font-size: 0.875rem;">Districts</div>
                                            <div style="color: var(--text-primary); font-weight: 500;">Within Provinces</div>
                                        </div>
                                    </div>
                                    
                                    <div style="padding-left: var(--spacing-lg); border-left: 2px solid var(--glass-border);">
                                        <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-sm);">
                                            <span style="font-size: 1.25rem;">🏛️</span>
                                            <div>
                                                <div style="color: var(--text-secondary); font-size: 0.875rem;">Local Level Governments</div>
                                                <div style="color: var(--text-primary); font-weight: 500;">Within Districts</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                            <span>⚙️</span>
                            System Information
                        </h3>
                    </div>
                    <div style="padding: var(--spacing-lg);">
                        <div style="display: flex; flex-direction: column; gap: var(--spacing-md); font-size: 0.9rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm) 0; border-bottom: 1px solid var(--glass-border);">
                                <span style="color: var(--text-secondary);">Record ID:</span>
                                <span style="color: var(--text-primary); font-family: var(--font-mono);">#<?= $country['id'] ?></span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm) 0; border-bottom: 1px solid var(--glass-border);">
                                <span style="color: var(--text-secondary);">Status:</span>
                                <span style="color: #28a745; font-weight: 500;">✅ Active</span>
                            </div>
                            <?php if (isset($country['created_by'])): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm) 0; border-bottom: 1px solid var(--glass-border);">
                                <span style="color: var(--text-secondary);">Created By:</span>
                                <span style="color: var(--text-primary);">User #<?= $country['created_by'] ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if (isset($country['updated_by'])): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm) 0;">
                                <span style="color: var(--text-secondary);">Updated By:</span>
                                <span style="color: var(--text-primary);">User #<?= $country['updated_by'] ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Help Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                            <span>💡</span>
                            Country Information
                        </h3>
                    </div>
                    <div style="padding: var(--spacing-lg);">
                        <div style="font-size: 0.9rem; color: var(--text-secondary);">
                            <p style="margin: 0 0 var(--spacing-md) 0;">
                                <strong>ISO Codes:</strong> International Organization for Standardization country codes used for identification.
                            </p>
                            <p style="margin: 0 0 var(--spacing-md) 0;">
                                <strong>ISO2:</strong> Two-letter country code (e.g., US, AU, PG)
                            </p>
                            <p style="margin: 0;">
                                <strong>ISO3:</strong> Three-letter country code (e.g., USA, AUS, PNG)
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php else: ?>
        <div class="card" style="text-align: center; padding: var(--spacing-2xl);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-md); opacity: 0.5;">🌍</div>
            <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Country Not Found</h3>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">The requested country could not be found.</p>
            <a href="<?= base_url('dakoii/government/countries') ?>" class="btn btn-primary">
                <i class="icon">←</i> Back to Countries
            </a>
        </div>
    <?php endif; ?>
</div>
<?= $this->endSection() ?> 