<?php

namespace App\Models;

/**
 * Project Milestone Model
 * 
 * Handles detailed project deliverables within phases.
 * Tracks milestone progress, completion, and evidence.
 */
class ProjectMilestoneModel extends BaseModel
{
    protected $table      = 'project_milestones';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'phase_id', 'milestone_code', 'title', 'description',
        'target_date', 'status', 'completion_date', 'evidence_count',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'phase_id'       => 'required|integer',
        'milestone_code' => 'required|max_length[20]',
        'title'          => 'required|max_length[200]',
        'status'         => 'in_list[pending,in-progress,completed,approved]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'phase_id' => [
            'required' => 'Phase ID is required'
        ],
        'milestone_code' => [
            'required' => 'Milestone code is required'
        ],
        'title' => [
            'required' => 'Milestone title is required'
        ]
    ];
    
    /**
     * Get milestones by project
     */
    public function getByProject(int $projectId, ?string $status = null): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('target_date', 'ASC')->findAll();
    }
    
    /**
     * Get milestones by phase
     */
    public function getByPhase(int $phaseId, ?string $status = null): array
    {
        $query = $this->where('phase_id', $phaseId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('target_date', 'ASC')->findAll();
    }
    
    /**
     * Get milestones by status
     */
    public function getByStatus(string $status): array
    {
        return $this->where('status', $status)
                   ->orderBy('target_date', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get overdue milestones
     */
    public function getOverdueMilestones(): array
    {
        return $this->where('target_date <', date('Y-m-d'))
                   ->whereIn('status', ['pending', 'in-progress'])
                   ->orderBy('target_date', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get upcoming milestones
     */
    public function getUpcomingMilestones(int $days = 30): array
    {
        $futureDate = date('Y-m-d', strtotime("+{$days} days"));
        
        return $this->where('target_date >=', date('Y-m-d'))
                   ->where('target_date <=', $futureDate)
                   ->whereIn('status', ['pending', 'in-progress'])
                   ->orderBy('target_date', 'ASC')
                   ->findAll();
    }
    
    /**
     * Update milestone status
     */
    public function updateStatus(int $milestoneId, string $status, ?int $userId = null): bool
    {
        $data = [
            'status' => $status,
            'updated_by' => $userId
        ];
        
        // Set completion date if status is completed or approved
        if (in_array($status, ['completed', 'approved'])) {
            $data['completion_date'] = date('Y-m-d');
        }
        
        return $this->update($milestoneId, $data);
    }
    
    /**
     * Update evidence count
     */
    public function updateEvidenceCount(int $milestoneId, int $count): bool
    {
        return $this->update($milestoneId, [
            'evidence_count' => $count,
            'updated_by' => session()->get('admin_user_id')
        ]);
    }
    
    /**
     * Get milestone statistics
     */
    public function getMilestoneStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Count by status
        $statusCounts = $query->select('status, COUNT(*) as count')
                             ->groupBy('status')
                             ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Overdue count
        $stats['overdue'] = $this->where('target_date <', date('Y-m-d'))
                                ->whereIn('status', ['pending', 'in-progress'])
                                ->countAllResults();
        
        // Upcoming count (next 30 days)
        $futureDate = date('Y-m-d', strtotime('+30 days'));
        $stats['upcoming'] = $this->where('target_date >=', date('Y-m-d'))
                                 ->where('target_date <=', $futureDate)
                                 ->whereIn('status', ['pending', 'in-progress'])
                                 ->countAllResults();
        
        // Total milestones
        $totalQuery = $this;
        if ($projectId) {
            $totalQuery = $totalQuery->where('project_id', $projectId);
        }
        $stats['total'] = $totalQuery->countAllResults();
        
        return $stats;
    }
    
    /**
     * Get milestones with phase information
     */
    public function getMilestonesWithPhase(int $projectId): array
    {
        return $this->select('project_milestones.*, project_phases.title as phase_title, project_phases.phase_code')
                   ->join('project_phases', 'project_phases.id = project_milestones.phase_id')
                   ->where('project_milestones.project_id', $projectId)
                   ->orderBy('project_phases.sort_order', 'ASC')
                   ->orderBy('project_milestones.target_date', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get completion percentage for project
     */
    public function getProjectCompletionPercentage(int $projectId): float
    {
        $total = $this->where('project_id', $projectId)->countAllResults();
        
        if ($total === 0) {
            return 0.0;
        }
        
        $completed = $this->where('project_id', $projectId)
                         ->whereIn('status', ['completed', 'approved'])
                         ->countAllResults();
        
        return round(($completed / $total) * 100, 2);
    }
}
