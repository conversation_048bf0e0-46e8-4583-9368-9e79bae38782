<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DakoiiUserSeeder extends Seeder
{
    public function run()
    {
        $data = [
            'user_code' => 'ADMIN001',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'name' => 'Dakoii Administrator',
            'role' => 'admin',
            'password_hash' => password_hash('admin123', PASSWORD_ARGON2ID),
            'is_activated' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => 1,
        ];

        // Check if user already exists
        $builder = $this->db->table('dakoii_users');
        $existingUser = $builder->where('username', 'admin')->get()->getRow();

        if (!$existingUser) {
            $builder->insert($data);
            echo "Default admin user created:\n";
            echo "Username: admin\n";
            echo "Password: admin123\n";
        } else {
            echo "Admin user already exists.\n";
        }

        // Insert test admin user fkenny
        $fkennyData = [
            'user_code' => 'ADMIN002',
            'username' => 'fkenny',
            'email' => '<EMAIL>',
            'name' => 'F <PERSON>',
            'role' => 'admin',
            'password_hash' => password_hash('dakoii', PASSWORD_ARGON2ID),
            'is_activated' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'created_by' => 1,
        ];
        $existingFkenny = $builder->where('username', 'fkenny')->get()->getRow();
        if (!$existingFkenny) {
            $builder->insert($fkennyData);
            echo "Test admin user created:\n";
            echo "Username: fkenny\n";
            echo "Password: dakoii\n";
        } else {
            echo "Test admin user 'fkenny' already exists.\n";
        }
    }
}
