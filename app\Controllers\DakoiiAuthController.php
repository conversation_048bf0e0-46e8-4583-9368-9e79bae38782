<?php

namespace App\Controllers;

use App\Models\DakoiiUserModel;
use CodeIgniter\Controller;

class DakoiiAuthController extends BaseController
{
    protected $dakoiiUserModel;
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();
    }

    private function getDakoiiUserModel()
    {
        if (!$this->dakoiiUserModel) {
            $this->dakoiiUserModel = new DakoiiUserModel();
        }
        return $this->dakoiiUserModel;
    }

    /**
     * Show login form
     * Task 1.1: showLoginForm
     */
    public function showLoginForm()
    {
        // Check if user is already logged in
        if ($this->session->get('dakoii_user_id')) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        $data = [
            'title' => 'Dakoii Portal Login',
            'validation' => \Config\Services::validation(),
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('dakoii/dakoii_login', $data);
    }

    /**
     * Authenticate user credentials
     * Task 1.2: authenticateUser
     */
    public function authenticateUser()
    {
        // Check if request method is POST
        if ($this->request->getMethod() !== 'POST') {
            return redirect()->back()->with('error', 'Invalid request method.');
        }

        // Validation rules
        $rules = [
            'identifier' => [
                'label' => 'Email/Username',
                'rules' => 'required|min_length[3]',
                'errors' => [
                    'required' => 'Email or username is required.',
                    'min_length' => 'Email or username must be at least 3 characters long.'
                ]
            ],
            'password' => [
                'label' => 'Password',
                'rules' => 'required|min_length[4]',
                'errors' => [
                    'required' => 'Password is required.',
                    'min_length' => 'Password must be at least 4 characters long.'
                ]
            ]
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $identifier = $this->request->getPost('identifier');
        $password = $this->request->getPost('password');
        $rememberMe = $this->request->getPost('remember_me');

        // Find user by email or username
        $user = $this->getDakoiiUserModel()
            ->groupStart()
            ->where('email', $identifier)
            ->orWhere('username', $identifier)
            ->groupEnd()
            ->where('deleted_at', null)
            ->first();

        if (!$user) {
            return redirect()->back()->withInput()->with('error', 'Invalid credentials. Please try again.');
        }

        // Check if user is activated
        if (!$user['is_activated']) {
            return redirect()->back()->withInput()->with('error', 'Your account is not activated. Please check your email for activation instructions.');
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            // Log failed login attempt
            log_message('info', 'Failed login attempt for user: ' . $identifier . ' from IP: ' . $this->request->getIPAddress());
            
            return redirect()->back()->withInput()->with('error', 'Invalid credentials. Please try again.');
        }

        // Successful authentication
        $this->createUserSession($user, $rememberMe);

        // Update last login
        $this->getDakoiiUserModel()->update($user['id'], [
            'last_login_at' => date('Y-m-d H:i:s'),
            'updated_by' => $user['id']
        ]);

        // Log successful login
        log_message('info', 'Successful login for user: ' . $user['username'] . ' (ID: ' . $user['id'] . ') from IP: ' . $this->request->getIPAddress());

        // Log authentication event to audit trail
        try {
            $auditService = new \App\Libraries\AuditService();
            $auditService->logAuthentication(
                'login',
                "Dakoii user {$user['username']} logged in successfully",
                $user['id']
            );
        } catch (\Exception $e) {
            log_message('error', 'Failed to log authentication audit: ' . $e->getMessage());
        }

        // Redirect to intended URL or dashboard
        $intendedUrl = $this->session->get('intended_url') ?? base_url('dakoii/dashboard');
        $this->session->remove('intended_url');

        return redirect()->to($intendedUrl)->with('success', 'Welcome back, ' . $user['name'] . '!');
    }

    /**
     * Create user session
     * Task 1.2: Session management implementation
     */
    private function createUserSession($user, $rememberMe = false)
    {
        // Regenerate session ID for security
        $this->session->regenerate();

        // Set session data for audit trail compatibility
        $sessionData = [
            'dakoii_user_id' => $user['id'],
            'dakoii_username' => $user['username'],
            'dakoii_name' => $user['name'],
            'dakoii_user_full_name' => $user['name'], // For audit trail
            'dakoii_email' => $user['email'],
            'dakoii_role' => $user['role'],
            'dakoii_logged_in' => true,
            'dakoii_login_time' => time()
        ];

        $this->session->set($sessionData);

        // Handle "Remember Me" functionality
        if ($rememberMe) {
            $this->setRememberMeCookie($user['id']);
        }
    }

    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie($userId)
    {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days

        // Store token in database (you might want to create a separate table for this)
        // For now, we'll use the password_reset_token field temporarily
        $this->getDakoiiUserModel()->update($userId, [
            'password_reset_token' => $token,
            'updated_by' => $userId
        ]);

        // Set cookie
        setcookie('dakoii_remember', $userId . ':' . $token, $expiry, '/', '', true, true);
    }

    /**
     * Logout user
     * Task 1.3: logoutUser
     */
    public function logoutUser()
    {
        $userId = $this->session->get('dakoii_user_id');
        $username = $this->session->get('dakoii_username');

        // Log authentication event to audit trail before destroying session
        if ($userId && $username) {
            try {
                $auditService = new \App\Libraries\AuditService();
                $auditService->logAuthentication(
                    'logout',
                    "Dakoii user {$username} logged out",
                    $userId
                );
            } catch (\Exception $e) {
                log_message('error', 'Failed to log logout audit: ' . $e->getMessage());
            }
        }

        // Log logout
        if ($userId) {
            log_message('info', 'User logout: ' . $username . ' (ID: ' . $userId . ') from IP: ' . $this->request->getIPAddress());
        }

        // Clear remember me cookie
        if (isset($_COOKIE['dakoii_remember'])) {
            setcookie('dakoii_remember', '', time() - 3600, '/', '', true, true);
        }

        // Destroy session
        $this->session->destroy();

        // Redirect to login with success message
        return redirect()->to(base_url('dakoii'))->with('success', 'You have been successfully logged out.');
    }

    /**
     * Check remember me cookie and auto-login
     */
    public function checkRememberMe()
    {
        if (!isset($_COOKIE['dakoii_remember']) || $this->session->get('dakoii_user_id')) {
            return false;
        }

        $cookieValue = $_COOKIE['dakoii_remember'];
        $parts = explode(':', $cookieValue);

        if (count($parts) !== 2) {
            return false;
        }

        $userId = $parts[0];
        $token = $parts[1];

        $user = $this->getDakoiiUserModel()->find($userId);

        if ($user && $user['password_reset_token'] === $token && $user['is_activated']) {
            $this->createUserSession($user);
            return true;
        }

        // Invalid token, clear cookie
        setcookie('dakoii_remember', '', time() - 3600, '/', '', true, true);
        return false;
    }

    /**
     * Show password reset request form (GET)
     */
    public function showPasswordResetForm()
    {
        return view('auth/password_reset_request');
    }

    /**
     * Process password reset request (POST)
     */
    public function processPasswordReset()
    {
        $email = $this->request->getPost('email');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return redirect()->back()->with('error', 'Please enter a valid email address.');
        }

        $user = $this->getDakoiiUserModel()->where('email', $email)->first();

        if ($user) {
            $token = bin2hex(random_bytes(32));

            $this->getDakoiiUserModel()->update($user['id'], [
                'password_reset_token' => $token,
                'updated_by' => $user['id']
            ]);

            // Send password reset email with professional template
            $this->sendPasswordResetEmail($user, $token);

            log_message('info', 'Password reset requested for user: ' . $user['username']);
        }

        // Always show success message for security
        return redirect()->back()->with('success', 'If an account with that email exists, password reset instructions have been sent.');
    }

    /**
     * Show reset password form (GET)
     */
    public function showResetPasswordForm($token = null)
    {
        if (!$token) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Invalid reset token.');
        }

        $user = $this->getDakoiiUserModel()->where('password_reset_token', $token)->first();

        if (!$user) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Invalid or expired reset token.');
        }

        return view('auth/password_reset', ['token' => $token]);
    }

    /**
     * Process reset password (POST)
     */
    public function processResetPassword($token = null)
    {
        if (!$token) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Invalid reset token.');
        }

        $user = $this->getDakoiiUserModel()->where('password_reset_token', $token)->first();

        if (!$user) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Invalid or expired reset token.');
        }

        $rules = [
            'password' => 'required|min_length[4]',
            'password_confirm' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('errors', $this->validator->getErrors());
        }

        $newPassword = $this->request->getPost('password');
        $hashedPassword = password_hash($newPassword, PASSWORD_ARGON2ID);

        $this->getDakoiiUserModel()->update($user['id'], [
            'password_hash' => $hashedPassword,
            'password_reset_token' => null,
            'updated_by' => $user['id']
        ]);

        log_message('info', 'Password reset completed for user: ' . $user['username']);

        return redirect()->to(base_url('dakoii'))->with('success', 'Your password has been reset successfully. Please log in with your new password.');
    }

    /**
     * Send password reset email to user
     */
    private function sendPasswordResetEmail($user, $token)
    {
        try {
            $email = \Config\Services::email();
            $email->setFrom('<EMAIL>', 'Dakoii Portal');
            $email->setTo($user['email']);
            $email->setSubject('Dakoii Portal - Password Reset Request');

            // Create reset URL
            $resetUrl = base_url("dakoii/password-reset/{$token}");

            // Prepare template data
            $templateData = [
                'name' => $user['name'],
                'username' => $user['username'],
                'email' => $user['email'],
                'reset_url' => $resetUrl,
                'subject' => 'Dakoii Portal - Password Reset Request'
            ];

            // Render the professional email template
            $message = view('emails/password_reset_email', $templateData);

            $email->setMessage($message);
            $result = $email->send();

            if (!$result) {
                log_message('error', 'Failed to send password reset email to: ' . $user['email']);
            }

            return $result;
        } catch (\Exception $e) {
            log_message('error', 'Email sending exception: ' . $e->getMessage());
            return false;
        }
    }
}
