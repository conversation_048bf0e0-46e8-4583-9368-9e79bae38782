<?php

namespace App\Models;

/**
 * Project Model
 * 
 * Handles the main project records with comprehensive project information
 * including location, status, certification, and evaluation details.
 */
class ProjectModel extends BaseModel
{
    protected $table      = 'projects';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'org_id', 'pro_code', 'other_project_ids', 'title', 'goal', 'description',
        'initiation_date', 'start_date', 'end_date', 'address_line', 'country_id',
        'province_id', 'district_id', 'llg_id', 'ward_name', 'village_name',
        'gps_point', 'gps_kml_path', 'status', 'status_notes', 'status_at',
        'status_by_id', 'officer_certified', 'officer_cert_at', 'officer_cert_by',
        'contractor_certified', 'contractor_cert_at', 'contractor_cert_by',
        'evaluation_file', 'evaluation_notes', 'evaluation_date', 'evaluation_by',
        'baseline_year', 'target_year', 'project_budget', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'org_id'    => 'required|integer',
        'pro_code'  => 'required|max_length[20]',
        'title'     => 'required|max_length[200]',
        'status'    => 'in_list[planning,active,on-hold,completed,cancelled]'
    ];
    
    protected $validationMessages = [
        'pro_code' => [
            'required' => 'Project code is required',
            'is_unique' => 'Project code must be unique'
        ],
        'title' => [
            'required' => 'Project title is required'
        ]
    ];
    
    /**
     * Get projects by organization
     */
    public function getByOrganization(int $orgId, ?string $status = null): array
    {
        $query = $this->where('org_id', $orgId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get projects by status
     */
    public function getByStatus(string $status): array
    {
        return $this->where('status', $status)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get projects by location
     */
    public function getByLocation(array $locationFilters): array
    {
        $query = $this;
        
        if (!empty($locationFilters['country_id'])) {
            $query = $query->where('country_id', $locationFilters['country_id']);
        }
        
        if (!empty($locationFilters['province_id'])) {
            $query = $query->where('province_id', $locationFilters['province_id']);
        }
        
        if (!empty($locationFilters['district_id'])) {
            $query = $query->where('district_id', $locationFilters['district_id']);
        }
        
        if (!empty($locationFilters['llg_id'])) {
            $query = $query->where('llg_id', $locationFilters['llg_id']);
        }
        
        return $query->orderBy('title', 'ASC')->findAll();
    }
    
    /**
     * Get certified projects
     */
    public function getCertifiedProjects(string $certificationType = 'both'): array
    {
        $query = $this;
        
        switch ($certificationType) {
            case 'officer':
                $query = $query->where('officer_certified', 1);
                break;
            case 'contractor':
                $query = $query->where('contractor_certified', 1);
                break;
            case 'both':
                $query = $query->where('officer_certified', 1)
                              ->where('contractor_certified', 1);
                break;
        }
        
        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get project statistics
     */
    public function getProjectStatistics(): array
    {
        $stats = [];
        
        // Count by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // Certification statistics
        $stats['officer_certified'] = $this->where('officer_certified', 1)->countAllResults();
        $stats['contractor_certified'] = $this->where('contractor_certified', 1)->countAllResults();
        $stats['fully_certified'] = $this->where('officer_certified', 1)
                                         ->where('contractor_certified', 1)
                                         ->countAllResults();
        
        // Total projects
        $stats['total'] = $this->countAllResults();
        
        return $stats;
    }
    
    /**
     * Update project status
     */
    public function updateStatus(int $projectId, string $status, ?string $notes = null, ?int $userId = null): bool
    {
        $data = [
            'status' => $status,
            'status_notes' => $notes,
            'status_at' => date('Y-m-d H:i:s'),
            'status_by_id' => $userId,
            'updated_by' => $userId
        ];
        
        return $this->update($projectId, $data);
    }
    
    /**
     * Certify project
     */
    public function certifyProject(int $projectId, string $type, int $userId): bool
    {
        $data = [
            $type . '_certified' => 1,
            $type . '_cert_at' => date('Y-m-d H:i:s'),
            $type . '_cert_by' => $userId,
            'updated_by' => $userId
        ];
        
        return $this->update($projectId, $data);
    }
}
