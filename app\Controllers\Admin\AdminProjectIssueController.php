<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectIssuesAddressedModel;

/**
 * Admin Project Issues Addressed Controller
 * 
 * Handles CRUD operations for project issues addressed in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectIssueController extends BaseController
{
    protected $projectModel;
    protected $projectIssuesAddressedModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectIssuesAddressedModel = new ProjectIssuesAddressedModel();
    }

    /**
     * Show issues list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get issues for this project
        $issues = $this->projectIssuesAddressedModel->getByProject($projectId);

        // Get issue statistics
        $issueStats = $this->projectIssuesAddressedModel->getIssuesStatistics($projectId);

        $data = [
            'title' => 'Issues Addressed - PROMIS Admin',
            'page_title' => 'Issues Addressed',
            'project' => $project,
            'issues' => $issues,
            'issueStats' => $issueStats
        ];

        return view('admin/admin_projects_issues_list', $data);
    }

    /**
     * Show create issue form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Add Issue Addressed - PROMIS Admin',
            'page_title' => 'Add Issue Addressed',
            'project' => $project
        ];

        return view('admin/admin_projects_issues_create', $data);
    }

    /**
     * Store issue - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'issue_type' => 'required|in_list[direct,indirect]',
            'description' => 'required|max_length[500]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data
        $issueData = [
            'project_id' => $projectId,
            'issue_type' => $this->request->getPost('issue_type'),
            'description' => $this->request->getPost('description'),
            'created_by' => $adminUserId
        ];

        try {
            $issueId = $this->projectIssuesAddressedModel->insert($issueData);

            if ($issueId) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                               ->with('success', 'Issue addressed added successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectIssuesAddressedModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error adding issue addressed: ' . $e->getMessage());
        }
    }

    /**
     * Show edit issue form - GET request
     */
    public function edit($projectId, $issueId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get issue
        $issue = $this->projectIssuesAddressedModel->where('id', $issueId)
                                                  ->where('project_id', $projectId)
                                                  ->where('deleted_at', null)
                                                  ->first();

        if (!$issue) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                           ->with('error', 'Issue not found.');
        }

        $data = [
            'title' => 'Edit Issue Addressed - PROMIS Admin',
            'page_title' => 'Edit Issue Addressed',
            'project' => $project,
            'issue' => $issue
        ];

        return view('admin/admin_projects_issues_edit', $data);
    }

    /**
     * Update issue - POST request
     */
    public function update($projectId, $issueId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get issue
        $issue = $this->projectIssuesAddressedModel->where('id', $issueId)
                                                  ->where('project_id', $projectId)
                                                  ->where('deleted_at', null)
                                                  ->first();

        if (!$issue) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                           ->with('error', 'Issue not found.');
        }

        // Validation rules
        $rules = [
            'issue_type' => 'required|in_list[direct,indirect]',
            'description' => 'required|max_length[500]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare data
        $issueData = [
            'issue_type' => $this->request->getPost('issue_type'),
            'description' => $this->request->getPost('description'),
            'updated_by' => $adminUserId
        ];

        try {
            $result = $this->projectIssuesAddressedModel->update($issueId, $issueData);

            if ($result) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                               ->with('success', 'Issue addressed updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('errors', $this->projectIssuesAddressedModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error updating issue addressed: ' . $e->getMessage());
        }
    }

    /**
     * Delete issue - POST request
     */
    public function delete($projectId, $issueId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminUserId = session()->get('admin_user_id');
        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get issue
        $issue = $this->projectIssuesAddressedModel->where('id', $issueId)
                                                  ->where('project_id', $projectId)
                                                  ->where('deleted_at', null)
                                                  ->first();

        if (!$issue) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                           ->with('error', 'Issue not found.');
        }

        try {
            // Set deleted_by before soft delete
            $this->projectIssuesAddressedModel->update($issueId, ['deleted_by' => $adminUserId]);
            $this->projectIssuesAddressedModel->delete($issueId);

            return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                           ->with('success', 'Issue addressed deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/issues'))
                           ->with('error', 'Error deleting issue addressed: ' . $e->getMessage());
        }
    }

    /**
     * Get issues data for project show view
     * This method is called by AdminProjectController to get issues data for the project profile
     */
    public function getIssuesForProjectShow($projectId)
    {
        // Get issues for this project
        $issues = $this->projectIssuesAddressedModel->getByProject($projectId);

        // Get issue statistics
        $issueStats = $this->projectIssuesAddressedModel->getIssuesStatistics($projectId);

        return [
            'issues' => $issues,
            'issueStats' => $issueStats
        ];
    }
}
