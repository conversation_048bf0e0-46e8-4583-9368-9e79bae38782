<?php

namespace App\Models;

/**
 * Contractor Model
 * 
 * Manages contractor information including business details,
 * contact information, and status tracking.
 */
class ContractorModel extends BaseModel
{
    protected $table      = 'contractors';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'org_id', 'contractor_code', 'name', 'business_type', 'registration_num',
        'tax_id', 'services_offered', 'description', 'contact_person',
        'contact_email', 'contact_phone', 'address_line1', 'address_line2',
        'city', 'state', 'postal_code', 'country_id', 'status', 'status_notes',
        'status_changed_at', 'status_changed_by', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'org_id'          => 'required|integer',
        'contractor_code' => 'required|max_length[20]|is_unique[contractors.contractor_code,id,{id}]',
        'name'            => 'required|max_length[150]',
        'business_type'   => 'in_list[company,individual,ngo,government]',
        'registration_num' => 'max_length[50]',
        'tax_id'          => 'max_length[50]',
        'contact_person'  => 'max_length[100]',
        'contact_email'   => 'valid_email|max_length[100]',
        'contact_phone'   => 'max_length[30]',
        'address_line1'   => 'max_length[150]',
        'address_line2'   => 'max_length[150]',
        'city'            => 'max_length[100]',
        'state'           => 'max_length[100]',
        'postal_code'     => 'max_length[20]',
        'country_id'      => 'integer',
        'status'          => 'in_list[active,suspended,terminated,blacklisted]'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required'
        ],
        'contractor_code' => [
            'required' => 'Contractor code is required',
            'is_unique' => 'Contractor code already exists'
        ],
        'name' => [
            'required' => 'Contractor name is required'
        ],
        'contact_email' => [
            'valid_email' => 'Please enter a valid email address'
        ]
    ];
    
    /**
     * Get contractors by organization
     */
    public function getByOrganization(int $orgId, ?string $status = null): array
    {
        $query = $this->where('org_id', $orgId);
        
        if ($status) {
            $query = $query->where('status', $status);
        }
        
        return $query->orderBy('name', 'ASC')->findAll();
    }
    
    /**
     * Get active contractors
     */
    public function getActiveContractors(int $orgId): array
    {
        return $this->where('org_id', $orgId)
                   ->where('status', 'active')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get contractor with country information
     */
    public function getWithCountry(int $id): ?array
    {
        return $this->select('contractors.*, countries.name as country_name')
                   ->join('countries', 'countries.id = contractors.country_id', 'left')
                   ->where('contractors.id', $id)
                   ->first();
    }
    
    /**
     * Search contractors
     */
    public function searchContractors(int $orgId, string $search): array
    {
        return $this->where('org_id', $orgId)
                   ->groupStart()
                       ->like('name', $search)
                       ->orLike('contractor_code', $search)
                       ->orLike('contact_person', $search)
                       ->orLike('contact_email', $search)
                   ->groupEnd()
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Update contractor status
     */
    public function updateStatus(int $id, string $status, ?string $notes = null, ?int $userId = null): bool
    {
        $data = [
            'status' => $status,
            'status_notes' => $notes,
            'status_changed_at' => date('Y-m-d H:i:s'),
            'status_changed_by' => $userId
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * Get contractor statistics by organization
     */
    public function getStatistics(int $orgId): array
    {
        $stats = [];
        
        // Total contractors
        $stats['total'] = $this->where('org_id', $orgId)->countAllResults();
        
        // By status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->where('org_id', $orgId)
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        
        // By business type
        $typeCounts = $this->select('business_type, COUNT(*) as count')
                          ->where('org_id', $orgId)
                          ->groupBy('business_type')
                          ->findAll();
        
        $stats['by_type'] = array_column($typeCounts, 'count', 'business_type');
        
        return $stats;
    }
    
    /**
     * Generate contractor code
     */
    public function generateContractorCode(int $orgId): string
    {
        $prefix = 'CON';
        $year = date('Y');
        
        // Get the last contractor code for this organization and year
        $lastContractor = $this->select('contractor_code')
                              ->where('org_id', $orgId)
                              ->like('contractor_code', $prefix . '-' . $year, 'after')
                              ->orderBy('contractor_code', 'DESC')
                              ->first();
        
        if ($lastContractor) {
            // Extract the sequence number and increment
            $parts = explode('-', $lastContractor['contractor_code']);
            $sequence = isset($parts[2]) ? intval($parts[2]) + 1 : 1;
        } else {
            $sequence = 1;
        }
        
        return sprintf('%s-%s-%03d', $prefix, $year, $sequence);
    }
    
    /**
     * Check if contractor has active projects
     */
    public function hasActiveProjects(int $contractorId): bool
    {
        $projectContractorModel = new ProjectContractorModel();
        return $projectContractorModel->where('contractor_id', $contractorId)
                                     ->where('is_active', 1)
                                     ->countAllResults() > 0;
    }
}
