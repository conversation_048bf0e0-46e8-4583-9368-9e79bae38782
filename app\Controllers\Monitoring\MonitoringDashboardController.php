<?php

namespace App\Controllers\Monitoring;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;

/**
 * Monitoring Dashboard Controller for PROMIS
 * 
 * Handles the project monitoring dashboard focused on project tracking and reports
 */
class MonitoringDashboardController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
        $this->session = \Config\Services::session();
    }

    /**
     * Display monitoring dashboard - GET request
     */
    public function index()
    {
        // Check if user is logged in
        if (!$this->session->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        // Get dashboard statistics
        $stats = $this->getMonitoringStats();
        
        // Get project activities
        $projectActivities = $this->getProjectActivities();
        
        // Get current user info
        $currentUser = $this->userModel->find($this->session->get('admin_user_id'));
        
        $data = [
            'title' => 'Project Monitoring - PROMIS',
            'page_title' => 'Project Monitoring Dashboard',
            'stats' => $stats,
            'project_activities' => $projectActivities,
            'current_user' => $currentUser
        ];

        return view('monitoring/monitoring_dashboard', $data);
    }

    /**
     * Get monitoring-specific statistics
     */
    private function getMonitoringStats()
    {
        // Get user's organization
        $userId = $this->session->get('admin_user_id');
        $user = $this->userModel->find($userId);
        $organizationId = $user['organization_id'] ?? null;

        $stats = [
            'total_projects' => 0,
            'active_projects' => 0,
            'completed_projects' => 0,
            'pending_projects' => 0,
            'overdue_projects' => 0,
            'on_track_projects' => 0,
            'total_budget' => 0,
            'spent_budget' => 0,
            'remaining_budget' => 0,
            'budget_utilization' => 0
        ];

        // If we had a ProjectModel, we would filter by organization
        // For now, return sample data
        if ($organizationId) {
            $stats = [
                'total_projects' => 12,
                'active_projects' => 8,
                'completed_projects' => 3,
                'pending_projects' => 1,
                'overdue_projects' => 2,
                'on_track_projects' => 6,
                'total_budget' => 1500000,
                'spent_budget' => 850000,
                'remaining_budget' => 650000,
                'budget_utilization' => 56.7
            ];
        }

        return $stats;
    }

    /**
     * Get project activities
     */
    private function getProjectActivities()
    {
        // Sample project activities data
        return [
            [
                'project_name' => 'Road Construction Phase 1',
                'activity' => 'Milestone Completed',
                'description' => 'Foundation work completed ahead of schedule',
                'status' => 'completed',
                'timestamp' => '2 hours ago',
                'progress' => 75,
                'budget_used' => 450000,
                'icon' => '🛣️',
                'color' => 'green'
            ],
            [
                'project_name' => 'School Building Project',
                'activity' => 'Status Update',
                'description' => 'Structural work in progress, 60% complete',
                'status' => 'in_progress',
                'timestamp' => '5 hours ago',
                'progress' => 60,
                'budget_used' => 320000,
                'icon' => '🏫',
                'color' => 'blue'
            ],
            [
                'project_name' => 'Water Supply System',
                'activity' => 'Issue Reported',
                'description' => 'Delay in material delivery, requires attention',
                'status' => 'delayed',
                'timestamp' => '1 day ago',
                'progress' => 35,
                'budget_used' => 180000,
                'icon' => '💧',
                'color' => 'orange'
            ],
            [
                'project_name' => 'Community Center',
                'activity' => 'Budget Review',
                'description' => 'Monthly budget review completed',
                'status' => 'on_track',
                'timestamp' => '2 days ago',
                'progress' => 45,
                'budget_used' => 275000,
                'icon' => '🏛️',
                'color' => 'blue'
            ],
            [
                'project_name' => 'Bridge Construction',
                'activity' => 'Phase Started',
                'description' => 'Engineering phase has begun',
                'status' => 'started',
                'timestamp' => '3 days ago',
                'progress' => 15,
                'budget_used' => 95000,
                'icon' => '🌉',
                'color' => 'green'
            ]
        ];
    }

    /**
     * Get project summary data for AJAX requests
     */
    public function getProjectSummary()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        $projectId = $this->request->getGet('project_id');
        
        // Sample project summary data
        $summary = [
            'project_name' => 'Road Construction Phase 1',
            'status' => 'In Progress',
            'progress' => 75,
            'start_date' => '2024-01-15',
            'end_date' => '2024-06-30',
            'budget_total' => 600000,
            'budget_used' => 450000,
            'team_size' => 12,
            'milestones_completed' => 6,
            'milestones_total' => 8,
            'last_update' => '2024-01-20 14:30:00'
        ];

        return $this->response->setJSON($summary);
    }

    /**
     * Export monitoring data
     */
    public function exportMonitoringData()
    {
        // Check permissions
        if (!$this->session->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $format = $this->request->getGet('format') ?? 'json';
        $stats = $this->getMonitoringStats();
        $activities = $this->getProjectActivities();

        $data = [
            'export_date' => date('Y-m-d H:i:s'),
            'exported_by' => $this->session->get('admin_username'),
            'organization' => $this->session->get('admin_organization_name'),
            'monitoring_statistics' => $stats,
            'project_activities' => $activities
        ];

        switch ($format) {
            case 'json':
                return $this->response->setJSON($data)
                                     ->setHeader('Content-Disposition', 'attachment; filename="monitoring_export_' . date('Y-m-d') . '.json"');
                                     
            case 'csv':
                // Convert to CSV format
                $csv = "Metric,Value\n";
                foreach ($stats as $key => $value) {
                    $csv .= ucfirst(str_replace('_', ' ', $key)) . "," . $value . "\n";
                }
                
                return $this->response->setBody($csv)
                                     ->setHeader('Content-Type', 'text/csv')
                                     ->setHeader('Content-Disposition', 'attachment; filename="monitoring_stats_' . date('Y-m-d') . '.csv"');
                                     
            default:
                return redirect()->back()->with('error', 'Invalid export format');
        }
    }

    /**
     * Get real-time project updates
     */
    public function getProjectUpdates()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        // Get latest project updates
        $updates = $this->getProjectActivities();
        
        return $this->response->setJSON([
            'success' => true,
            'updates' => array_slice($updates, 0, 5),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
