<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectOfficerModel;
use App\Models\UserModel;

/**
 * Admin Project Officer Controller
 * 
 * Handles project officer management for the PROMIS Admin Portal including:
 * - Listing project officers
 * - Assigning officers to projects
 * - Removing officers from projects
 * - Role management (lead, certifier, support)
 */
class AdminProjectOfficerController extends BaseController
{
    protected $projectModel;
    protected $projectOfficerModel;
    protected $userModel;
    
    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectOfficerModel = new ProjectOfficerModel();
        $this->userModel = new UserModel();
    }

    /**
     * Display project officers list - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get project officers
        $officers = $this->projectOfficerModel->getByProject($projectId);

        // Get officer statistics
        $stats = [
            'total_officers' => count($officers),
            'by_role' => [
                'lead' => 0,
                'certifier' => 0,
                'support' => 0
            ]
        ];

        foreach ($officers as $officer) {
            if (isset($stats['by_role'][$officer['role']])) {
                $stats['by_role'][$officer['role']]++;
            }
        }

        $data = [
            'title' => 'Project Officers - PROMIS Admin',
            'page_title' => 'Project Officers',
            'project' => $project,
            'officers' => $officers,
            'stats' => $stats
        ];

        return view('admin/admin_projects_officers_list', $data);
    }

    /**
     * Show assign officer form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get available project officers from the same organization
        $availableOfficers = $this->userModel->where('organization_id', $adminOrganizationId)
                                            ->where('is_project_officer', 1)
                                            ->where('is_activated', 1)
                                            ->where('deleted_at', null)
                                            ->findAll();

        // Get current project officers to exclude from dropdown
        $currentOfficers = $this->projectOfficerModel->getByProject($projectId);
        $currentOfficerIds = array_column($currentOfficers, 'user_id');

        // Filter out already assigned officers
        $availableOfficers = array_filter($availableOfficers, function($officer) use ($currentOfficerIds) {
            return !in_array($officer['id'], $currentOfficerIds);
        });

        $data = [
            'title' => 'Assign Project Officer - PROMIS Admin',
            'page_title' => 'Assign Project Officer',
            'project' => $project,
            'availableOfficers' => $availableOfficers,
            'currentOfficers' => $currentOfficers
        ];

        return view('admin/admin_projects_officers_create', $data);
    }

    /**
     * Process assign officer form - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'user_id' => 'required|integer',
            'role' => 'required|in_list[lead,certifier,support]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $userId = $this->request->getPost('user_id');
        $role = $this->request->getPost('role');

        // Verify the user is a project officer in the same organization
        $officer = $this->userModel->where('id', $userId)
                                  ->where('organization_id', $adminOrganizationId)
                                  ->where('is_project_officer', 1)
                                  ->where('is_activated', 1)
                                  ->where('deleted_at', null)
                                  ->first();

        if (!$officer) {
            return redirect()->back()->withInput()->with('error', 'Invalid officer selection.');
        }

        // Check if this is the first officer being assigned (auto-make them lead)
        $currentOfficers = $this->projectOfficerModel->getByProject($projectId);
        if (empty($currentOfficers) && $role !== 'lead') {
            $role = 'lead'; // First officer automatically becomes lead
        }

        // Check if trying to assign lead role when one already exists
        if ($role === 'lead') {
            $existingLead = $this->projectOfficerModel->getProjectLead($projectId);
            if ($existingLead) {
                return redirect()->back()->withInput()->with('error', 'A project lead already exists. Please remove the current lead first or assign a different role.');
            }
        }

        try {
            $success = $this->projectOfficerModel->addOfficer($projectId, $userId, $role, $adminUserId);
            
            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/officers'))
                               ->with('success', 'Project officer assigned successfully!');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to assign officer. They may already be assigned to this project.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error assigning officer: ' . $e->getMessage());
        }
    }

    /**
     * Remove officer from project - POST request
     */
    public function remove($projectId, $officerId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get the officer assignment
        $assignment = $this->projectOfficerModel->where('id', $officerId)
                                               ->where('project_id', $projectId)
                                               ->where('is_active', 1)
                                               ->first();

        if (!$assignment) {
            return redirect()->back()->with('error', 'Officer assignment not found.');
        }

        // Get removal reason from POST data
        $removalReason = $this->request->getPost('removal_reason') ?: 'Removed by admin';

        try {
            $success = $this->projectOfficerModel->removeOfficer(
                $projectId, 
                $assignment['user_id'], 
                $assignment['role'], 
                $removalReason, 
                $adminUserId
            );
            
            if ($success) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/officers'))
                               ->with('success', 'Project officer removed successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to remove officer.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error removing officer: ' . $e->getMessage());
        }
    }
}
