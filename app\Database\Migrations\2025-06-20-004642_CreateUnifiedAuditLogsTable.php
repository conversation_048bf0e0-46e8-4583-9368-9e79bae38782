<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateUnifiedAuditLogsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id'          => ['type' => 'BIGINT', 'auto_increment' => true],
            'table_name'  => ['type' => 'VARCHAR', 'constraint' => 100, 'comment' => 'Name of the table that was modified'],
            'primary_key' => ['type' => 'VARCHAR', 'constraint' => 64, 'comment' => 'Primary key value of the affected record'],
            'action'      => ['type' => 'ENUM', 'constraint' => ['create','update','delete','login','logout','access'], 'comment' => 'Type of operation performed'],
            'old_data'    => ['type' => 'MEDIUMTEXT', 'null' => true, 'comment' => 'JSON of data before change (for updates/deletes)'],
            'new_data'    => ['type' => 'MEDIUMTEXT', 'null' => true, 'comment' => 'JSON of data after change (for creates/updates)'],

            // User identification fields (flexible for different portal users)
            'user_id'     => ['type' => 'BIGINT', 'null' => true, 'comment' => 'ID of the user who performed the action'],
            'username'    => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true, 'comment' => 'Username of the user'],
            'user_type'   => ['type' => 'VARCHAR', 'constraint' => 20, 'null' => true, 'comment' => 'Type of user (dakoii_user, admin_user, project_officer, etc.)'],
            'user_full_name' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true, 'comment' => 'Full name of the user'],

            // Organization tracking
            'organization_id' => ['type' => 'BIGINT', 'null' => true, 'comment' => 'ID of the organization the user belongs to'],
            'organization_name' => ['type' => 'VARCHAR', 'constraint' => 100, 'null' => true, 'comment' => 'Name of the organization'],
            'organization_type' => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true, 'comment' => 'Type of organization (NGO, Government, etc.)'],

            // Project tracking (for project-related activities)
            'project_id'  => ['type' => 'BIGINT', 'null' => true, 'comment' => 'ID of the project if action is project-related'],
            'project_title' => ['type' => 'VARCHAR', 'constraint' => 200, 'null' => true, 'comment' => 'Title of the project'],

            // Portal and context information
            'portal'      => ['type' => 'ENUM', 'constraint' => ['dakoii','admin','monitor'], 'comment' => 'Portal where the action was performed'],
            'module'      => ['type' => 'VARCHAR', 'constraint' => 50, 'null' => true, 'comment' => 'Module/section within the portal'],

            // Technical details
            'ip_address'  => ['type' => 'VARCHAR', 'constraint' => 45, 'null' => true, 'comment' => 'IP address of the user'],
            'user_agent'  => ['type' => 'TEXT', 'null' => true, 'comment' => 'Browser/client user agent string'],
            'session_id'  => ['type' => 'VARCHAR', 'constraint' => 128, 'null' => true, 'comment' => 'Session ID'],
            'request_url' => ['type' => 'VARCHAR', 'constraint' => 255, 'null' => true, 'comment' => 'URL where the action was performed'],

            // Additional context
            'description' => ['type' => 'TEXT', 'null' => true, 'comment' => 'Human-readable description of the action'],
            'created_at'  => ['type' => 'DATETIME', 'null' => true, 'comment' => 'When the audit log was created'],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('table_name');
        $this->forge->addKey('user_id');
        $this->forge->addKey('portal');
        $this->forge->addKey('action');
        $this->forge->addKey('created_at');
        $this->forge->addKey('user_type');
        $this->forge->addKey('module');
        $this->forge->addKey('organization_id');
        $this->forge->addKey('project_id');
        $this->forge->addKey(['portal', 'organization_id']);
        $this->forge->addKey(['portal', 'user_id']);
        $this->forge->addKey(['organization_id', 'user_id']);
        $this->forge->addKey(['project_id', 'action']);

        $this->forge->createTable('audit_logs');
    }

    public function down()
    {
        $this->forge->dropTable('audit_logs');
    }
}
