<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// PROMIS Authentication Routes
$routes->group('auth', function($routes) {
    // Login routes
    $routes->get('login', 'AuthController::showLoginForm');
    $routes->post('login', 'AuthController::authenticateUser');
    $routes->get('logout', 'AuthController::logoutUser');

    // Registration routes (for future use)
    $routes->get('register', 'AuthController::showRegistrationForm');
    $routes->post('register', 'AuthController::registerUser');
});

// Dakoii Portal Authentication Routes
$routes->group('dakoii', function($routes) {
    // Login routes
    $routes->get('/', 'DakoiiAuthController::showLoginForm');
    $routes->post('authenticate', 'DakoiiAuthController::authenticateUser');
    $routes->get('logout', 'DakoiiAuthController::logoutUser');

    // Password reset routes
    $routes->get('password-reset', 'DakoiiAuthController::requestPasswordReset');
    $routes->post('password-reset', 'DakoiiAuthController::requestPasswordReset');
    $routes->get('reset-password/(:any)', 'DakoiiAuthController::resetPassword/$1');
    $routes->post('reset-password/(:any)', 'DakoiiAuthController::resetPassword/$1');

    // Dashboard and admin routes (will be added later)
    $routes->get('dashboard', 'DakoiiDashboardController::index', ['filter' => 'dakoii_auth']);
    $routes->group('organizations', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiOrganizationController::listOrganisations');
        $routes->get('create', 'DakoiiOrganizationController::showCreateOrganisationForm');
        $routes->post('create', 'DakoiiOrganizationController::createOrganisation');
        $routes->get('(:num)', 'DakoiiOrganizationController::viewOrganisationProfile/$1');
        $routes->get('(:num)/edit', 'DakoiiOrganizationController::showEditOrganisationModal/$1');
        $routes->post('(:num)/update', 'DakoiiOrganizationController::updateOrganisation/$1');
        $routes->post('(:num)/toggle-status', 'DakoiiOrganizationController::toggleOrganisationStatus/$1');
        $routes->post('(:num)/license-status', 'DakoiiOrganizationController::changeOrganisationLicenseStatus/$1');
        $routes->post('(:num)/upload-images', 'DakoiiOrganizationController::uploadOrganisationImages/$1');
        $routes->delete('(:num)', 'DakoiiOrganizationController::softDeleteOrganisation/$1');

        // Organization admin routes
        $routes->get('(:num)/admins', 'DakoiiOrganizationController::listOrgAdmins/$1');
        $routes->get('(:num)/admins/create', 'DakoiiOrganizationController::showCreateAdminForm/$1');
        $routes->post('(:num)/admins/create', 'DakoiiOrganizationController::createOrgAdmin/$1');
        $routes->get('admins/(:num)', 'DakoiiOrganizationController::viewOrgAdminProfile/$1');
        $routes->get('admins/(:num)/edit', 'DakoiiOrganizationController::showEditAdminModal/$1');
        $routes->post('admins/(:num)/update', 'DakoiiOrganizationController::updateOrgAdmin/$1');
        $routes->post('admins/(:num)/toggle-status', 'DakoiiOrganizationController::toggleAdminStatus/$1');
        $routes->post('admins/(:num)/reset-password', 'DakoiiOrganizationController::resetAdminPassword/$1');
        $routes->delete('admins/(:num)', 'DakoiiOrganizationController::softDeleteOrgAdmin/$1');
    });

    // Admin activation route (public access)
    $routes->get('organizations/admins/activate/(:any)', 'DakoiiOrganizationController::activateAdmin/$1');

    // Dakoii Users Management routes (Function 6)
    $routes->group('users', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiUserController::listDakoiiUsers');
        $routes->get('create', 'DakoiiUserController::showCreateDakoiiUserForm');
        $routes->post('create', 'DakoiiUserController::createDakoiiUser');
        $routes->get('(:num)', 'DakoiiUserController::viewDakoiiUserProfile/$1');
        $routes->get('(:num)/edit', 'DakoiiUserController::showEditDakoiiUserModal/$1');
        $routes->post('(:num)/update', 'DakoiiUserController::updateDakoiiUser/$1');
        $routes->post('(:num)/toggle-status', 'DakoiiUserController::toggleDakoiiUserStatus/$1');
        $routes->post('(:num)/reset-password', 'DakoiiUserController::resetDakoiiUserPassword/$1');
        $routes->post('(:num)/resend-activation', 'DakoiiUserController::resendActivationEmail/$1');
        $routes->delete('(:num)', 'DakoiiUserController::softDeleteDakoiiUser/$1');
        $routes->post('bulk-action', 'DakoiiUserController::bulkAction');
    });

    // User activation route (public access)
    $routes->get('users/activate/(:any)', 'DakoiiUserController::completeUserActivation/$1');

    // Government Structure Management routes (Function 5)
    $routes->group('government', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiGovernmentController::index');
        // Countries
        $routes->get('countries', 'DakoiiGovernmentController::listCountries');
        $routes->get('countries/create', 'DakoiiGovernmentController::createCountry');
        $routes->post('countries/create', 'DakoiiGovernmentController::createCountry');
        $routes->get('countries/(:num)', 'DakoiiGovernmentController::showCountry/$1');
        $routes->get('countries/(:num)/edit', 'DakoiiGovernmentController::updateCountry/$1');
        $routes->post('countries/(:num)/edit', 'DakoiiGovernmentController::updateCountry/$1');
        $routes->post('countries/(:num)/delete', 'DakoiiGovernmentController::deleteCountry/$1');
        // Provinces
        $routes->get('provinces', 'DakoiiGovernmentController::listProvinces');
        $routes->get('provinces/create', 'DakoiiGovernmentController::createProvince');
        $routes->post('provinces/create', 'DakoiiGovernmentController::createProvince');
        $routes->get('provinces/(:num)', 'DakoiiGovernmentController::showProvince/$1');
        $routes->get('provinces/(:num)/edit', 'DakoiiGovernmentController::updateProvince/$1');
        $routes->post('provinces/(:num)/edit', 'DakoiiGovernmentController::updateProvince/$1');
        $routes->post('provinces/(:num)/delete', 'DakoiiGovernmentController::deleteProvince/$1');
        // Districts
        $routes->get('districts', 'DakoiiGovernmentController::listDistricts');
        $routes->get('districts/create', 'DakoiiGovernmentController::createDistrict');
        $routes->post('districts/create', 'DakoiiGovernmentController::createDistrict');
        $routes->get('districts/(:num)', 'DakoiiGovernmentController::showDistrict/$1');
        $routes->get('districts/(:num)/edit', 'DakoiiGovernmentController::updateDistrict/$1');
        $routes->post('districts/(:num)/edit', 'DakoiiGovernmentController::updateDistrict/$1');
        $routes->post('districts/(:num)/delete', 'DakoiiGovernmentController::deleteDistrict/$1');
        // LLGs
        $routes->get('llgs', 'DakoiiGovernmentController::listLlgs');
        $routes->get('llgs/create', 'DakoiiGovernmentController::createLlg');
        $routes->post('llgs/create', 'DakoiiGovernmentController::createLlg');
        $routes->get('llgs/(:num)', 'DakoiiGovernmentController::showLlg/$1');
        $routes->get('llgs/(:num)/edit', 'DakoiiGovernmentController::updateLlg/$1');
        $routes->post('llgs/(:num)/edit', 'DakoiiGovernmentController::updateLlg/$1');
        $routes->post('llgs/(:num)/delete', 'DakoiiGovernmentController::deleteLlg/$1');
    });
    // Hierarchy chart and map (public, no auth filter)
    $routes->get('government/chart', 'DakoiiGovernmentController::chart');
    $routes->get('government/map', 'DakoiiGovernmentController::map');
});

// Admin Portal Routes
$routes->group('admin', function($routes) {
    // Dashboard
    $routes->get('dashboard', 'Admin\AdminDashboardController::index', ['filter' => 'admin_auth']);
    $routes->get('/', 'Admin\AdminDashboardController::index', ['filter' => 'admin_auth']);

    // User Management Routes
    $routes->group('users', ['filter' => 'admin_auth'], function($routes) {
        // List users - GET
        $routes->get('/', 'Admin\AdminUserController::listUsers');

        // Create user step 1 - GET/POST
        $routes->get('create', 'Admin\AdminUserController::createUserStep1');
        $routes->post('create', 'Admin\AdminUserController::processUserStep1');

        // Create user step 2 - GET/POST
        $routes->get('create/step2/(:any)', 'Admin\AdminUserController::createUserStep2/$1');
        $routes->post('create/step2/(:any)', 'Admin\AdminUserController::processUserStep2/$1');

        // Edit user - GET/POST
        $routes->get('(:num)/edit', 'Admin\AdminUserController::editUser/$1');
        $routes->post('(:num)/edit', 'Admin\AdminUserController::updateUser/$1');

        // Password reset - GET/POST
        $routes->get('(:num)/reset-password', 'Admin\AdminUserController::showResetPasswordModal/$1');
        $routes->post('(:num)/reset-password', 'Admin\AdminUserController::processPasswordReset/$1');

        // Active sessions - GET/POST
        $routes->get('sessions', 'Admin\AdminUserController::viewActiveSessions');
        $routes->post('sessions/(:any)/terminate', 'Admin\AdminUserController::terminateSession/$1');
    });

    // Project Management Routes
    $routes->group('projects', ['filter' => 'admin_auth'], function($routes) {
        // List projects - GET
        $routes->get('/', 'Admin\AdminProjectController::index');

        // Create project - GET/POST
        $routes->get('create', 'Admin\AdminProjectController::create');
        $routes->post('create', 'Admin\AdminProjectController::store');

        // View project details - GET
        $routes->get('(:num)', 'Admin\AdminProjectController::show/$1');

        // Edit project - GET/POST
        $routes->get('(:num)/edit', 'Admin\AdminProjectController::edit/$1');
        $routes->post('(:num)/edit', 'Admin\AdminProjectController::update/$1');

        // Delete project - POST
        $routes->post('(:num)/delete', 'Admin\AdminProjectController::destroy/$1');

        // Project Phase Management Routes
        $routes->get('(:num)/phases/create', 'Admin\AdminProjectPhaseController::create/$1');
        $routes->post('(:num)/phases/create', 'Admin\AdminProjectPhaseController::store/$1');
        $routes->get('(:num)/phases/(:num)/edit', 'Admin\AdminProjectPhaseController::edit/$1/$2');
        $routes->post('(:num)/phases/(:num)/edit', 'Admin\AdminProjectPhaseController::update/$1/$2');
        $routes->post('(:num)/phases/(:num)/delete', 'Admin\AdminProjectPhaseController::delete/$1/$2');

        // Project Milestone Management Routes
        $routes->get('(:num)/milestones/create', 'Admin\AdminProjectMilestoneController::create/$1');
        $routes->post('(:num)/milestones/create', 'Admin\AdminProjectMilestoneController::store/$1');
        $routes->get('(:num)/milestones/(:num)/edit', 'Admin\AdminProjectMilestoneController::edit/$1/$2');
        $routes->post('(:num)/milestones/(:num)/edit', 'Admin\AdminProjectMilestoneController::update/$1/$2');
        $routes->post('(:num)/milestones/(:num)/delete', 'Admin\AdminProjectMilestoneController::delete/$1/$2');

        // Project Milestone Assessment Routes
        $routes->get('(:num)/milestones/(:num)/assessment', 'Admin\AdminProjectMilestoneAssessmentController::index/$1/$2');
        $routes->get('(:num)/milestones/(:num)/assessment/upload', 'Admin\AdminProjectMilestoneAssessmentController::upload/$1/$2');
        $routes->post('(:num)/milestones/(:num)/assessment/upload', 'Admin\AdminProjectMilestoneAssessmentController::store/$1/$2');
        $routes->post('(:num)/milestones/(:num)/assessment/(:num)/verify', 'Admin\AdminProjectMilestoneAssessmentController::verify/$1/$2/$3');
        $routes->post('(:num)/milestones/(:num)/assessment/(:num)/delete', 'Admin\AdminProjectMilestoneAssessmentController::delete/$1/$2/$3');

        // Project Budget Management Routes
        $routes->get('(:num)/budgets', 'Admin\AdminProjectBudgetController::index/$1');
        $routes->get('(:num)/budgets/create', 'Admin\AdminProjectBudgetController::create/$1');
        $routes->post('(:num)/budgets/create', 'Admin\AdminProjectBudgetController::store/$1');
        $routes->get('(:num)/budgets/(:num)/edit', 'Admin\AdminProjectBudgetController::edit/$1/$2');
        $routes->post('(:num)/budgets/(:num)/edit', 'Admin\AdminProjectBudgetController::update/$1/$2');
        $routes->post('(:num)/budgets/(:num)/delete', 'Admin\AdminProjectBudgetController::delete/$1/$2');

        // Project Outcomes Management Routes
        $routes->get('(:num)/outcomes', 'Admin\AdminProjectOutcomeController::index/$1');
        $routes->get('(:num)/outcomes/create', 'Admin\AdminProjectOutcomeController::create/$1');
        $routes->post('(:num)/outcomes/create', 'Admin\AdminProjectOutcomeController::store/$1');
        $routes->get('(:num)/outcomes/(:num)/edit', 'Admin\AdminProjectOutcomeController::edit/$1/$2');
        $routes->post('(:num)/outcomes/(:num)/edit', 'Admin\AdminProjectOutcomeController::update/$1/$2');
        $routes->post('(:num)/outcomes/(:num)/delete', 'Admin\AdminProjectOutcomeController::delete/$1/$2');

        // Project Issues Addressed Management Routes
        $routes->get('(:num)/issues', 'Admin\AdminProjectIssueController::index/$1');
        $routes->get('(:num)/issues/create', 'Admin\AdminProjectIssueController::create/$1');
        $routes->post('(:num)/issues/create', 'Admin\AdminProjectIssueController::store/$1');
        $routes->get('(:num)/issues/(:num)/edit', 'Admin\AdminProjectIssueController::edit/$1/$2');
        $routes->post('(:num)/issues/(:num)/edit', 'Admin\AdminProjectIssueController::update/$1/$2');
        $routes->post('(:num)/issues/(:num)/delete', 'Admin\AdminProjectIssueController::delete/$1/$2');

        // Project Impact Indicators Management Routes
        $routes->get('(:num)/indicators', 'Admin\AdminProjectIndicatorController::index/$1');
        $routes->get('(:num)/indicators/create', 'Admin\AdminProjectIndicatorController::create/$1');
        $routes->post('(:num)/indicators/create', 'Admin\AdminProjectIndicatorController::store/$1');
        $routes->get('(:num)/indicators/(:num)/edit', 'Admin\AdminProjectIndicatorController::edit/$1/$2');
        $routes->post('(:num)/indicators/(:num)/edit', 'Admin\AdminProjectIndicatorController::update/$1/$2');
        $routes->post('(:num)/indicators/(:num)/delete', 'Admin\AdminProjectIndicatorController::delete/$1/$2');

        // Project Risks Management Routes
        $routes->get('(:num)/risks', 'Admin\AdminProjectRisksController::index/$1');
        $routes->get('(:num)/risks/create', 'Admin\AdminProjectRisksController::create/$1');
        $routes->post('(:num)/risks/create', 'Admin\AdminProjectRisksController::store/$1');
        $routes->get('(:num)/risks/(:num)/edit', 'Admin\AdminProjectRisksController::edit/$1/$2');
        $routes->post('(:num)/risks/(:num)/edit', 'Admin\AdminProjectRisksController::update/$1/$2');
        $routes->post('(:num)/risks/(:num)/delete', 'Admin\AdminProjectRisksController::delete/$1/$2');

        // Project Documents Management Routes
        $routes->get('(:num)/documents', 'Admin\AdminProjectDocumentsController::index/$1');
        $routes->get('(:num)/documents/create', 'Admin\AdminProjectDocumentsController::create/$1');
        $routes->post('(:num)/documents/create', 'Admin\AdminProjectDocumentsController::store/$1');
        $routes->get('(:num)/documents/(:num)/edit', 'Admin\AdminProjectDocumentsController::edit/$1/$2');
        $routes->post('(:num)/documents/(:num)/edit', 'Admin\AdminProjectDocumentsController::update/$1/$2');
        $routes->post('(:num)/documents/(:num)/delete', 'Admin\AdminProjectDocumentsController::delete/$1/$2');
        $routes->get('(:num)/documents/(:num)/download', 'Admin\AdminProjectDocumentsController::download/$1/$2');

        // Project Officers Management Routes
        $routes->get('(:num)/officers', 'Admin\AdminProjectOfficerController::index/$1');
        $routes->get('(:num)/officers/create', 'Admin\AdminProjectOfficerController::create/$1');
        $routes->post('(:num)/officers/create', 'Admin\AdminProjectOfficerController::store/$1');
        $routes->post('(:num)/officers/(:num)/remove', 'Admin\AdminProjectOfficerController::remove/$1/$2');

        // Project Contractors Management Routes
        $routes->get('(:num)/contractors', 'Admin\AdminProjectContractorController::index/$1');
        $routes->get('(:num)/contractors/create', 'Admin\AdminProjectContractorController::create/$1');
        $routes->post('(:num)/contractors/create', 'Admin\AdminProjectContractorController::store/$1');
        $routes->post('(:num)/contractors/(:num)/remove', 'Admin\AdminProjectContractorController::remove/$1/$2');

        // Project Expenses Management Routes
        $routes->get('(:num)/expenses', 'Admin\AdminProjectExpenseController::index/$1');
        $routes->get('(:num)/expenses/create', 'Admin\AdminProjectExpenseController::create/$1');
        $routes->post('(:num)/expenses/create', 'Admin\AdminProjectExpenseController::store/$1');
        $routes->get('(:num)/expenses/(:num)', 'Admin\AdminProjectExpenseController::show/$1/$2');
        $routes->get('(:num)/expenses/(:num)/edit', 'Admin\AdminProjectExpenseController::edit/$1/$2');
        $routes->post('(:num)/expenses/(:num)/edit', 'Admin\AdminProjectExpenseController::update/$1/$2');
        $routes->post('(:num)/expenses/(:num)/delete', 'Admin\AdminProjectExpenseController::delete/$1/$2');
        $routes->get('(:num)/expenses/(:num)/download', 'Admin\AdminProjectExpenseController::download/$1/$2');

        // Project Events Management Routes
        $routes->get('(:num)/events', 'Admin\AdminProjectEventController::index/$1');
        $routes->get('(:num)/events/create', 'Admin\AdminProjectEventController::create/$1');
        $routes->post('(:num)/events/create', 'Admin\AdminProjectEventController::store/$1');
        $routes->get('(:num)/events/(:num)', 'Admin\AdminProjectEventController::show/$1/$2');
        $routes->get('(:num)/events/(:num)/edit', 'Admin\AdminProjectEventController::edit/$1/$2');
        $routes->post('(:num)/events/(:num)/edit', 'Admin\AdminProjectEventController::update/$1/$2');
        $routes->post('(:num)/events/(:num)/delete', 'Admin\AdminProjectEventController::delete/$1/$2');
        $routes->post('(:num)/events/(:num)/resolve', 'Admin\AdminProjectEventController::resolve/$1/$2');
    });

    // Contractor Management Routes
    $routes->group('contractors', ['filter' => 'admin_auth'], function($routes) {
        // List contractors - GET
        $routes->get('/', 'Admin\AdminContractorsController::index');

        // Create contractor - GET/POST
        $routes->get('create', 'Admin\AdminContractorsController::create');
        $routes->post('create', 'Admin\AdminContractorsController::store');

        // View contractor - GET
        $routes->get('(:num)', 'Admin\AdminContractorsController::show/$1');

        // Edit contractor - GET/POST
        $routes->get('(:num)/edit', 'Admin\AdminContractorsController::edit/$1');
        $routes->post('(:num)/edit', 'Admin\AdminContractorsController::update/$1');

        // Contractor sub-features (will be added later)
        // Documents, Services, Compliance, Assessments, Evaluations, Contacts
    });

    // Logout (redirect to main auth logout)
    $routes->get('logout', 'AuthController::logoutUser');
});

// Monitoring Portal Routes
$routes->group('monitoring', function($routes) {
    // Dashboard
    $routes->get('dashboard', 'Monitoring\MonitoringDashboardController::index', ['filter' => 'admin_auth']);
    $routes->get('/', 'Monitoring\MonitoringDashboardController::index', ['filter' => 'admin_auth']);

    // Logout (redirect to main auth logout)
    $routes->get('logout', 'AuthController::logoutUser');
});
