<?php

namespace App\Models;

/**
 * Project Expense Model
 * 
 * Handles project expenses with milestone tracking and file attachments.
 */
class ProjectExpenseModel extends BaseModel
{
    protected $table      = 'project_expenses';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'description', 'amount_paid', 'paid_on', 'file_path',
        'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'  => 'required|integer',
        'description' => 'required|max_length[255]',
        'amount_paid' => 'required|decimal',
        'paid_on'     => 'required|valid_date'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'description' => [
            'required' => 'Description is required'
        ],
        'amount_paid' => [
            'required' => 'Amount paid is required'
        ],
        'paid_on' => [
            'required' => 'Payment date is required'
        ]
    ];
    
    /**
     * Get expenses by project
     */
    public function getByProject(int $projectId): array
    {
        return $this->where('project_id', $projectId)
                   ->orderBy('paid_on', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get expenses by milestone
     */
    public function getByMilestone(int $milestoneId): array
    {
        return $this->where('milestone_id', $milestoneId)
                   ->orderBy('paid_on', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get expenses by date range
     */
    public function getByDateRange(string $startDate, string $endDate, ?int $projectId = null): array
    {
        $query = $this->where('paid_on >=', $startDate)
                     ->where('paid_on <=', $endDate);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('paid_on', 'DESC')->findAll();
    }
    
    /**
     * Get project total expenses
     */
    public function getProjectTotalExpenses(int $projectId): float
    {
        $result = $this->selectSum('amount_paid')
                      ->where('project_id', $projectId)
                      ->first();
        
        return (float) ($result['amount_paid'] ?? 0);
    }
    
    /**
     * Get milestone total expenses
     */
    public function getMilestoneTotalExpenses(int $milestoneId): float
    {
        $result = $this->selectSum('amount_paid')
                      ->where('milestone_id', $milestoneId)
                      ->first();
        
        return (float) ($result['amount_paid'] ?? 0);
    }
    
    /**
     * Get recent expenses
     */
    public function getRecentExpenses(int $days = 30, ?int $projectId = null): array
    {
        $dateThreshold = date('Y-m-d', strtotime("-{$days} days"));
        
        $query = $this->where('paid_on >=', $dateThreshold);
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('paid_on', 'DESC')->findAll();
    }
    
    /**
     * Get expense statistics
     */
    public function getExpenseStatistics(?int $projectId = null): array
    {
        $stats = [];
        
        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        // Total expenses
        $totalResult = $query->selectSum('amount_paid')->first();
        $stats['total_amount'] = (float) ($totalResult['amount_paid'] ?? 0);
        
        // Total count
        $totalQuery = $this;
        if ($projectId) {
            $totalQuery = $totalQuery->where('project_id', $projectId);
        }
        $stats['total_count'] = $totalQuery->countAllResults();
        
        // Average expense
        $stats['average_amount'] = $stats['total_count'] > 0 
            ? $stats['total_amount'] / $stats['total_count'] 
            : 0;
        
        // Monthly breakdown (last 12 months)
        $monthlyExpenses = $this->select("DATE_FORMAT(paid_on, '%Y-%m') as month, SUM(amount_paid) as total")
                               ->where('paid_on >=', date('Y-m-d', strtotime('-12 months')))
                               ->groupBy("DATE_FORMAT(paid_on, '%Y-%m')")
                               ->orderBy('month', 'ASC')
                               ->findAll();
        
        $stats['monthly_breakdown'] = $monthlyExpenses;
        
        return $stats;
    }
    
    /**
     * Get expenses with milestone info
     */
    public function getExpensesWithMilestone(int $projectId): array
    {
        return $this->select('project_expenses.*, project_milestones.title as milestone_title, project_milestones.milestone_code')
                   ->join('project_milestones', 'project_milestones.id = project_expenses.milestone_id', 'left')
                   ->where('project_expenses.project_id', $projectId)
                   ->orderBy('project_expenses.paid_on', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get largest expenses
     */
    public function getLargestExpenses(int $limit = 10, ?int $projectId = null): array
    {
        $query = $this;
        
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }
        
        return $query->orderBy('amount_paid', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
}
