<?php /** @var array $admin */ ?>
<?php /** @var array $stats */ ?>

<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations/' . $admin['organization_id'] . '/admins') ?>" class="btn btn-secondary">
    ← Back to Administrators
</a>
<a href="<?= base_url('dakoii/organizations/admins/' . $admin['id'] . '/edit') ?>" class="btn btn-primary">
    Edit Administrator
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success" style="margin-bottom: var(--spacing-xl);">
            <?= session()->getFlashdata('success') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <!-- Admin Profile Header -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-lg);">
            <div class="avatar-large">
                <?= strtoupper(substr($admin['name'], 0, 1)) ?>
            </div>
            
            <div style="flex: 1;">
                <h1 style="margin: 0; color: var(--text-primary);"><?= esc($admin['name']) ?></h1>
                <div style="color: var(--text-secondary); margin-top: var(--spacing-xs);">
                    <strong>Username:</strong> <?= esc($admin['username']) ?>
                </div>
                <div style="color: var(--text-secondary); margin-top: var(--spacing-xs);">
                    <strong>Email:</strong> <?= esc($admin['email']) ?>
                </div>
                <div style="margin-top: var(--spacing-sm);">
                    <span class="role-badge role-<?= $admin['role'] ?>">
                        <?= ucfirst($admin['role']) ?>
                    </span>
                    <span class="status-badge <?= $admin['is_activated'] ? 'status-active' : 'status-pending' ?>">
                        <?= $admin['is_activated'] ? 'Activated' : 'Pending Activation' ?>
                    </span>
                </div>
            </div>

            <div class="admin-actions">
                <button onclick="toggleAdminStatus(<?= $admin['id'] ?>)" class="btn btn-sm <?= $admin['is_activated'] ? 'btn-warning' : 'btn-success' ?>">
                    <?= $admin['is_activated'] ? 'Deactivate' : 'Activate' ?>
                </button>
                <button onclick="resetPassword(<?= $admin['id'] ?>)" class="btn btn-sm btn-secondary">
                    Reset Password
                </button>
                <button onclick="deleteAdmin(<?= $admin['id'] ?>)" class="btn btn-sm btn-danger">
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Admin Details -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl);">
        <!-- Main Information -->
        <div>
            <div class="card" style="margin-bottom: var(--spacing-xl);">
                <div class="card-header">Administrator Information</div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <label>User Code</label>
                        <value><?= esc($admin['user_code']) ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Username</label>
                        <value><?= esc($admin['username']) ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Full Name</label>
                        <value><?= esc($admin['name']) ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Email Address</label>
                        <value><?= esc($admin['email']) ?></value>
                    </div>
                    
                    <div class="info-item">
                        <label>Role</label>
                        <value>
                            <span class="role-badge role-<?= $admin['role'] ?>">
                                <?= ucfirst($admin['role']) ?>
                            </span>
                        </value>
                    </div>
                    
                    <div class="info-item">
                        <label>Status</label>
                        <value>
                            <span class="status-badge <?= $admin['is_activated'] ? 'status-active' : 'status-pending' ?>">
                                <?= $admin['is_activated'] ? 'Activated' : 'Pending Activation' ?>
                            </span>
                        </value>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div>
            <div class="card">
                <div class="card-header">Account Statistics</div>
                
                <div class="stats-list">
                    <div class="stat-item">
                        <label>Created Date</label>
                        <value><?= date('M j, Y', strtotime($stats['created_date'])) ?></value>
                    </div>
                    
                    <div class="stat-item">
                        <label>Last Login</label>
                        <value><?= $stats['last_login'] ? date('M j, Y g:i A', strtotime($stats['last_login'])) : 'Never' ?></value>
                    </div>
                    
                    <div class="stat-item">
                        <label>Activation Status</label>
                        <value><?= $stats['activation_status'] ?></value>
                    </div>
                    
                    <div class="stat-item">
                        <label>Role Level</label>
                        <value><?= $stats['role'] ?></value>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.admin-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item label {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

.info-item value {
    color: var(--text-primary);
    font-weight: 500;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.stat-item value {
    font-weight: 600;
    color: var(--text-primary);
}

.role-badge {
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.role-admin { background: rgba(255, 0, 110, 0.1); color: #FF006E; }
.role-moderator { background: rgba(255, 183, 0, 0.1); color: #FFB700; }
.role-editor { background: rgba(0, 255, 163, 0.1); color: #00FFA3; }
.role-user { background: rgba(128, 128, 128, 0.1); color: #808080; }

.status-badge {
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: var(--spacing-sm);
}

.status-active { background: rgba(0, 255, 163, 0.1); color: #00FFA3; }
.status-pending { background: rgba(255, 183, 0, 0.1); color: #FFB700; }

.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: rgba(0, 255, 163, 0.1);
    border-color: rgba(0, 255, 163, 0.3);
    color: #00FFA3;
}

.alert-error {
    background: rgba(255, 0, 110, 0.1);
    border-color: rgba(255, 0, 110, 0.3);
    color: #FF006E;
}
</style>

<script>
function toggleAdminStatus(adminId) {
    if (confirm('Are you sure you want to change this administrator\'s status?')) {
        fetch(`<?= base_url('dakoii/organizations/admins/') ?>${adminId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function resetPassword(adminId) {
    if (confirm('Are you sure you want to reset this administrator\'s password?')) {
        fetch(`<?= base_url('dakoii/organizations/admins/') ?>${adminId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}

function deleteAdmin(adminId) {
    if (confirm('Are you sure you want to delete this administrator? This action cannot be undone.')) {
        fetch(`<?= base_url('dakoii/organizations/admins/') ?>${adminId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '<?= base_url('dakoii/organizations/' . $admin['organization_id'] . '/admins') ?>';
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        });
    }
}
</script>
<?= $this->endSection() ?>
