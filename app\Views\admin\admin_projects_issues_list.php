<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#issues-addressed" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-plus-circle me-2"></i>
    Add Issue
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Issues Addressed
        </h1>
        <p class="text-muted mb-0">
            Problems and challenges addressed by project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Issue Statistics -->
<div class="row g-4 mb-4">
    <!-- Total Issues -->
    <div class="col-lg-4 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-exclamation-triangle text-primary"></i>
                </div>
                <div class="h4 fw-bold text-primary mb-2">
                    <?= $issueStats['total_issues'] ?>
                </div>
                <div class="text-muted small">Total Issues</div>
            </div>
        </div>
    </div>

    <!-- Direct Issues -->
    <div class="col-lg-4 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-bullseye text-success"></i>
                </div>
                <div class="h4 fw-bold text-success mb-2">
                    <?= $issueStats['by_type']['direct'] ?>
                </div>
                <div class="text-muted small">Direct Issues</div>
            </div>
        </div>
    </div>

    <!-- Indirect Issues -->
    <div class="col-lg-4 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-arrow-repeat text-warning"></i>
                </div>
                <div class="h4 fw-bold text-warning mb-2">
                    <?= $issueStats['by_type']['indirect'] ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Indirect Issues</div>
    </div>

    <!-- Impact Ratio -->
    <div class="card" style="text-align: center; padding: var(--spacing-lg);">
        <div style="font-size: 2rem; margin-bottom: var(--spacing-sm);">📊</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-warning); margin-bottom: var(--spacing-xs);">
            <?php 
            $total = $issueStats['total_issues'];
            $directRatio = $total > 0 ? round(($issueStats['by_type']['direct'] / $total) * 100) : 0;
            echo $directRatio . '%';
            ?>
        </div>
        <div style="color: var(--text-muted); font-size: 0.875rem;">Direct Impact</div>
    </div>
</div>

<!-- Issues by Type -->
<?php if ($issueStats['total_issues'] > 0): ?>
<div class="card mb-xl">
    <div class="card-header">
        📊 Impact Assessment Overview
    </div>
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl);">
            <!-- Direct Issues -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                    <div style="font-size: 1.5rem;">🎯</div>
                    <div>
                        <h3 style="color: var(--text-primary); margin: 0; font-size: 1.125rem;">Direct Issues</h3>
                        <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">Primary problems directly addressed</p>
                    </div>
                </div>
                <div style="font-size: 2rem; font-weight: 700; color: var(--brand-secondary);">
                    <?= $issueStats['by_type']['direct'] ?>
                </div>
            </div>

            <!-- Indirect Issues -->
            <div style="background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                    <div style="font-size: 1.5rem;">🔄</div>
                    <div>
                        <h3 style="color: var(--text-primary); margin: 0; font-size: 1.125rem;">Indirect Issues</h3>
                        <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">Secondary problems indirectly addressed</p>
                    </div>
                </div>
                <div style="font-size: 2rem; font-weight: 700; color: var(--brand-accent);">
                    <?= $issueStats['by_type']['indirect'] ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Issues Table -->
<div class="card">
    <div class="card-header">
        🎯 Issues Addressed
    </div>

    <?php if (!empty($issues)): ?>
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: var(--bg-tertiary); border-bottom: 1px solid var(--border-color);">
                        <th style="padding: var(--spacing-md); text-align: left; font-weight: 600; color: var(--text-primary);">
                            Issue Description
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Impact Type
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Created
                        </th>
                        <th style="padding: var(--spacing-md); text-align: center; font-weight: 600; color: var(--text-primary);">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($issues as $issue): ?>
                        <tr style="border-bottom: 1px solid var(--border-color);">
                            <td style="padding: var(--spacing-md);">
                                <div style="color: var(--text-primary); line-height: 1.5;">
                                    <?= esc($issue['description']) ?>
                                </div>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <?php if ($issue['issue_type'] === 'direct'): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        🎯 Direct
                                    </span>
                                <?php else: ?>
                                    <span style="background: var(--brand-accent); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        🔄 Indirect
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($issue['created_at'])) ?>
                                </span>
                            </td>
                            <td style="padding: var(--spacing-md); text-align: center;">
                                <div class="d-flex gap-md justify-content-center">
                                    <a
                                        href="<?= base_url('admin/projects/' . $project['id'] . '/issues/' . $issue['id'] . '/edit') ?>"
                                        class="btn btn-primary"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Edit Issue"
                                    >
                                        ✏️ Edit
                                    </a>
                                    <button
                                        onclick="showDeleteModal(<?= $issue['id'] ?>, '<?= esc(substr($issue['description'], 0, 50)) ?>...')"
                                        class="btn btn-danger"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Delete Issue"
                                    >
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🎯</div>
            <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Issues Addressed Yet</h3>
            <p style="margin-bottom: var(--spacing-lg);">Document the problems and challenges this project addresses for impact assessment.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/issues/create') ?>" class="btn btn-primary">
                🎯 Add First Issue
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; padding: var(--spacing-xl); border-radius: var(--radius-md); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Delete Issue</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to delete issue "<span id="deleteIssueText"></span>"?
            This action cannot be undone.
        </p>

        <form id="deleteForm" method="post" style="display: none;">
            <?= csrf_field() ?>
        </form>

        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideDeleteModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmDelete()" class="btn btn-danger">Delete Issue</button>
        </div>
    </div>
</div>

<script>
let currentDeleteIssueId = null;

function showDeleteModal(issueId, issueText) {
    currentDeleteIssueId = issueId;
    document.getElementById('deleteIssueText').textContent = issueText;
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    currentDeleteIssueId = null;
}

function confirmDelete() {
    if (currentDeleteIssueId) {
        const form = document.getElementById('deleteForm');
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/issues/') ?>' + currentDeleteIssueId + '/delete';
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
