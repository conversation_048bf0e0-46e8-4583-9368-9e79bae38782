<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<!-- Dashboard Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
            Welcome back, <?= esc(session()->get('admin_user_name')) ?>!
        </h1>
        <p class="text-muted mb-0">
            Organization Portal - Here's what's happening in your PROMIS system today.
        </p>
    </div>
    <div>
        <!-- Dashboard Controls -->
        <div class="d-flex gap-3 align-items-center">
            <!-- Theme Switcher -->
            <form action="<?= base_url('admin/theme/switch') ?>" method="post" class="d-inline-block">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                <select name="theme" onchange="this.form.submit()" class="form-select form-select-sm" style="width: auto;">
                    <option value="light" <?= (session()->get('admin_theme') === 'light' || !session()->get('admin_theme')) ? 'selected' : '' ?>>
                        <i class="bi bi-sun"></i> Light
                    </option>
                    <option value="dark" <?= (session()->get('admin_theme') === 'dark') ? 'selected' : '' ?>>
                        <i class="bi bi-moon"></i> Dark
                    </option>
                    <option value="auto" <?= (session()->get('admin_theme') === 'auto') ? 'selected' : '' ?>>
                        <i class="bi bi-circle-half"></i> Auto
                    </option>
                </select>
            </form>

            <!-- Dashboard Reset -->
            <form action="<?= base_url('admin/dashboard/reset') ?>" method="post" class="d-inline-block">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                <button type="submit" class="btn btn-outline-secondary btn-sm" onclick="return confirm('Reset dashboard to default layout?')" title="Reset Layout">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    Reset
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-5">

    <!-- Users Stats -->
    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="display-6 fw-bold text-primary mb-1">
                            <?= number_format($stats['total_users']) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Total Users
                        </p>
                        <p class="text-success mb-0 small mt-1">
                            <i class="bi bi-check-circle me-1"></i>
                            <?= number_format($stats['active_users']) ?> active
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 60px; height: 60px; background: var(--promis-gradient-primary);">
                        <i class="bi bi-people fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organizations Stats -->
    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="display-6 fw-bold text-primary mb-1">
                            <?= number_format($stats['total_organizations']) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Organizations
                        </p>
                        <p class="text-success mb-0 small mt-1">
                            <i class="bi bi-check-circle me-1"></i>
                            <?= number_format($stats['active_organizations']) ?> active
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 60px; height: 60px; background: var(--promis-gradient-secondary);">
                        <i class="bi bi-buildings fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Stats -->
    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="display-6 fw-bold text-primary mb-1">
                            <?= number_format($stats['total_projects']) ?>
                        </h3>
                        <p class="text-muted mb-0 small">
                            Projects
                        </p>
                        <p class="text-success mb-0 small mt-1">
                            <i class="bi bi-check-circle me-1"></i>
                            <?= number_format($stats['active_projects']) ?> active
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 60px; height: 60px; background: var(--promis-gradient-accent);">
                        <i class="bi bi-folder fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="col-lg-3 col-md-6">
        <div class="card promis-card-hover h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <h3 class="h5 fw-bold text-success mb-1">
                            Excellent
                        </h3>
                        <p class="text-muted mb-0 small">
                            System Health
                        </p>
                        <p class="text-muted mb-0 small mt-1">
                            <i class="bi bi-shield-check me-1"></i>
                            All systems operational
                        </p>
                    </div>
                    <div class="rounded-circle d-flex align-items-center justify-content-center text-white"
                         style="width: 60px; height: 60px; background: var(--promis-gradient-secondary);">
                        <i class="bi bi-check-circle fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div class="row g-4">

    <!-- Recent Activities -->
    <div class="col-lg-8">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity me-2"></i>
                    Recent Activities
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recent_activities)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="list-group-item d-flex align-items-center py-3">
                                <div class="rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center me-3"
                                     style="width: 40px; height: 40px;">
                                    <?php
                                    $icons = [
                                        'user' => 'bi-person',
                                        'project' => 'bi-folder',
                                        'organization' => 'bi-building'
                                    ];
                                    $iconClass = $icons[$activity['type']] ?? 'bi-clipboard';
                                    ?>
                                    <i class="bi <?= $iconClass ?> text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">
                                        <?= esc($activity['action']) ?>
                                    </h6>
                                    <p class="mb-0 text-muted small">
                                        <?= esc($activity['description']) ?>
                                    </p>
                                </div>
                                <small class="text-muted">
                                    <?= esc($activity['time']) ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <p class="text-muted mt-3">No recent activities to display.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="<?= base_url('admin/users/create') ?>" class="btn btn-primary promis-btn-gradient">
                        <i class="bi bi-person-plus me-2"></i>
                        Create New User
                    </a>
                    <a href="<?= base_url('admin/users') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-people me-2"></i>
                        Manage Users
                    </a>
                    <a href="<?= base_url('admin/audit') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-search me-2"></i>
                        View Audit Trail
                    </a>
                    <a href="<?= base_url('admin/search') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-globe me-2"></i>
                        Global Search
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card mt-4">
    <div class="card-header bg-light">
        <h5 class="card-title mb-0">
            <i class="bi bi-info-circle me-2"></i>
            System Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center me-3"
                         style="width: 40px; height: 40px;">
                        <i class="bi bi-person text-primary"></i>
                    </div>
                    <div>
                        <h6 class="mb-1 fw-semibold">Current User</h6>
                        <p class="mb-0 text-muted small">
                            <?= esc(session()->get('admin_user_name')) ?> (<?= esc(session()->get('admin_user_role')) ?>)
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-success bg-opacity-10 d-flex align-items-center justify-content-center me-3"
                         style="width: 40px; height: 40px;">
                        <i class="bi bi-building text-success"></i>
                    </div>
                    <div>
                        <h6 class="mb-1 fw-semibold">Organization</h6>
                        <p class="mb-0 text-muted small">
                            <?= esc(session()->get('admin_organization_name')) ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-success bg-opacity-10 d-flex align-items-center justify-content-center me-3"
                         style="width: 40px; height: 40px;">
                        <i class="bi bi-check-circle text-success"></i>
                    </div>
                    <div>
                        <h6 class="mb-1 fw-semibold">Session Status</h6>
                        <p class="mb-0 text-success small">
                            <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                            Active
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-info bg-opacity-10 d-flex align-items-center justify-content-center me-3"
                         style="width: 40px; height: 40px;">
                        <i class="bi bi-gear text-info"></i>
                    </div>
                    <div>
                        <h6 class="mb-1 fw-semibold">PROMIS Version</h6>
                        <p class="mb-0 text-muted small">
                            v2.0
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
