/* ================================================================
   1)  projects  (master record)
   ================================================================ */
CREATE TABLE projects (
    id                      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    org_id                  BIGINT UNSIGNED NOT NULL,          -- logical link only

    pro_code                VARCHAR(20)      NOT NULL UNIQUE,
    other_project_ids       TEXT             NULL,             -- x-refs to external systems

    title                   VARCHAR(200)     NOT NULL,
    goal                    TEXT             NULL,
    description             MEDIUMTEXT       NULL,

    /* Dates */
    initiation_date         DATE             NULL,
    start_date              DATE             NULL,
    end_date                DATE             NULL,

    /* Location */
    address_line            VARCHAR(200)     NULL,
    country_id              BIGINT UNSIGNED  NULL,
    province_id             BIGINT UNSIGNED  NULL,
    district_id             BIGINT UNSIGNED  NULL,
    llg_id                  BIGINT UNSIGNED  NULL,
    ward_name               VARCHAR(100)     NULL,
    village_name            VARCHAR(100)     NULL,
    gps_point               VARCHAR(20)      NULL,             -- text, not POINT
    gps_kml_path            VARCHAR(255)     NULL,

    /* Overall status & certification */
    status                  ENUM('planning','active','on-hold','completed','cancelled')
                                               DEFAULT 'planning',
    status_notes            VARCHAR(255)     NULL,
    status_at               DATETIME         NULL,
    status_by_id            BIGINT UNSIGNED  NULL,

    officer_certified       TINYINT(1)       DEFAULT 0,
    officer_cert_at         DATETIME         NULL,
    officer_cert_by         BIGINT UNSIGNED  NULL,

    contractor_certified    TINYINT(1)       DEFAULT 0,
    contractor_cert_at      DATETIME         NULL,
    contractor_cert_by      BIGINT UNSIGNED  NULL,

    evaluation_file         VARCHAR(255)     NULL,
    evaluation_notes        TEXT             NULL,
    evaluation_date         DATE             NULL,
    evaluation_by           BIGINT UNSIGNED  NULL,

    baseline_year           SMALLINT UNSIGNED NULL,
    target_year             SMALLINT UNSIGNED NULL,

    /* Soft-delete & audit */
    created_at              DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at              DATETIME         NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at              DATETIME         NULL,
    created_by              BIGINT UNSIGNED  NULL,
    updated_by              BIGINT UNSIGNED  NULL,
    deleted_by              BIGINT UNSIGNED  NULL,

    INDEX idx_org        (org_id),
    INDEX idx_status     (status),
    INDEX idx_soft       (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/* ================================================================
   2)  project_phases  (container for milestones)
       status limited to active | deactivated
   ================================================================ */
CREATE TABLE project_phases (
    id            BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id    BIGINT UNSIGNED NOT NULL,
    phase_code    VARCHAR(20)     NOT NULL,
    title         VARCHAR(150)    NOT NULL,
    description   TEXT            NULL,
    sort_order    SMALLINT UNSIGNED NOT NULL DEFAULT 1,
    start_date    DATE            NULL,
    end_date      DATE            NULL,
    status        ENUM('active','deactivated') NOT NULL DEFAULT 'active',

    created_at    DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    DATETIME        NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at    DATETIME        NULL,
    created_by    BIGINT UNSIGNED NULL,
    updated_by    BIGINT UNSIGNED NULL,
    deleted_by    BIGINT UNSIGNED NULL,

    INDEX idx_project (project_id),
    INDEX idx_sort    (project_id, sort_order),
    INDEX idx_soft    (deleted_at)
);

/* ================================================================
   3)  project_milestones  (detailed deliverables)
       unchanged since last spec
   ================================================================ */
CREATE TABLE project_milestones (
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id      BIGINT UNSIGNED NOT NULL,
    phase_id        BIGINT UNSIGNED NOT NULL,
    milestone_code  VARCHAR(20)     NOT NULL,
    title           VARCHAR(200)    NOT NULL,
    description     TEXT            NULL,
    target_date     DATE            NULL,
    status          ENUM('pending','in-progress','completed','approved') DEFAULT 'pending',
    completion_date DATE            NULL,

    evidence_count  SMALLINT UNSIGNED DEFAULT 0,

    created_at      DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME        NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at      DATETIME        NULL,
    created_by      BIGINT UNSIGNED NULL,
    updated_by      BIGINT UNSIGNED NULL,
    deleted_by      BIGINT UNSIGNED NULL,

    INDEX idx_phase   (phase_id),
    INDEX idx_project (project_id),
    INDEX idx_status  (status),
    INDEX idx_soft    (deleted_at)
);

/* ================================================================
   4)  project_contractors  (many-to-many with history)
   ================================================================ */
CREATE TABLE project_contractors (
    id               BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id       BIGINT UNSIGNED NOT NULL,
    contractor_id    BIGINT UNSIGNED NOT NULL,
    joined_at        DATE            NOT NULL,
    is_active        TINYINT(1)      NOT NULL DEFAULT 1,
    removal_reason   TEXT            NULL,
    client_flag      ENUM('positive','neutral','negative') DEFAULT 'neutral',

    created_at       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at       DATETIME        NULL,
    created_by       BIGINT UNSIGNED NULL,
    deleted_by       BIGINT UNSIGNED NULL,

    INDEX idx_project    (project_id),
    INDEX idx_contractor (project_id, contractor_id),
    INDEX idx_active     (is_active),
    INDEX idx_soft       (deleted_at)
);

/* ================================================================
   5)  project_officers  (many-to-many with roles)
       duplicates allowed
   ================================================================ */
CREATE TABLE project_officers (
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id     BIGINT UNSIGNED NOT NULL,
    user_id        BIGINT UNSIGNED NOT NULL,
    role           ENUM('lead','certifier','support') NOT NULL,
    is_active      TINYINT(1)      NOT NULL DEFAULT 1,
    removal_reason TEXT            NULL,

    created_at     DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at     DATETIME        NULL,
    created_by     BIGINT UNSIGNED NULL,
    deleted_by     BIGINT UNSIGNED NULL,

    INDEX idx_project_user (project_id, user_id),
    INDEX idx_role         (role),
    INDEX idx_soft         (deleted_at)
);

/* ================================================================
   6)  project_budget_items
   ================================================================ */
CREATE TABLE project_budget_items (
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id      BIGINT UNSIGNED NOT NULL,
    item_code       VARCHAR(30)     NOT NULL,
    description     VARCHAR(255)    NOT NULL,
    amount_planned  DECIMAL(15,2)   NOT NULL,
    status          ENUM('active','removed') DEFAULT 'active',

    created_at      DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME        NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at      DATETIME        NULL,
    created_by      BIGINT UNSIGNED NULL,
    updated_by      BIGINT UNSIGNED NULL,
    deleted_by      BIGINT UNSIGNED NULL,

    INDEX idx_project (project_id),
    INDEX idx_status  (status),
    INDEX idx_soft    (deleted_at)
);

/* ================================================================
   7)  project_expenses
   ================================================================ */
CREATE TABLE project_expenses (
    id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id     BIGINT UNSIGNED NOT NULL,
    milestone_id   BIGINT UNSIGNED NULL,
    description    VARCHAR(255)    NOT NULL,
    amount_paid    DECIMAL(15,2)   NOT NULL,
    paid_on        DATE            NOT NULL,
    file_path      VARCHAR(255)    NULL,

    created_at     DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at     DATETIME        NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at     DATETIME        NULL,
    created_by     BIGINT UNSIGNED NULL,
    updated_by     BIGINT UNSIGNED NULL,
    deleted_by     BIGINT UNSIGNED NULL,

    INDEX idx_project   (project_id),
    INDEX idx_milestone (project_id, milestone_id),
    INDEX idx_soft      (deleted_at)
);

/* ================================================================
   8)  project_outcomes
   ================================================================ */
CREATE TABLE project_outcomes (
    id            BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id    BIGINT UNSIGNED NOT NULL,
    outcome_text  VARCHAR(255)     NOT NULL,
    quantity      DECIMAL(12,2)    NOT NULL DEFAULT 1,
    unit          VARCHAR(50)      NULL,

    created_at    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    DATETIME         NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at    DATETIME         NULL,
    created_by    BIGINT UNSIGNED  NULL,
    updated_by    BIGINT UNSIGNED  NULL,
    deleted_by    BIGINT UNSIGNED  NULL,

    INDEX idx_project (project_id),
    INDEX idx_soft    (deleted_at)
);

/* ================================================================
   9)  project_issues_addressed
   ================================================================ */
CREATE TABLE project_issues_addressed (
    id            BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id    BIGINT UNSIGNED NOT NULL,
    issue_type    ENUM('direct','indirect') NOT NULL,
    description   TEXT             NOT NULL,

    created_at    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    DATETIME         NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at    DATETIME         NULL,
    created_by    BIGINT UNSIGNED  NULL,
    updated_by    BIGINT UNSIGNED  NULL,
    deleted_by    BIGINT UNSIGNED  NULL,

    INDEX idx_project (project_id),
    INDEX idx_soft    (deleted_at)
);

/* ================================================================
  10)  project_impact_indicators
   ================================================================ */
CREATE TABLE project_impact_indicators (
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id      BIGINT UNSIGNED NOT NULL,
    indicator_text  VARCHAR(255)   NOT NULL,
    baseline_value  DECIMAL(15,4)  NULL,
    baseline_date   DATE           NULL,
    target_value    DECIMAL(15,4)  NULL,
    target_date     DATE           NULL,
    actual_value    DECIMAL(15,4)  NULL,
    actual_date     DATE           NULL,

    created_at      DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME       NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at      DATETIME       NULL,
    created_by      BIGINT UNSIGNED NULL,
    updated_by      BIGINT UNSIGNED NULL,
    deleted_by      BIGINT UNSIGNED NULL,

    INDEX idx_project (project_id),
    INDEX idx_soft    (deleted_at)
);

/* ================================================================
  11)  project_documents  (version-able)
   ================================================================ */
CREATE TABLE project_documents (
    id            BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id    BIGINT UNSIGNED NOT NULL,
    milestone_id  BIGINT UNSIGNED NULL,
    doc_path      VARCHAR(255)     NOT NULL,
    doc_type      VARCHAR(50)      NULL,
    description   VARCHAR(255)     NULL,
    version_no    SMALLINT UNSIGNED DEFAULT 1,

    created_at    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at    DATETIME         NULL,
    created_by    BIGINT UNSIGNED  NULL,
    deleted_by    BIGINT UNSIGNED  NULL,

    INDEX idx_project    (project_id),
    INDEX idx_milestone  (project_id, milestone_id),
    INDEX idx_version    (project_id, version_no),
    INDEX idx_soft       (deleted_at)
);

/* ================================================================
  12)  project_risks
   ================================================================ */
CREATE TABLE project_risks (
    id              BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id      BIGINT UNSIGNED NOT NULL,
    milestone_id    BIGINT UNSIGNED NULL,
    risk_type       ENUM('proposed','foreseen','witnessed') NOT NULL,
    risk_level      ENUM('low','medium','high','critical')  NOT NULL,
    description     TEXT            NOT NULL,
    mitigation      TEXT            NULL,
    approval_status ENUM('pending','approved','rejected')   DEFAULT 'pending',

    created_at      DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME        NULL ON UPDATE CURRENT_TIMESTAMP,
    deleted_at      DATETIME        NULL,
    created_by      BIGINT UNSIGNED NULL,
    updated_by      BIGINT UNSIGNED NULL,
    deleted_by      BIGINT UNSIGNED NULL,

    INDEX idx_project (project_id),
    INDEX idx_status  (approval_status),
    INDEX idx_soft    (deleted_at)
);
