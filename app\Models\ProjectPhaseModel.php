<?php

namespace App\Models;

/**
 * Project Phase Model
 * 
 * Handles project phases which serve as containers for milestones.
 * Each phase has a specific order and can be active or deactivated.
 */
class ProjectPhaseModel extends BaseModel
{
    protected $table      = 'project_phases';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'phase_code', 'title', 'description', 'sort_order',
        'start_date', 'end_date', 'status', 'created_by', 'updated_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'  => 'required|integer',
        'phase_code'  => 'required|max_length[20]',
        'title'       => 'required|max_length[150]',
        'sort_order'  => 'required|integer',
        'status'      => 'in_list[active,deactivated]'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'phase_code' => [
            'required' => 'Phase code is required'
        ],
        'title' => [
            'required' => 'Phase title is required'
        ]
    ];
    
    /**
     * Get phases by project
     */
    public function getByProject(int $projectId, bool $activeOnly = true): array
    {
        $query = $this->where('project_id', $projectId);
        
        if ($activeOnly) {
            $query = $query->where('status', 'active');
        }
        
        return $query->orderBy('sort_order', 'ASC')->findAll();
    }
    
    /**
     * Get active phases
     */
    public function getActivePhases(): array
    {
        return $this->where('status', 'active')
                   ->orderBy('project_id', 'ASC')
                   ->orderBy('sort_order', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get phase with milestones
     */
    public function getPhaseWithMilestones(int $phaseId): ?array
    {
        $phase = $this->find($phaseId);
        
        if (!$phase) {
            return null;
        }
        
        // Load milestones
        $milestoneModel = new ProjectMilestoneModel();
        $phase['milestones'] = $milestoneModel->getByPhase($phaseId);
        
        return $phase;
    }
    
    /**
     * Reorder phases
     */
    public function reorderPhases(int $projectId, array $phaseOrders): bool
    {
        $this->db->transStart();
        
        foreach ($phaseOrders as $phaseId => $sortOrder) {
            $this->update($phaseId, [
                'sort_order' => $sortOrder,
                'updated_by' => session()->get('admin_user_id')
            ]);
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }
    
    /**
     * Activate/Deactivate phase
     */
    public function updateStatus(int $phaseId, string $status): bool
    {
        return $this->update($phaseId, [
            'status' => $status,
            'updated_by' => session()->get('admin_user_id')
        ]);
    }
    
    /**
     * Get next sort order for project
     */
    public function getNextSortOrder(int $projectId): int
    {
        $maxOrder = $this->where('project_id', $projectId)
                        ->selectMax('sort_order')
                        ->first();
        
        return ($maxOrder['sort_order'] ?? 0) + 1;
    }
    
    /**
     * Get phase statistics
     */
    public function getPhaseStatistics(int $projectId): array
    {
        $stats = [];
        
        // Count by status
        $statusCounts = $this->select('status, COUNT(*) as count')
                            ->where('project_id', $projectId)
                            ->groupBy('status')
                            ->findAll();
        
        $stats['by_status'] = array_column($statusCounts, 'count', 'status');
        $stats['total'] = $this->where('project_id', $projectId)->countAllResults();
        
        return $stats;
    }
    
    /**
     * Check if phase can be deleted
     */
    public function canDelete(int $phaseId): bool
    {
        $milestoneModel = new ProjectMilestoneModel();
        $milestoneCount = $milestoneModel->where('phase_id', $phaseId)->countAllResults();
        
        return $milestoneCount === 0;
    }
    
    /**
     * Get phases with milestone counts
     */
    public function getPhasesWithMilestoneCounts(int $projectId): array
    {
        $phases = $this->getByProject($projectId, false);
        
        $milestoneModel = new ProjectMilestoneModel();
        
        foreach ($phases as &$phase) {
            $phase['milestone_count'] = $milestoneModel->where('phase_id', $phase['id'])
                                                     ->countAllResults();
        }
        
        return $phases;
    }
}
