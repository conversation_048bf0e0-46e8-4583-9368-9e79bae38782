Dakoii Portal System Writeup
The Dakoii Portal is a super admin interface within the PROMIS system, designed exclusively for developers or system owners to manage high-level configurations. It serves as the central hub for overseeing organizations, their administrators, government structures, and Dakoii system users. The portal is built using CodeIgniter 4, employs a modular programming approach with RESTful API-like interfaces, and prioritizes performance optimization for reliable operation. Below is a detailed description of the portal and its features, structured by module and feature, with clear explanations of functionality, navigation, and relationships to other system components.

Portal Name: Dakoii Portal
System Overview

Purpose: The Dakoii Portal is a super admin interface accessible only to developers or system owners. It is responsible for managing organizations, organization administrators, government structures, and Dakoii system users.
Access: The portal is accessed via base_url()/dakoii, which displays the dedicated login form.
Development Approach: Utilizes modular programming for code reuse and RESTful methods for API-like interfaces. POST and GET functions are separated to maintain clean code structure.
Interface: Features a dark theme derived from the main system theme, ensuring a consistent and professional appearance with clear navigation.
System Don'ts: Do not combine POST and GET functions in the same method. Avoid AJAX for CRUD operations, favoring standard CodeIgniter 4 form submissions.


Module Name: Organizations Management
Feature Name: Organizations Management
Feature PurposeThe Organizations Management feature enables CRUD (Create, Read, Update, Delete) operations for organizations within the PROMIS system. It is foundational for the Dakoii Portal, as organizations are the primary entities that use the Admin and Project Monitoring Portals. This feature ensures that system owners can configure and maintain organizational profiles, which are linked to organization administrators and projects managed in other portals.
How it works

Create: The system auto-generates a 5-digit unique code for each organization. Required fields are the unique code and organization name. Default settings include a paid license status and activated status. The creation form is a standard page, not a modal.
Read: Displays a list of organizations with details such as name, unique code, license status, and status. Each organization can be viewed as a profile page, showing additional details like description, logo, contact details, HQ GPS coordinates, website, social pages, and up to five representative images.
Update: Updates are performed via modals for specific actions, such as uploading a logo or wallpaper background image. License status changes (paid/unpaid) and status toggles (activated/deactivated) are also handled via modals. The five representative images are added post-creation through a dedicated interface.
Delete: Implements soft deletion, marking organizations as deleted without removing them from the database.
Relationship: Organizations are prerequisites for creating organization administrators (via the Organizations Administrators Management feature). They are also linked to projects managed in the Admin Portal and monitored in the Project Monitoring Portal.

Navigation

Dashboard → Organizations List → Add Organization/View Organization
From the dashboard, users access the organizations list, where they can click "Add Organization" to create a new one or select an organization to view its profile.

Specific Behavior

After creating an organization, the user is redirected to the View Organization page, which serves as the organization’s profile page, displaying all details and related actions.
Standard CodeIgniter 4 form submissions are used for all CRUD operations.

The Don'ts

Do not use AJAX for CRUD operations or form submissions.
Do not use modals for the initial organization creation form.

Interface

Extends the Dakoii Portal’s dark theme template.
Features clear navigation with a list view for organizations and a detailed profile view for individual organizations.
Modals are used for updates (e.g., logo upload, license status change) to maintain a streamlined interface.


Module Name: Organizations Administrators Management
Feature Name: Organizations Administrators Management
Feature PurposeThis feature manages CRUD operations for organization administrators, who are responsible for overseeing their respective organizations in the Admin Portal. It ensures that each organization has designated admin users with appropriate access rights, facilitating the link between the Dakoii Portal and the Admin Portal’s project management functions.
How it works

Create: An alphanumeric unique ID is auto-generated for each administrator. Required fields include unique ID, name, and email. Upon creation, an activation link is emailed to the administrator. Clicking the link activates the account, and a 4-digit temporary password is sent via email, which the user can change after logging in. The creation form is a standard page, not a modal.
Read: Lists all administrators for a selected organization, showing details like name, email, status, and contact information. Each administrator has a profile page displaying additional details, such as ID photo and wallpaper background image.
Update: Modals are used for uploading ID photos, resetting passwords, and toggling status (activated/deactivated). If an administrator is not activated, a “Send Activation Link” button is displayed on their profile.
Delete: Implements soft deletion to preserve historical data.
Relationship: Administrators are tied to specific organizations created in the Organizations Management feature. They access the Admin Portal to manage projects, contractors, and users, linking the Dakoii Portal’s super admin functions to operational management.

Navigation

Dashboard → Organizations List → View Organization → Administrators List → View Admin Profile
Users navigate to an organization’s profile from the dashboard, access the administrators list, and then view or add an administrator.

Specific Behavior

After creating an administrator, the user is redirected to the View Admin Profile page, which serves as the administrator’s profile page.
Standard CodeIgniter 4 form submissions handle all CRUD operations.

The Don'ts

Do not use AJAX for CRUD operations or form submissions.
Do not use modals for the initial administrator creation form.

Interface

Extends the Dakoii Portal’s dark theme template.
Features a clear navigational structure with a list view for administrators within an organization and a detailed profile view for each administrator.
Modals are used for updates (e.g., ID photo upload, password reset) to enhance usability.


Module Name: Government Structure Management
Feature Name: Government Structure Management
Feature PurposeThis feature manages the hierarchical government structure (Country, Province, District, LLG) using a parent-child relationship approach. It supports CRUD operations to configure geographical and administrative divisions, which are critical for mapping projects and organizations to specific locations in the Admin and Project Monitoring Portals.
How it works

Create: Users select a JSON ID from a dropdown populated by a map GeoJSON file. Required fields include name, level (country, province, district, LLG), and parent ID (optional for top-level items like countries). The code is manually entered. Creation can use a modal for a streamlined experience.
Read: Displays a hierarchical list of government structure items, organized by level. Each item includes details like name, code, level, parent ID, flag filepath, map center coordinates, map zoom level, and status.
Update: Modals are used for updating item details, such as name, code, or map settings.
Delete: Implements soft deletion to maintain data integrity.
Relationship: Government structures provide the geographical context for projects in the Admin Portal (e.g., project locations) and Project Monitoring Portal (e.g., GPS coordinates and KML files). They are also used to associate organizations with specific regions.

Navigation

Dashboard → Gov Structure Country → Province List → District List → LLG List
Users navigate through the hierarchy, starting from the country level, to view or manage lower-level structures.

Specific Behavior

After creating a government structure item, the user is redirected back to the list view for the corresponding level.
A hierarchy chart is displayed below the list, showing parent-child relationships (including great-grandchildren). Users can click on chart elements (e.g., a district) to view the list of its children (e.g., LLGs).
Standard CodeIgniter 4 form submissions are used for all CRUD operations.

The Don'ts

Do not use AJAX for CRUD operations or form submissions.

Interface

Extends the Dakoii Portal’s dark theme template.
Features clear navigation with a hierarchical list view and an interactive hierarchy chart for visualizing relationships.
Modals are used for creation and updates to maintain a clean interface.


Module Name: Dakoii Users Management
Feature Name: Dakoii Users Management
Feature PurposeThis feature manages CRUD operations for Dakoii Portal users, who are distinct from organization administrators and project officers. These users are typically developers or system owners with super admin access, ensuring secure and controlled management of the portal itself.
How it works

Create: An alphanumeric unique code is auto-generated for each user. Required fields include unique ID, username, name, and email. Upon creation, an activation link is emailed to the user. Clicking the link activates the account, and a 4-digit temporary password is sent via email, which the user can change after logging in. The creation form is a standard page, not a modal.
Read: Lists all Dakoii users with details like username, email, role (admin, moderator, user), name, and status. Each user has a profile page showing additional details, such as ID photo.
Update: Modals are used for uploading ID photos, resetting passwords, and toggling status (activated/deactivated). If a user is not activated, a “Send Activation Link” button is displayed on their profile.
Delete: Implements soft deletion to preserve historical data.
Relationship: Dakoii users are responsible for managing the Dakoii Portal, which oversees organizations and government structures that cascade to the Admin and Project Monitoring Portals.

Navigation

Dashboard → Dakoii Users List → View User
Users access the Dakoii users list from the dashboard and can view or add users.

Specific Behavior

After creating a user, the user is redirected back to the Dakoii Users List page.
Standard CodeIgniter 4 form submissions handle all CRUD operations.

The Don'ts

Do not use AJAX for CRUD operations or form submissions.
Do not use modals for the initial user creation form.

Interface

Extends the Dakoii Portal’s dark theme template.
Features a clear navigational structure with a list view for users and a detailed profile view for each user.
Modals are used for updates (e.g., ID photo upload, password reset) to enhance usability.


Technical Implementation

Framework: CodeIgniter 4
Frontend: Custom dark theme template, extending the main system theme.
Database: MySQL (assumed for standard CodeIgniter setup).
API: RESTful endpoints for data operations, ensuring modular and reusable code.
Form Submissions: Standard CodeIgniter 4 form handling, no AJAX.
Security: Argon2 password hashing, email-based activation links, and soft deletion for data integrity.
Performance: Optimized for reliability, with minimal resource usage to support super admin operations.

Role-Based Access Control

Dakoii Users: Exclusive access to the Dakoii Portal with roles (admin, moderator, user) determining specific permissions.
Relationship to Other Portals: Dakoii Portal users configure organizations and administrators, who then manage projects and users in the Admin Portal. Government structures defined here provide location data for projects monitored in the Project Monitoring Portal.

Navigation Flow

Login: Access via base_url()/dakoii → Login form → Dashboard.
Dashboard: Central hub with links to:
Organizations List → Add/View Organization
Gov Structure Country → Province/District/LLG Lists
Dakoii Users List → Add/View User
Organization → Administrators List → Add/View Admin Profile



Interface Design

Theme: Dark theme derived from the main system theme.
Layout: Clear navigation with list views, profile pages, and modals for updates.
Responsive: Optimized for desktop access, with a focus on usability for super admins.
Visuals: Hierarchy charts for government structures, profile pages for organizations and users, and modals for streamlined updates.

Relationship to Other Portals

Admin Portal: Organizations and their administrators created in the Dakoii Portal access the Admin Portal to manage projects, contractors, and users. Government structures provide location data for project details.
Project Monitoring Portal: Project officers in the Project Monitoring Portal rely on organizations and government structures configured in the Dakoii Portal to monitor projects in specific locations, using read-only preset data from the Admin Portal.

This comprehensive writeup outlines the Dakoii Portal’s role as the super admin interface within the PROMIS system, detailing its features, navigation, and integration with other portals to ensure seamless management of organizations, administrators, government structures, and system users.
