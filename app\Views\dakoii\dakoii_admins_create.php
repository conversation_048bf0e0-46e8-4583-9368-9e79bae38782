<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins') ?>" class="btn btn-secondary">
    ← Back to Administrators
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Organization Header -->
    <div class="card" style="margin-bottom: var(--spacing-xl);">
        <div style="display: flex; align-items: center; gap: var(--spacing-lg);">
            <?php if ($organization['logo_path']): ?>
                <img src="<?= base_url($organization['logo_path']) ?>" alt="Logo" style="width: 48px; height: 48px; border-radius: var(--radius-lg); object-fit: cover;">
            <?php else: ?>
                <div style="width: 48px; height: 48px; border-radius: var(--radius-lg); background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                    <?= strtoupper(substr($organization['name'], 0, 1)) ?>
                </div>
            <?php endif; ?>
            <div>
                <h2 style="margin: 0; color: var(--text-primary);">Create Administrator</h2>
                <div style="color: var(--text-secondary); margin-top: var(--spacing-xs);">
                    For: <?= esc($organization['name']) ?> (<?= esc($organization['org_code']) ?>)
                </div>
            </div>
        </div>
    </div>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <?= session()->getFlashdata('error') ?>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-error" style="margin-bottom: var(--spacing-xl);">
            <ul style="margin: 0; padding-left: 20px;">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <form action="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins/create') ?>" method="post" id="createAdminForm">
        <?= csrf_field() ?>
        
        <!-- Basic Information -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Administrator Information</div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                <div class="form-group">
                    <label for="username" class="form-label">Username *</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        placeholder="Enter username"
                        value="<?= old('username') ?>"
                        required
                        maxlength="50"
                        pattern="[a-zA-Z0-9_]+"
                        title="Username can only contain letters, numbers, and underscores"
                    >
                    <div class="form-help">Unique username for login (letters, numbers, underscore only)</div>
                </div>
                
                <div class="form-group">
                    <label for="admin_code_display" class="form-label">Admin Code</label>
                    <input 
                        type="text" 
                        id="admin_code_display" 
                        class="form-input" 
                        placeholder="Auto-generated"
                        disabled
                        style="background: var(--glass-bg); color: var(--text-muted);"
                    >
                    <div class="form-help">8-10 character alphanumeric code will be auto-generated</div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                <div class="form-group">
                    <label for="name" class="form-label">Full Name *</label>
                    <input 
                        type="text" 
                        id="name" 
                        name="name" 
                        class="form-input" 
                        placeholder="Enter full name"
                        value="<?= old('name') ?>"
                        required
                        maxlength="100"
                    >
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="<EMAIL>"
                        value="<?= old('email') ?>"
                        required
                        maxlength="100"
                    >
                    <div class="form-help">Activation email will be sent to this address</div>
                </div>
            </div>

            <div class="form-group">
                <label for="role" class="form-label">Role *</label>
                <select id="role" name="role" class="form-input" required>
                    <option value="">Select a role</option>
                    <option value="admin" <?= old('role') === 'admin' ? 'selected' : '' ?>>Administrator</option>
                    <option value="moderator" <?= old('role') === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                    <option value="user" <?= old('role') === 'user' ? 'selected' : '' ?>>User</option>
                </select>
                <div class="form-help">
                    <strong>Administrator:</strong> Full access to organization management<br>
                    <strong>Moderator:</strong> Limited management capabilities<br>
                    <strong>User:</strong> Basic access and viewing permissions
                </div>
            </div>
        </div>

        <!-- Account Setup Information -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Account Setup</div>
            
            <div style="background: var(--glass-bg); border: 1px solid var(--glass-border); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                <div style="display: flex; align-items: center; margin-bottom: var(--spacing-md);">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; margin-right: var(--spacing-md);">
                        📧
                    </div>
                    <div>
                        <div style="font-weight: 600; color: var(--text-primary);">Email Activation Process</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Automated account setup workflow</div>
                    </div>
                </div>
                
                <div style="color: var(--text-secondary); font-size: 0.875rem; line-height: 1.6;">
                    <ol style="margin: 0; padding-left: 20px;">
                        <li>Administrator account will be created with a unique admin code</li>
                        <li><strong>Step 1:</strong> Activation email will be sent to the provided email address</li>
                        <li><strong>Step 2:</strong> Administrator clicks activation link to activate account</li>
                        <li><strong>Step 3:</strong> After activation, temporary 4-digit password will be sent via email</li>
                        <li><strong>Step 4:</strong> Administrator can log in and must change password on first login</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-header">Administrator Preview</div>
            
            <div id="adminPreview" style="display: none;">
                <div style="display: flex; align-items: center; gap: var(--spacing-lg); padding: var(--spacing-lg); background: var(--glass-bg); border-radius: var(--radius-md);">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 700; font-size: 1.5rem;" id="previewAvatar">
                        ?
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: var(--text-primary); font-size: 1.125rem;" id="previewName">-</div>
                        <div style="color: var(--text-secondary); margin-top: var(--spacing-xs);" id="previewEmail">-</div>
                        <div style="margin-top: var(--spacing-sm);">
                            <span class="role-badge" id="previewRole">-</span>
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 0.75rem; color: var(--text-tertiary); margin-bottom: var(--spacing-xs);">Username</div>
                        <div style="font-family: var(--font-mono); font-weight: 600; color: var(--text-primary);" id="previewUsername">-</div>
                    </div>
                </div>
            </div>
            
            <div id="previewPlaceholder" style="text-align: center; padding: var(--spacing-2xl); color: var(--text-tertiary);">
                Fill in the form above to see a preview of the administrator profile
            </div>
        </div>

        <!-- Submit Buttons -->
        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
            <a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/admins') ?>" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <span id="submitText">Create Administrator</span>
                <span id="submitLoader" class="loading" style="display: none;"></span>
            </button>
        </div>
    </form>
</div>

<style>
.form-help {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
    line-height: 1.4;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid;
    backdrop-filter: blur(10px);
}

.alert-error {
    background: rgba(255, 0, 110, 0.1);
    border-color: rgba(255, 0, 110, 0.3);
    color: #FF006E;
}

.role-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-admin {
    background: rgba(255, 0, 110, 0.1);
    color: #FF006E;
}

.role-moderator {
    background: rgba(131, 56, 236, 0.1);
    color: #8338EC;
}

.role-user {
    background: rgba(0, 212, 255, 0.1);
    color: #00D4FF;
}

.form-input:focus {
    transform: translateY(-1px);
}

select.form-input {
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createAdminForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitLoader = document.getElementById('submitLoader');

    // Form elements
    const nameInput = document.getElementById('name');
    const usernameInput = document.getElementById('username');
    const emailInput = document.getElementById('email');
    const roleSelect = document.getElementById('role');

    // Preview elements
    const adminPreview = document.getElementById('adminPreview');
    const previewPlaceholder = document.getElementById('previewPlaceholder');
    const previewAvatar = document.getElementById('previewAvatar');
    const previewName = document.getElementById('previewName');
    const previewEmail = document.getElementById('previewEmail');
    const previewUsername = document.getElementById('previewUsername');
    const previewRole = document.getElementById('previewRole');

    // Update preview when form changes
    function updatePreview() {
        const name = nameInput.value.trim();
        const username = usernameInput.value.trim();
        const email = emailInput.value.trim();
        const role = roleSelect.value;

        if (name || username || email || role) {
            adminPreview.style.display = 'block';
            previewPlaceholder.style.display = 'none';

            previewAvatar.textContent = name ? name.charAt(0).toUpperCase() : '?';
            previewName.textContent = name || '-';
            previewEmail.textContent = email || '-';
            previewUsername.textContent = username || '-';
            
            if (role) {
                previewRole.textContent = role.charAt(0).toUpperCase() + role.slice(1);
                previewRole.className = 'role-badge role-' + role;
            } else {
                previewRole.textContent = '-';
                previewRole.className = 'role-badge';
            }
        } else {
            adminPreview.style.display = 'none';
            previewPlaceholder.style.display = 'block';
        }
    }

    // Add event listeners for preview updates
    [nameInput, usernameInput, emailInput, roleSelect].forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        submitText.style.display = 'none';
        submitLoader.style.display = 'inline-block';
        
        // Re-enable after 30 seconds as fallback
        setTimeout(function() {
            submitBtn.disabled = false;
            submitText.style.display = 'inline';
            submitLoader.style.display = 'none';
        }, 30000);
    });

    // Auto-focus on username field
    usernameInput.focus();

    // Username validation
    usernameInput.addEventListener('input', function() {
        this.value = this.value.replace(/[^a-zA-Z0-9_]/g, '');
    });
});
</script>
<?= $this->endSection() ?>
