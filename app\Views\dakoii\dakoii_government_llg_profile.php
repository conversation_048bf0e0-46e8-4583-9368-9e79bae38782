<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to LLGs
</a>
<a href="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/edit') ?>" class="btn btn-primary">
    <i class="icon">✏️</i> Edit LLG
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Success Message -->
    <?php if (session()->getFlashdata('success')): ?>
        <div style="margin-bottom: var(--spacing-lg); padding: var(--spacing-md); background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: var(--radius-md);">
            <div style="color: #28a745; font-weight: 500;">
                ✅ <?= esc(session()->getFlashdata('success')) ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($llg)): ?>
        <!-- Page Header -->
        <div style="margin-bottom: var(--spacing-xl);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-md);">
                <div style="width: 80px; height: 80px; background: var(--gradient-primary); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; font-size: 2.5rem;">
                    🏛️
                </div>
                <div style="flex: 1;">
                    <h1 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 2.5rem; font-weight: 700;">
                        <?= esc($llg['name']) ?>
                    </h1>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-xs);">
                        <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(0, 212, 255, 0.1); color: var(--accent-primary); border-radius: var(--radius-sm); font-size: 0.875rem; font-weight: 500;">
                            Code: <?= esc($llg['llg_code']) ?>
                        </span>
                        <?php if (isset($llg['llg_type']) && !empty($llg['llg_type'])): ?>
                        <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(255, 193, 7, 0.1); color: #ffc107; border-radius: var(--radius-sm); font-size: 0.875rem; font-weight: 500;">
                            <?= ucfirst(esc($llg['llg_type'])) ?> LLG
                        </span>
                        <?php endif; ?>
                    </div>
                    <p style="margin: 0; color: var(--text-secondary); font-size: 1rem;">
                        Local Level Government • Lowest administrative division
                    </p>
                </div>
            </div>
        </div>

        <!-- Information Grid -->
        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
            <!-- Basic Information Card -->
            <div class="card">
                <div class="card-header">
                    <h2 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                        <span>📋</span> Basic Information
                    </h2>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg);">
                    <div class="info-item">
                        <div class="info-label">LLG Name</div>
                        <div class="info-value"><?= esc($llg['name']) ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">LLG Code</div>
                        <div class="info-value"><?= esc($llg['llg_code']) ?></div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">LLG Type</div>
                        <div class="info-value">
                            <?php if (isset($llg['llg_type']) && !empty($llg['llg_type'])): ?>
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(255, 193, 7, 0.1); color: #ffc107; border-radius: var(--radius-sm); font-size: 0.875rem; font-weight: 500;">
                                    <?= ucfirst(esc($llg['llg_type'])) ?>
                                </span>
                            <?php else: ?>
                                <span style="color: var(--text-tertiary); font-style: italic;">Not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">GeoJSON ID</div>
                        <div class="info-value">
                            <?php if (isset($llg['geojson_id']) && !empty($llg['geojson_id'])): ?>
                                <?= esc($llg['geojson_id']) ?>
                            <?php else: ?>
                                <span style="color: var(--text-tertiary); font-style: italic;">Not set</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                        <span>⚡</span> Quick Actions
                    </h3>
                </div>
                
                <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                    <a href="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/edit') ?>" class="btn btn-primary" style="justify-content: flex-start;">
                        <i class="icon">✏️</i> Edit LLG Information
                    </a>
                    
                    <?php if (isset($district)): ?>
                    <a href="<?= base_url('dakoii/government/districts/'.$district['id']) ?>" class="btn btn-secondary" style="justify-content: flex-start;">
                        <i class="icon">🏢</i> View Parent District
                    </a>
                    <?php endif; ?>
                    
                    <a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-secondary" style="justify-content: flex-start;">
                        <i class="icon">📋</i> View All LLGs
                    </a>
                    
                    <hr style="border: none; border-top: 1px solid var(--glass-border); margin: var(--spacing-sm) 0;">
                    
                    <form method="POST" action="<?= base_url('dakoii/government/llgs/'.$llg['id'].'/delete') ?>" style="margin: 0;">
                        <?= csrf_field() ?>
                        <button type="submit" class="btn" 
                                style="width: 100%; background: #dc3545; color: white; justify-content: flex-start;"
                                onclick="return confirm('Are you sure you want to delete this LLG? This action cannot be undone.')">
                            <i class="icon">🗑️</i> Delete LLG
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Hierarchy and Geographic Information -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); margin-bottom: var(--spacing-xl);">
            <!-- Administrative Hierarchy Card -->
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                        <span>🏛️</span> Administrative Hierarchy
                    </h3>
                </div>
                
                <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                    <?php if (isset($country)): ?>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.02); border-radius: var(--radius-md);">
                        <div style="width: 40px; height: 40px; background: var(--gradient-primary); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">🌍</div>
                        <div style="flex: 1;">
                            <div style="color: var(--text-secondary); font-size: 0.875rem;">Country</div>
                            <div style="color: var(--text-primary); font-weight: 500;">
                                <a href="<?= base_url('dakoii/government/countries/'.$country['id']) ?>" style="color: var(--accent-primary); text-decoration: none;">
                                    <?= esc($country['name']) ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($province)): ?>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.02); border-radius: var(--radius-md);">
                        <div style="width: 40px; height: 40px; background: var(--gradient-secondary); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">🏞️</div>
                        <div style="flex: 1;">
                            <div style="color: var(--text-secondary); font-size: 0.875rem;">Province</div>
                            <div style="color: var(--text-primary); font-weight: 500;">
                                <a href="<?= base_url('dakoii/government/provinces/'.$province['id']) ?>" style="color: var(--accent-primary); text-decoration: none;">
                                    <?= esc($province['name']) ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($district)): ?>
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(255, 255, 255, 0.02); border-radius: var(--radius-md);">
                        <div style="width: 40px; height: 40px; background: var(--gradient-accent); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">🏢</div>
                        <div style="flex: 1;">
                            <div style="color: var(--text-secondary); font-size: 0.875rem;">District</div>
                            <div style="color: var(--text-primary); font-weight: 500;">
                                <a href="<?= base_url('dakoii/government/districts/'.$district['id']) ?>" style="color: var(--accent-primary); text-decoration: none;">
                                    <?= esc($district['name']) ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div style="display: flex; align-items: center; gap: var(--spacing-md); padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border-radius: var(--radius-md); border: 1px solid rgba(0, 212, 255, 0.3);">
                        <div style="width: 40px; height: 40px; background: rgba(0, 212, 255, 0.2); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">🏛️</div>
                        <div style="flex: 1;">
                            <div style="color: var(--text-secondary); font-size: 0.875rem;">Local Level Government</div>
                            <div style="color: var(--accent-primary); font-weight: 600;"><?= esc($llg['name']) ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Geographic Information Card -->
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                        <span>🗺️</span> Geographic Information
                    </h3>
                </div>
                
                <div style="display: flex; flex-direction: column; gap: var(--spacing-lg);">
                    <div class="info-item">
                        <div class="info-label">Map Center Coordinates</div>
                        <div class="info-value">
                            <?php if (isset($llg['map_centre_lat']) && isset($llg['map_centre_lng']) && !empty($llg['map_centre_lat']) && !empty($llg['map_centre_lng'])): ?>
                                <div style="font-family: monospace; padding: var(--spacing-xs) var(--spacing-sm); background: rgba(255, 255, 255, 0.05); border-radius: var(--radius-sm);">
                                    <?= esc($llg['map_centre_lat']) ?>, <?= esc($llg['map_centre_lng']) ?>
                                </div>
                                <small style="color: var(--text-tertiary); font-size: 0.8rem;">Latitude, Longitude</small>
                            <?php else: ?>
                                <span style="color: var(--text-tertiary); font-style: italic;">Not set</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Default Map Zoom</div>
                        <div class="info-value">
                            <?php if (isset($llg['map_zoom']) && !empty($llg['map_zoom'])): ?>
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(255, 255, 255, 0.05); border-radius: var(--radius-sm); font-family: monospace;">
                                    Level <?= esc($llg['map_zoom']) ?>
                                </span>
                            <?php else: ?>
                                <span style="color: var(--text-tertiary); font-style: italic;">Not set</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Map Boundary Data</div>
                        <div class="info-value">
                            <?php if (isset($llg['geojson_id']) && !empty($llg['geojson_id'])): ?>
                                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                    <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(40, 167, 69, 0.1); color: #28a745; border-radius: var(--radius-sm); font-size: 0.875rem;">
                                        ✅ Available
                                    </span>
                                    <span style="font-family: monospace; font-size: 0.875rem;"><?= esc($llg['geojson_id']) ?></span>
                                </div>
                            <?php else: ?>
                                <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(220, 53, 69, 0.1); color: #dc3545; border-radius: var(--radius-sm); font-size: 0.875rem;">
                                    ❌ Not linked
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information Card -->
        <div class="card">
            <div class="card-header">
                <h3 style="margin: 0; display: flex; align-items: center; gap: var(--spacing-sm);">
                    <span>🔧</span> System Information
                </h3>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">
                <div class="info-item">
                    <div class="info-label">Record ID</div>
                    <div class="info-value" style="font-family: monospace;">#<?= esc($llg['id']) ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Created</div>
                    <div class="info-value">
                        <?php if (isset($llg['created_at'])): ?>
                            <?= date('M j, Y \a\t g:i A', strtotime($llg['created_at'])) ?>
                        <?php else: ?>
                            <span style="color: var(--text-tertiary);">Unknown</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Last Updated</div>
                    <div class="info-value">
                        <?php if (isset($llg['updated_at'])): ?>
                            <?= date('M j, Y \a\t g:i A', strtotime($llg['updated_at'])) ?>
                        <?php else: ?>
                            <span style="color: var(--text-tertiary);">Never</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        <span style="padding: var(--spacing-xs) var(--spacing-sm); background: rgba(40, 167, 69, 0.1); color: #28a745; border-radius: var(--radius-sm); font-size: 0.875rem; font-weight: 500;">
                            ✅ Active
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="info-card glass-effect">
            <div style="padding: var(--spacing-md); background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--radius-md);">
                <div style="display: flex; gap: var(--spacing-sm); align-items: flex-start;">
                    <div style="font-size: 1.25rem;">💡</div>
                    <div>
                        <h4 style="margin: 0 0 var(--spacing-xs) 0; color: var(--text-primary); font-size: 0.9rem;">About Local Level Governments</h4>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.85rem;">
                            LLGs are the lowest level of government administration, responsible for local community services, development projects, and grassroots governance within their designated areas.
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- LLG Not Found -->
        <div class="card" style="text-align: center; padding: var(--spacing-xl);">
            <div style="font-size: 4rem; margin-bottom: var(--spacing-lg);">🚫</div>
            <h2 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary);">LLG Not Found</h2>
            <p style="margin: 0 0 var(--spacing-lg) 0; color: var(--text-secondary);">
                The requested Local Level Government could not be found or may have been deleted.
            </p>
            <a href="<?= base_url('dakoii/government/llgs') ?>" class="btn btn-primary">
                <i class="icon">←</i> Back to LLG List
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 400;
}

.card-header h2,
.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

.btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?= $this->endSection() ?> 