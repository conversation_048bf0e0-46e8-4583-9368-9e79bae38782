<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
    ← Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit User
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Update user information for <strong><?= esc($user['name']) ?></strong>
        </p>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header">
        User Information
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <form action="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>" method="post" class="user-edit-form">

            <?= csrf_field() ?>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                
                <!-- Username (Read-only) -->
                <div class="form-group">
                    <label for="username" class="form-label">Username</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        value="<?= esc($user['username']) ?>"
                        readonly
                        style="background-color: var(--bg-muted); cursor: not-allowed;"
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Username cannot be changed
                    </small>
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        value="<?= old('email', $user['email']) ?>"
                        placeholder="Enter email address"
                        required
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Must be a valid email address
                    </small>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">

                <!-- Full Name -->
                <div class="form-group">
                    <label for="name" class="form-label">Full Name *</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input"
                        value="<?= old('name', $user['name']) ?>"
                        placeholder="Enter full name"
                        required
                    >
                </div>

                <!-- Role -->
                <div class="form-group">
                    <label for="role" class="form-label">Role *</label>
                    <select
                        id="role"
                        name="role"
                        class="form-input"
                        required
                    >
                        <option value="">Select Role</option>
                        <option value="moderator" <?= old('role', $user['role']) === 'moderator' ? 'selected' : '' ?>>Moderator</option>
                        <option value="editor" <?= old('role', $user['role']) === 'editor' ? 'selected' : '' ?>>Editor</option>
                        <option value="user" <?= old('role', $user['role']) === 'user' ? 'selected' : '' ?>>User</option>
                    </select>
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Administrator accounts cannot be edited
                    </small>
                </div>
            </div>

            <!-- Organization (Read-only) -->
            <div class="form-group">
                <label for="organization" class="form-label">Organization</label>
                <input
                    type="text"
                    id="organization"
                    name="organization"
                    class="form-input"
                    value="<?= esc($admin_organization_name) ?>"
                    readonly
                    style="background-color: var(--bg-muted); cursor: not-allowed;"
                >
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Organization assignment cannot be changed
                </small>
            </div>

            <!-- Additional Permissions -->
            <div class="form-group">
                <label class="form-label">Additional Permissions</label>
                <div style="background: var(--surface-card); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                    <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
                        Select additional permissions for this user. These are in addition to their role permissions.
                    </p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">

                        <!-- Project Officer Permission -->
                        <div class="permission-option">
                            <label style="display: flex; align-items: center; gap: var(--spacing-sm); cursor: pointer; padding: var(--spacing-md); border: 1px solid var(--border-color); border-radius: var(--radius-md); transition: all 0.3s ease;">
                                <input
                                    type="checkbox"
                                    id="is_project_officer"
                                    name="is_project_officer"
                                    value="1"
                                    <?= old('is_project_officer', $user['is_project_officer']) ? 'checked' : '' ?>
                                    style="margin: 0;"
                                >
                                <div>
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        📋 Project Officer
                                    </div>
                                    <div style="color: var(--text-secondary); font-size: 0.75rem;">
                                        Can manage project activities and reports
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Evaluator Permission -->
                        <div class="permission-option">
                            <label style="display: flex; align-items: center; gap: var(--spacing-sm); cursor: pointer; padding: var(--spacing-md); border: 1px solid var(--border-color); border-radius: var(--radius-md); transition: all 0.3s ease;">
                                <input
                                    type="checkbox"
                                    id="is_evaluator"
                                    name="is_evaluator"
                                    value="1"
                                    <?= old('is_evaluator', $user['is_evaluator']) ? 'checked' : '' ?>
                                    style="margin: 0;"
                                >
                                <div>
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        📊 Evaluator
                                    </div>
                                    <div style="color: var(--text-secondary); font-size: 0.75rem;">
                                        Can evaluate projects and submit assessments
                                    </div>
                                </div>
                            </label>
                        </div>

                        <!-- Supervisor Permission -->
                        <div class="permission-option">
                            <label style="display: flex; align-items: center; gap: var(--spacing-sm); cursor: pointer; padding: var(--spacing-md); border: 1px solid var(--border-color); border-radius: var(--radius-md); transition: all 0.3s ease;">
                                <input
                                    type="checkbox"
                                    id="is_supervisor"
                                    name="is_supervisor"
                                    value="1"
                                    <?= old('is_supervisor', $user['is_supervisor']) ? 'checked' : '' ?>
                                    style="margin: 0;"
                                >
                                <div>
                                    <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        👥 Supervisor
                                    </div>
                                    <div style="color: var(--text-secondary); font-size: 0.75rem;">
                                        Can supervise teams and approve activities
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
                <small style="color: var(--text-muted); font-size: 0.75rem; margin-top: var(--spacing-sm); display: block;">
                    These permissions are in addition to the selected role
                </small>
            </div>

            <!-- Information Box -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin: var(--spacing-lg) 0;">
                <h4 style="color: var(--brand-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    ℹ️ Edit Information
                </h4>
                <ul style="color: var(--text-secondary); font-size: 0.875rem; margin: 0; padding-left: var(--spacing-lg);">
                    <li>Username and organization cannot be changed after creation</li>
                    <li>Email changes may require the user to verify their new email</li>
                    <li>Role changes take effect immediately</li>
                    <li>All changes are logged for security auditing</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center" style="margin-top: var(--spacing-xl);">
                <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                
                <button type="submit" class="btn btn-primary">
                    💾 Update User
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Form Validation Styling -->
<style>
.user-edit-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-edit-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.user-edit-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Permission Options Styling */
.permission-option label:hover {
    border-color: var(--brand-primary) !important;
    background: var(--bg-accent) !important;
}

.permission-option input[type="checkbox"]:checked + div {
    color: var(--brand-primary);
}

.permission-option input[type="checkbox"]:checked {
    accent-color: var(--brand-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .user-edit-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .d-flex.justify-content-between .btn {
        width: 100%;
        text-align: center;
    }
}
</style>

<?= $this->endSection() ?>
