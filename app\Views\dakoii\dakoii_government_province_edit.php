<?= $this->extend('templates/dakoii_portal_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('dakoii/government/provinces/' . $province['id']) ?>" class="btn btn-secondary">
    <i class="icon">←</i> Back to Profile
</a>
<a href="<?= base_url('dakoii/government/provinces') ?>" class="btn btn-secondary">
    <i class="icon">🏘️</i> All Provinces
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Page Header -->
    <div style="margin-bottom: var(--spacing-xl);">
        <h1 style="color: var(--text-primary); margin-bottom: var(--spacing-sm); font-size: 2rem; display: flex; align-items: center; gap: var(--spacing-md);">
            <span style="font-size: 2.5rem;">🏞️</span>
            Edit Province: <?= esc($province['name']) ?>
        </h1>
        <p style="color: var(--text-secondary); font-size: 1rem;">
            Update the administrative information for this province.
        </p>
    </div>

    <!-- Edit Province Form -->
    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl); max-width: 1200px; margin: 0 auto;">
        <div class="card">
            <div class="card-header">Province Information</div>
            <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">Modify the details for the province.</p>

            <form method="POST" action="<?= base_url('dakoii/government/provinces/'.$province['id'].'/edit') ?>" class="province-form">
                <?= csrf_field() ?>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Basic Information</h3>

                    <div class="form-group">
                        <label for="country_id" class="form-label">Country *</label>
                        <select id="country_id" name="country_id" class="form-input" required>
                            <option value="">Select Country</option>
                            <?php if (!empty($countries)): ?>
                                <?php foreach ($countries as $country): ?>
                                    <option value="<?= $country['id'] ?>" <?= ($province['country_id'] == $country['id']) ? 'selected' : '' ?>>
                                        <?= esc($country['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="name" class="form-label">Province Name *</label>
                        <input type="text" id="name" name="name" class="form-input" value="<?= esc($province['name']) ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="prov_code" class="form-label">Province Code *</label>
                        <input type="text" id="prov_code" name="prov_code" class="form-input" value="<?= esc($province['prov_code']) ?>" required>
                    </div>
                </div>

                <!-- Geographic Information -->
                <div class="form-section">
                    <h3 style="margin: 0 0 var(--spacing-md) 0; color: var(--text-primary); font-size: 1.2rem;">Geographic Information</h3>
                    
                    <div class="form-group">
                        <label for="geojson_id" class="form-label">GeoJSON ID</label>
                        <select id="geojson_id" name="geojson_id" class="form-input"
                                style="background: rgba(26, 26, 46, 0.8); color: #ffffff; border: 1px solid rgba(255, 255, 255, 0.1);"
                                onchange="this.style.color='#ffffff'"
                                onfocus="this.style.color='#ffffff'"
                                onblur="this.style.color='#ffffff'">
                            <option value="">Select Province Boundary (Optional)</option>
                            <?php
                            // Load province options from JSON
                            $jsonPath = FCPATH . 'map_jsons/png_prov_boundaries_2011.json';
                            if (file_exists($jsonPath)) {
                                $jsonData = json_decode(file_get_contents($jsonPath), true);
                                if (isset($jsonData['features'])) {
                                    $provinces_json = [];
                                    foreach ($jsonData['features'] as $feature) {
                                        if (isset($feature['properties']['PROVID']) && isset($feature['properties']['PROVNAME'])) {
                                            $provinces_json[] = [
                                                'id' => $feature['properties']['PROVID'],
                                                'name' => $feature['properties']['PROVNAME']
                                            ];
                                        }
                                    }
                                    // Sort by name
                                    usort($provinces_json, function($a, $b) {
                                        return strcmp($a['name'], $b['name']);
                                    });

                                    foreach ($provinces_json as $prov_option) {
                                        $selected = (($province['geojson_id'] ?? null) == $prov_option['id']) ? 'selected' : '';
                                        echo '<option value="' . esc($prov_option['id']) . '" ' . $selected . ' style="background: #1a1a2e !important; color: #ffffff !important; padding: 8px 12px !important;">' . esc($prov_option['name']) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <small style="margin-top: var(--spacing-xs); font-size: 0.85rem; color: var(--text-secondary);">
                            Optional: Select the corresponding boundary from map data
                        </small>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md);">
                        <div class="form-group">
                            <label for="map_centre_gps" class="form-label">Map Center GPS</label>
                            <input type="text" id="map_centre_gps" name="map_centre_gps" class="form-input" value="<?= esc($province['map_centre_gps'] ?? '') ?>">
                        </div>
                        <div class="form-group">
                            <label for="map_zoom" class="form-label">Map Zoom Level</label>
                            <input type="number" id="map_zoom" name="map_zoom" class="form-input" value="<?= esc($province['map_zoom'] ?? '') ?>" step="1">
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">💾 Update Province</button>
                </div>
            </form>
        </div>

        <!-- Side Panel -->
        <div style="display: flex; flex-direction: column; gap: var(--spacing-xl);">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header">💡 Help</div>
                <ul style="list-style: none; padding: 0; margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                    <li style="padding: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <strong>Country:</strong> The parent country for this province.
                    </li>
                    <li style="padding: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <strong>Province Name:</strong> The official full name.
                    </li>
                    <li style="padding: var(--spacing-md); border-bottom: 1px solid var(--glass-border);">
                        <strong>Province Code:</strong> A short, unique code.
                    </li>
                    <li style="padding: var(--spacing-md);">
                        <strong>GeoJSON ID:</strong> Optional link to a map boundary shape.
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?> 