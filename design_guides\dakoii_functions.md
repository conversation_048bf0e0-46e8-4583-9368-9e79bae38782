Below is a comprehensive function-level checklist for the first build of the **Dakoii Portal**.
Every function honours the project rules (pure REST style in CodeIgniter 4, POST & GET in separate methods, standard form submissions, soft-delete, dark theme, etc.) drawn from the attached specifications.

---

### 1 · Core authentication & session

| Function name        | Purpose                            | List of tasks                                                                                                                                                                |
| -------------------- | ---------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **showLoginForm**    | Render `/dakoii` login page        | – Load dark theme view  <br>– Inject CSRF token                                                                                                                              |
| **authenticateUser** | Verify credentials & start session | – Validate email + password  <br>– Fetch user by `username/email`  <br>– Verify Argon2id hash  <br>– Set session, regenerate ID  <br>– Redirect to dashboard or return error |
| **logoutUser**       | End current session                | – Destroy session  <br>– Redirect to `/dakoii`                                                                                                                               |

---

### 2 · Dashboard

| Function name         | Purpose                                | List of tasks                                                                                           |
| --------------------- | -------------------------------------- | ------------------------------------------------------------------------------------------------------- |
| **getDashboardStats** | Provide quick counts & recent activity | – Query totals (organisations, admins, gov units, Dakoii users)  <br>– Return dataset to dashboard view |

---

### 3 · Organisations Management

| Function name                       | Purpose                         | List of tasks                                                                                                                                    |
| ----------------------------------- | ------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------ |
| **listOrganisations**               | Show paginated list             | – Pull non-deleted records  <br>– Apply search / filter  <br>– Paginate                                                                          |
| **showCreateOrganisationForm**      | Display creation screen (GET)   | – Pre-populate defaults (`license=paid`, `is_active=1`)                                                                                          |
| **createOrganisation**              | Persist new organisation (POST) | – Validate required fields  <br>– **generateOrganisationCode** (5-digit unique)  <br>– Insert row  <br>– Redirect to **viewOrganisationProfile** |
| **generateOrganisationCode**        | Produce unique 5-digit code     | – Loop until `org_code` unused                                                                                                                   |
| **viewOrganisationProfile**         | Detailed profile view           | – Join with images table  <br>– Render logo, wallpaper, 5 images                                                                                 |
| **showEditOrganisationModal**       | Generic modal loader            | – Fetch record  <br>– Return partial view (logo, wallpaper, license toggle, etc.)                                                                |
| **updateOrganisation**              | Apply updates from modal        | – Process file upload if present  <br>– Validate changed fields  <br>– Update DB timestamps/audit                                                |
| **uploadOrganisationImages**        | Add / reorder up-to-5 images    | – Validate max = 5  <br>– Store files, write `organization_images`  <br>– Handle `sort_order`                                                    |
| **toggleOrganisationStatus**        | Activate/Deactivate             | – Flip `is_active` flag                                                                                                                          |
| **changeOrganisationLicenseStatus** | Paid ↔ Unpaid                   | – Flip `license_status` enum                                                                                                                     |
| **softDeleteOrganisation**          | Mark deleted                    | – Set `deleted_at`, `deleted_by`                                                                                                                 |

---

### 4 · Organisation Administrators Management

| Function name                | Purpose                         | List of tasks                                                                                                      |
| ---------------------------- | ------------------------------- | ------------------------------------------------------------------------------------------------------------------ |
| **listOrgAdmins**            | Show admins for an organisation | – Filter by `organization_id`                                                                                      |
| **showCreateAdminForm**      | Admin creation screen           | – Display required fields (`admin_code`, name, email)                                                              |
| **createOrgAdmin**           | Persist new admin               | – **generateAdminCode** (alphanumeric)  <br>– Insert row; set `is_activated=0`  <br>– **sendAdminActivationEmail** |
| **generateAdminCode**        | Unique admin ID                 | – Random alphanumeric 8–10                                                                                         |
| **viewOrgAdminProfile**      | Profile page                    | – Load admin + organisation                                                                                        |
| **showEditAdminModal**       | Load ID-photo or status modal   | – Return partial view                                                                                              |
| **updateOrgAdmin**           | Save modal edits                | – Upload ID photo if given  <br>– Handle status toggle                                                             |
| **sendAdminActivationEmail** | Deliver activation link         | – Compose email w/ token  <br>– Store `activation_token`                                                           |
| **completeAdminActivation**  | Handle link click               | – Validate token  <br>– Flag `is_activated=1`  <br>– **emailTempPassword** (4-digit)                               |
| **resetAdminPassword**       | Manual reset                    | – Generate & email new temp pwd                                                                                    |
| **toggleAdminStatus**        | Activate/Deactivate             | – Flip flag                                                                                                        |
| **softDeleteOrgAdmin**       | Soft delete                     | – Stamp `deleted_at/by`                                                                                            |

---

### 5 · Government Structure Management (Country → Province → District → LLG)

| Function name                     | Purpose                  | List of tasks                                                                                    |
| --------------------------------- | ------------------------ | ------------------------------------------------------------------------------------------------ |
| **listGovUnits(level, parentId)** | Hierarchical listing     | – Query level table  <br>– Return collection                                                     |
| **showCreateGovUnitModal**        | Display create modal     | – Load GeoJSON dropdown (json\_id)                                                               |
| **createGovUnit**                 | Persist new unit         | – Validate `name`, `level`, `code`  <br>– Insert into correct table  <br>– Redirect back to list |
| **viewGovUnit**                   | Single unit view         | – Show details + map defaults                                                                    |
| **showEditGovUnitModal**          | Display edit modal       | – Fetch record                                                                                   |
| **updateGovUnit**                 | Apply edits              | – Validate & save                                                                                |
| **toggleGovUnitStatus**           | Activate/Deactivate      | – Flip flag                                                                                      |
| **softDeleteGovUnit**             | Soft delete              | – Stamp `deleted_at/by`                                                                          |
| **generateHierarchyChart**        | Build parent–child chart | – Recursively fetch descendants  <br>– Return JSON for JS chart                                  |
| **loadGeoJSONIDDropdown**         | Populate ID list         | – Read GeoJSON file server-side  <br>– Return `<select>` options                                 |

---

### 6 · Dakoii Users Management

| Function name                | Purpose                 | List of tasks                                                                                            |
| ---------------------------- | ----------------------- | -------------------------------------------------------------------------------------------------------- |
| **listDakoiiUsers**          | Paginated list          | – Query `dakoii_users`                                                                                   |
| **showCreateDakoiiUserForm** | Display user creation   | – Fields: username, email, name                                                                          |
| **createDakoiiUser**         | Persist new super-admin | – **generateUserCode** (alphanum)  <br>– `role` default = user  <br>– Insert row & send activation email |
| **generateUserCode**         | Unique code generator   | – Random 10–12 chars                                                                                     |
| **viewDakoiiUserProfile**    | Profile page            | – Load user, role, login history                                                                         |
| **showEditDakoiiUserModal**  | Modal loader            | – ID-photo / status / reset pwd                                                                          |
| **updateDakoiiUser**         | Save modal edits        | – Handle photo upload or status                                                                          |
| **sendUserActivationEmail**  | Email activation link   | – Store token, send mail                                                                                 |
| **completeUserActivation**   | Process link            | – Validate token, activate, **emailTempPassword**                                                        |
| **resetDakoiiUserPassword**  | Manual reset            | – Generate & email temp pwd                                                                              |
| **toggleDakoiiUserStatus**   | Activate/Deactivate     | – Flip `is_activated`                                                                                    |
| **softDeleteDakoiiUser**     | Soft delete             | – Stamp `deleted_at/by`                                                                                  |

---

### 7 · Shared utility helpers

| Function name                                          | Purpose                        | List of tasks                                                   |
| ------------------------------------------------------ | ------------------------------ | --------------------------------------------------------------- |
| **emailTempPassword(recipient, tempPwd)**              | Send 4-digit temp password     | – Render template  <br>– Send via mailer                        |
| **hashPasswordArgon2(password)**                       | Secure hashing                 | – Use PHP `password_hash()` Argon2id                            |
| **verifyPassword(password, hash)**                     | Login helper                   | – Use `password_verify()`                                       |
| **generateUniqueCode(length, charset)**                | General ID helper              | – Random+collision check                                        |
| **handleFileUpload(field, path)**                      | Centralised upload logic       | – Validate size/type  <br>– Move file  <br>– Return stored path |
| **softDelete(table, id, userId)**                      | Generic soft delete            | – Set `deleted_at`, `deleted_by`                                |
| **recordAuditTrail(userId, action, entity, entityId)** | Track changes                  | – Insert into audit table (future)                              |
| **paginate(queryBuilder, perPage)**                    | Shared paginator               | – Return CI Pager instance                                      |
| **sendEmail(to, subject, view, data)**                 | Wrapper around CI4 email class | – Configure SMTP  <br>– Render view                             |

---

These functions collectively cover **all CRUD flows, activation/password cycles, status toggles, file uploads, code generation and hierarchy visualisation** required for the first release of the Dakoii Portal.
